<ResponseField name="total" type="Int" >Total number of transactions that meets the requirements</ResponseField>
<ResponseField name="transactions" type="object[]" >
  <Expandable title="object">
    <ResponseField name="cobo_id" type="String" >Unique transaction ID</ResponseField>
    <ResponseField name="request_id" type="String" >Transaction request ID; it can be left empty if the transaction is a deposit</ResponseField>
    <ResponseField name="status" type="Int" >
    | Status Type | Code |
    | ----------- | ----------- |
    | PENDING_APPROVAL| 101|
    | QUEUED| 201|
    | PENDING_SIGNATURE| 301|
    | BROADCASTING| 401|
    | BROADCAST_FAILED| 402|
    | PENDING_CONFIRMATION| 403|
    | CONFIRMATION| 501|
    | REVERTING| 502|
    | SUCCESS| 900|
    | FAILED| 901|
    | REORG| 902|

    <Tip>The 402 status is not the final status of the transaction and will be rebroadcast, but there will still be broadcast failures.</Tip>
    <Tip>The 403 status indicates that the transaction has been successfully broadcast but not yet confirmed, while the 501 status indicates that the transaction has been successfully confirmed on the blockchain and is awaiting further confirmations.</Tip>
    <Tip>The 502 status indicates that during the confirmation process on the blockchain, the transaction execution has failed. Once the required number of confirmations is reached, the transaction status will change to 901.</Tip>
    <Tip>The 902 status indicates that the blockchain has undergone a reorganization and the transaction is no longer part of the confirmed chain, the transaction may either get confirmed again in a later block or fail altogether. The 902 status currently only applies to deposit transactions. If you have credited the funds after the transaction status reached 501, please implement the appropriate logic to handle potential rollbacks after the 902 status.</Tip>
    </ResponseField>
    <ResponseField name="coin_detail" type="object" >
        <Expandable title="object">
            <ResponseField name="coin" type="String" >Coin code</ResponseField>
            <ResponseField name="chain_code" type="String" >Chain code</ResponseField>
            <ResponseField name="display_code" type="String" >Abbreviation (reference only, subject to change)</ResponseField>
            <ResponseField name="description" type="String" >Full name (reference only, subject to change)</ResponseField>
            <ResponseField name="decimal" type="Int" >Decimal precision</ResponseField>
            <ResponseField name="can_deposit" type="Bool" >Whether deposit is supported</ResponseField>
            <ResponseField name="can_withdraw" type="Bool" >Whether withdraw is supported</ResponseField>
            <ResponseField name="confirming_threshold" type="Int" >Number of confirmations required (may fluctuate)</ResponseField>
        </Expandable>
    </ResponseField>

    <ResponseField name="amount_detail" type="object" >
        <Expandable title="object">
            <ResponseField name="amount" type="String" >Transaction amount (e.g. one bitcoin is divisible to eight decimal places, and 100000000 represents 1 BTC)</ResponseField>
            <ResponseField name="abs_amount" type="String" >Absolute value of the transaction amount (for instance, 1.5 will be displayed for a transaction amount of 1.5 BTC)</ResponseField>
        </Expandable>
    </ResponseField>

    <ResponseField name="fee_detail" type="object" >
        <Expandable title="object">
            <ResponseField name="fee_coin_detail" type="object" >
                <Expandable title="object">
                    <ResponseField name="coin" type="String" >The coin symbol</ResponseField>
                    <ResponseField name="chain_code" type="String" >Chain code</ResponseField>
                    <ResponseField name="display_code" type="String" >Coin ticker symbol (not unique, changeable, for reference only )</ResponseField>
                    <ResponseField name="description" type="String" >Full name of coin (not unique, changeable, for reference only )</ResponseField>
                    <ResponseField name="decimal" type="Int" >Coin balance decimal places</ResponseField>
                    <ResponseField name="can_deposit" type="Bool" >Deposit accessibility</ResponseField>
                    <ResponseField name="can_withdraw" type="Bool" >Withdraw accessibility</ResponseField>
                    <ResponseField name="confirming_threshold" type="Int" >Confirming threshold of the coin</ResponseField>
                </Expandable>
            </ResponseField>
            <ResponseField name="gas_price" type="Int" >Gas Price</ResponseField>
            <ResponseField name="gas_limit" type="Int" >Gas limit</ResponseField>
            <ResponseField name="fee_used" type="Int" >Gas fees (e.g. one bitcoin is divisible to eight decimal places, and 100000000 represents 1 BTC)</ResponseField>
            <ResponseField name="fee" type="Float" >transaction fees per byte, which is specified when constructing the transaction; applicable to UTXO model</ResponseField>
            <ResponseField name="max_fee_amount" type="Int" >limit of fee amount, which is specified when constructing the transaction; applicable to UTXO model</ResponseField>
        </Expandable>
    </ResponseField>

    <ResponseField name="source_addresses" type="List" >List of ‘from addresses’</ResponseField>
    <ResponseField name="from_address" type="String" >From address</ResponseField>
    <ResponseField name="to_address" type="String" >To address</ResponseField>
    <ResponseField name="memo" type="String" >Memo for specified coins(XRP)</ResponseField>
    <ResponseField name="tx_hash" type="String" >Transaction hash</ResponseField>
    <ResponseField name="vout_n" type="Int" >UTXO transaction index</ResponseField>
    <ResponseField name="nonce" type="Int" >nonce value of a transaction on Ethereum</ResponseField>
    <ResponseField name="confirmed_number" type="Int" >Number of block confirmations</ResponseField>
    <ResponseField name="replace_cobo_id" type="String" >transaction ID of a transaction replaced via RBF</ResponseField>
    <ResponseField name="transaction_type" type="Int" >
    **only use in non-web3 wallet:**
    | Transaction Type | Code |
    | ----------- | ----------- |
    | TYPE_MPC_WEB| 100|
    | TYPE_MPC_API| 102|
    | TYPE_RBF_API_SPEEDUP| 103|
    | TYPE_RBF_WEB_SPEEDUP| 104|
    | TYPE_RBF_API_DROP| 105|
    | TYPE_RBF_WEB_DROP| 106|
    | TYPE_MPC_TRANSACTION_FROM_EXTERNAL| 107|
    | TYPE_MPC_RESEND_WEB| 108|
    | TYPE_MPC_BABYLON_STAKE| 500|
    | TYPE_MPC_BABYLON_STAKE_RBF| 501|
    | TYPE_FROM_DEPOSIT| 1000|

    **only use in web3 wallet:**
    | Transaction Type | Code |
    | ----------- | ----------- |
    | TYPE_MPC_WEB3_WEB| 300|
    | TYPE_MPC_WEB3_MMI_TX| 301|
    | TYPE_MPC_WEB3_API_TRANSACTION| 303|
    | TYPE_MPC_WEB3_TRANSACTION_FROM_EXTERNAL| 307|
    | TYPE_MPC_WEB3_RBF_API_SPEEDUP| 308|
    | TYPE_MPC_WEB3_RBF_WEB_SPEEDUP| 309|
    | TYPE_MPC_WEB3_RBF_API_DROP| 310|
    | TYPE_MPC_WEB3_RBF_WEB_DROP| 311|
    | TYPE_FROM_DEPOSIT| 1000|
    </ResponseField>
    <ResponseField name="operation" type="Int" >action type; it will be empty if the action is making a deposit (TRANSFER: 100, CONTRACT_CALL: 200)</ResponseField>
    <ResponseField name="block_detail" type="object" >
        <Expandable title="object">
            <ResponseField name="block_hash" type="String" >Block hash</ResponseField>
            <ResponseField name="block_height" type="Int" >Block height</ResponseField>
            <ResponseField name="block_time" type="Int" >Block time</ResponseField>
        </Expandable>
    </ResponseField>
    <ResponseField name="tx_detail" type="object" >
        <Expandable title="object">
            <ResponseField name="tx_hash" type="String" >transaction hash</ResponseField>
        </Expandable>
    </ResponseField>
    <ResponseField name="selected_utxos" type="object[]" >
        <Expandable title="object">
            <ResponseField name="address" type="String" >UTXO address</ResponseField>
            <ResponseField name="tx_hash" type="String" >UTXO transaction hash</ResponseField>
            <ResponseField name="vout_n" type="Int" >UTXO transaction index</ResponseField>
            <ResponseField name="value" type="Int" >UTXO value</ResponseField>
        </Expandable>
    </ResponseField>
    <ResponseField name="target_destinations" type="object[]" >
        <Expandable title="object">
            <ResponseField name="address" type="String" >target address</ResponseField>
            <ResponseField name="value" type="Int" >target value</ResponseField>
            <ResponseField name="is_change" type="Bool" >If is output for change</ResponseField>
        </Expandable>
    </ResponseField>
    <ResponseField name="extra_parameters" type="Json" >Extra parameters specified when constructing the transaction</ResponseField>
    <ResponseField name="created_time" type="Int" >Creation time of the transaction</ResponseField>
    <ResponseField name="updated_time" type="Int" >Last time the transaction is updated</ResponseField>
    <ResponseField name="failed_reason" type="String" >Reasons for rejection</ResponseField>
    <ResponseField name="to_address_details" type="Json" >to_address details specified when constructing the transaction</ResponseField>
    <ResponseField name="approval_process" type="Json" >Approval flow</ResponseField>
    <ResponseField name="signature" type="String" >Signature</ResponseField>
    <ResponseField name="remark" type="String" >The remark to withdraw</ResponseField>
    <ResponseField name="gas_station_child_id" type="String" >Request ID of the corresponding gas station refueling transaction</ResponseField>
    <ResponseField name="is_gas_station_tx" type="Bool" >Whether is gas station refueling transaction, applicable to transactions with transaction_type 1000</ResponseField>
   </Expandable>
</ResponseField>