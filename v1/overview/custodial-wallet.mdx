---
title: "Introduction"
icon: ""
description: "Custodial Wallet Solution based on HSM/SGX"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

Cobo Custodial Wallet is built upon bank-grade security technologies such as
Hardware Security Modules (HSM) and Intel Software Guard Extensions (SGX), that
is battle tested in traditional financial industry. In comparison with [Cobo MPC
Wallet](/v1/overview/mpc-wallet/mpc-introduction) and [Smart Contract Wallet](/v1/overview/smart-contract-wallet/cobosafe),
Cobo Custodial Wallet's clients entrust their private keys to Cobo, which are
stored in HSM and SGX based secure devices, and access their custodized assets
via web interface, mobile apps or SaaS APIs.

Cobo Custodial Wallet is more tailored to insitutions from traditional finance
industry or enterprises who don't want to store and manage private keys by
themseves. It offers a wide range of features, such as all-in-one solutoin to
manage crypto assets across multiple blockchains and
[Wallet-as-a-Service](/v1/overview/waas-introduction) API access etc.

Cobo Custodial Wallet has served more than 500 institutional clients since its
launch in 2018. With a track record of zero security incident or breaches, our
solution delivers the highest level of security, advanced risk management,
and developer-friendly integration options.

## Intuitive Custody Interface

Securely store and access over 80+ chains and 2,300+ tokens all on one simple interface.
<img src="/v1/images/full_custody.png" />

## Bank-Grade Security Features

Cobo Custodial Wallet employs a 3-tier (Hot-Warm-Cold) private key storage
architecture, with a majority of funds (95%) stored in cold wallets. These
wallets are fortified by offline key shards distributed across global
locations, ensuring bank-grade security. For faster transactions, hot wallets
are protected by bank-grade HSM, SGX, risk policies, and [Cobo
Auth](https://apps.apple.com/us/app/cobo-auth/id1485413078). This comprehensive
approach guarantees the highest level of security and transaction integrity.

## Comprehensive Risk Management

Cobo Custodial Wallet offers an extensive risk control framework and real-time
on-chain monitoring to effectively manage potential risks. Institutions can
implement customized risk control rules, including spending limits, whitelists,
blacklists, and IP monitoring.

## Role-based Access Control and Custom Workflows

Cobo Custodial Wallet puts you in control by offering role-based access control and
customizable workflows tailored to your institution's scale and needs. You can
define roles such as Admin, Spender, Approver, and Operator, segregating user
privileges effectively. Additionally, our customizable workflow engine and
transaction approval process enable the application of governance policies.
Each operation is secured by Cobo Auth, a mobile app that provides multi-role
and multi-level authentication for enhanced integrity.

## Wallet-as-a-Service Support

Cobo Custodial Wallet provides a comprehensive crypto wallet infrastructure service
via HTTP based SaaS APIs. Our [Wallet-as-a-Service (WaaS) ](/v1/overview/waas-introduction) allows clients
to seamlessly integrate an bank-grade wallet into their applications. With
easy-to-use APIs and plug-and-play SDKs, developers can quickly build and
deploy their applications with wallets supporting over 2,300 tokens across more
than 80 chains. Cobo Custodial Wallet handles token sweeping, reducing the effort
required for wallet development.

## Easy-to-integrate SDKs

Cobo Custodial Wallet comes with easy-to-integrate client libraries in
[Python](/v1/sdks-and-tools/sdks/waas/python), [JavaScript](v1/sdks-and-tools/sdks/waas/javascript), [Golang](/v1/sdks-and-tools/sdks/waas/go),
[Java](/v1/sdks-and-tools/sdks/waas/java), [PHP](v1/sdks-and-tools/sdks/waas/php) for third-party developers to use.
. This allows developers to quickly
learn and integrate Cobo Custody SaaS Service into their applicaitons.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>