---
title: Introduction
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

As the world's first crypto omni-custody provider, Cobo offers a full suite of
crypto wallet and custody technologies. This document intends to provide a
central place to help developers to learn how to leverage Cobo's SaaS based
APIs, SDKs and various tools to build their own crypto or web3 applications.

<img src="/v1/images/instroduction.png" />

## Cobo's Solutions

<CardGroup cols={2}>
   <Card title="Wallet as a Service" icon="clouds" href="/v1/overview/waas-introduction" color="#ea5a0c">
   SaaS APIs to Build Your Crypto Applications.
  </Card>
   <Card title="Custodial Wallet" icon="warehouse" href="/v1/overview/custodial-wallet" color="#ea5a0c">
    HSM/SGX based centralized custody solution.
  </Card>
   <Card title="MPC Wallet" icon="people-group" href="/v1/overview/mpc-wallet/mpc-introduction" color="#ea5a0c">
   Multi-Party-Computation (MPC) based Threshold Signature Scheme (TSS) to eliminate single point of failure.
  </Card>
   <Card title="Smart Contract Wallet" icon="file-contract" href="/v1/overview/smart-contract-wallet" color="#0285c7">
   Smart Contract based access control for smart wallets.
  </Card>
</CardGroup>

## About Cobo

Cobo is a globally trusted leader in digital asset custody solutions. As the
world’s first omni-custody platform, Cobo offers the full spectrum of solutions
from full custody, co-managed MPC custody, to fully decentralized custody as
well as wallet-as-a-service, advanced DeFi investment tools and an off-exchange
settlement network. Trusted by over 500 institutions with billions in assets
under custody, Cobo inspires confidence in digital asset ownership by enabling
safe and efficient management of digital assets and interactions with Web 3.0.
Cobo is SOC2 Type 1 and Type 2-compliance-certified and licensed in 5
jurisdictions.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>