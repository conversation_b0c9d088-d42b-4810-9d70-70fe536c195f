---
title: "Introduction"
icon: ""
description: "Institution's Gateway to DeFi"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

## Introduction

Cobo Argus is built on top of [Cobo Safe](/v1/overview/smart-contract-wallet/cobosafe). It aims to be insitution's
gateway to DeFi. Cobo Safe overcomes the limitations of traditional smart
contract wallets, e.g. multi-signature wallets, by adding flexible access
control capabilities to allow teams access DeFi protocols without sacrificing
efficiency for security and providing advanced, proprietary financial
risk-management tools. Through role-based access controls, asset owners can
give specific access to operators for efficient, and controlled access and
interaction with DeFi protocols.

With Cobo Argus, asset owners are able to implement new or leverage existing
DeFi Bots for monitoring on-chain data (e.g. Pool weight change, TVL change
etc), executing automatic tasks like auto-claiming rewards, auto-transferring
or swapping tokens. Besides, the Bots will also provide risk-control
services like for auto-withdrawing, auto-deleveraging among different
protocols, etc.

To get started, please head to https://argus.cobo.com/. Google Chrome is
recommended as the preferred web browser to use Cobo Argus.

## Cobo Argus’s Service

#### 1. Customize On-chain Access Control Services:

    Customizable tiered authorization procedure and approval automation tools - Assign different roles to different team members for institution-wide collaboration

#### 2. Authorization strategies:

    We provide easy-to-use authorization strategies on top DeFi protocols  with preset on-chain roles and permissions for users that don't have or have little technology background.

#### 3. Bots Services:

    Bots could monitor on-chain data and send alerts according to triggers set. Bots can also  execute  automatic tasks like auto-claiming rewards, auto-transferring or swapping tokens with manual settings. Besides, the Bots will also provide risk-management services like for auto-withdrawing, auto-deleveraging among different protocols, etc.

#### 4. Services:

    Alerts on price change, TVL change, collateral valuation change, smart contract upgrade can be configured with robotic tools.

#### 5. Smart Contract Based Wallet:

    Argus provides the DeFi services above on top of the decentralized custody solution, smart contract based multi-sig wallet.

#### 6. Information (Coming Soon):

    Security audit/rating, community news/update, security information update, update in the ecosystem and smart contract under collaboration, research report

## Cobo Argus Infrastructure

Cobo Argus consists of three layers: Base Layer, Functionality Layer, Application Layer.

- Base Layer：Also called the Wallet layer, it’s about the wallet or custody technology. Argus is built on top of the Gnosis Safe, a smart contract based multisig wallet. In future, the Argus can be also extended on the Account Abstraction wallet.
- Functionality Layer：In this layer, we aim to provide institutional-grade on-chain access control and workflow. Cobo Safe is a smart contract for on-chain access control and role delegation.
- Application Layer：In this layer, we focus on DeFi investment tools like DeFi bots, easy-to-use authorization strategies etc.

 <img src="/v1/images/argus_overview.png"/>

## Cobo Argus Users

The typical users of Argus are mainly crypto institutions , e.g. DeFi farming teams, liquidity providers or arbitrage trading teams as well as traditional finance firms like family offices, VCs, etc.

For DeFi teams/liquidity providers, they hold large amounts of crypto and require an efficient liquidity management solution that brings good yield on the treasury while having good risk management tools such as multi-sig access, role-based delegation and automation features.

Traditional finance firms are eager to participate in the multi-billion dollar and exploding DeFi industry, however, they have no tools to reliably access DeFi protocols and the uncertainty and unknowns deter them from maximizing their DeFi potential.

## DeFi Market Analysis/Insights

With the emerging blockchains and dApps in crypto industry, for institutional DeFi participants that have large fund sizes and own a variety of different crypto assets, they typically access multiple protocols and have team members (eg: miners, traders, investment managers, accountants, and admin operator) that will require various levels of access control.

However, with the lack of collaboration tools to mitigate access control risks when interacting with DeFi protocols and the lack of custom workflow support, means that the onus of protecting and controlling the security of their crypto lies on just a few key people or a person in the organization. This person or small group will hold disproportionate amounts of responsibility and risk in ensuring the security of their crypto.

Some of the DeFi teams are acutely aware of the lack of institutional-focused solutions in the market and might have tried developing their own systems of team collaboration and risk management role delegation and automatic alert systems. However, this is technically challenging and resource-heavy.

As the industry matures, the need for dedicated blockchain DeFi solutions will grow - it will support the advanced needs of crypto native/visionary teams that require cross-chain access, role-based controls, risk management and collaboration with multiple different DeFi protocols on different blockchains.

An example of how dangerous this is, in the case of QuadrigaCX CEO’s alleged death that took with him the passcodes that locked around US$250 million in customer assets. Another example is exchanges that lost funds due to a lack of internal risk controls due to staff misappropriation of funds, or carelessness/fraud of key persons. These problems can be mitigated through the removal of single points of failure, distributing risks through multi-sig approvals, delegated access controls, and multi-approval custom workflow engines with strict rules. We provide Argus as a solution in different environments, based on your organization’s needs.

To read our product documentation, please [click here](https://docs.cobo.com/cobo-argus/v/cobo-argus-v2-en-documentation/).

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>