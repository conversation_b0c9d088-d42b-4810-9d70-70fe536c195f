---
title: "Introduction"
icon: ""
description: Access Control Framework for Smart Contract Wallets
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

## Introduction

Cobo's Smart Contract Wallet solution, dubbed as "Cobo Safe", is a smart
contract based access control framework on EVM compatible blockchains. It is
designed and implemented as a fully open source, transparent, permissionless,
modular smart contract framework that can be easily used with any smart
contract based wallets, such as Account Abstraction wallets, Gnosis Safe
wallet, etc. to add flexible access control, delegation, automation
capabilities.

The first version of Cobo Safe was implemented as a [Gnosis
Safe](https://safe.global/) module, which added flexible role based access
control and parameter-level access control to the de factor multi-sig wallet in
the ethereum ecosystem. Cobo Safe V2 adopted a more modular architecture and
underpins the [Cobo Argus](/v1/overview/smart-contract-wallet/coboargus), an all-in-one on-chain solution
institution's DeFi investment.

Cobo Safe is deployed on multiple EVM blockchains. Users can use dedicated
[tools](/v1/sdks-and-tools/tools/pycobosafe), [SDKs](/v1/sdks-and-tools/sdks/cobo-safe/python) or [Cobo Argus](/v1/overview/smart-contract-wallet/coboargus) web interface to
interact with Cobo Safe contracts.

## How to Use Cobo Safe

### Create Safe Roles

Owners can create customized safe roles through Cobo Safe, such as "harvesters, traders, etc.” They are then able to set up smart contracts for DeFi projects, and set specific contractual permissions so that transactions can take place within restricted limits.
Delegate permissions to Members
After creating the role, you need to assign the role to a specific Member, and the Member can complete the transaction within the transaction parameters specified in the role by means of a single signature, improving the efficiency while ensuring the security of the transaction.

### Import Authorizer contracts

If you require deep parameter-level control over the transactions that a member can execute, you can import an Authorizer contract for the target contract, whereby all interactions initiated by that member with the target contract will be restricted by the Authorizer contract.
Cobo also offers a custom Authorizer contract service, if you require it, you <NAME_EMAIL>

### Setting a Whitelist for Token Transactions

You can restrict Members to only send specific Tokens to whitelist addresses.
Note that currently this feature is only available by ABI call. It’s not supported on the Argus website yet. But we will support it as soon as possible.

### Single-sig DeFi Transactions

Members can connect to the DeFi program through Wallet Connect and perform DeFi transactions within their permitted role.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>