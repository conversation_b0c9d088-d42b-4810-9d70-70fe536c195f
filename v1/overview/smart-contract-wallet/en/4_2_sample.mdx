---
title: "Authorizer Example"
icon: 
description: 
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

In this example, the following access controls are implemented by `SampleAuthorizer`:

* (Before a transaction is executed) validate that the transaction amount is below 1,000 wei
* (After a transaction has been executed) validate that the initiator of the transaction has a wallet balance greater than 10,000 wei &#x20;

```solidity
// SPDX-License-Identifier: LGPL-3.0-only
pragma solidity ^0.8.19;

import "../base/BaseAuthorizer.sol";

contract SampleAuthorizer is BaseAuthorizer {
    bytes32 public constant NAME = "SampleAuthorizer";
    uint256 public constant VERSION = 1;
    uint256 public constant flag = AuthFlags.FULL_MODE;

    constructor(address _owner, address _caller) BaseAuthorizer(_owner, _caller) {}

    function _preExecCheck(
        TransactionData calldata transaction
    ) internal override returns (AuthorizerReturnData memory authData) {
        if(transaction.value < 1000){
            authData.result = AuthResult.SUCCESS;
        }else {
            authData.result = AuthResult.FAILED;
            authData.message = "Value over 1k not allowed";
        }
    }

    function _postExecCheck(
        TransactionData calldata transaction,
        TransactionResult calldata callResult,
        AuthorizerReturnData calldata preData
    ) internal override returns (AuthorizerReturnData memory authData) {
        if(transaction.from.balance > 10000){
            authData.result = AuthResult.SUCCESS;
        }else{
            authData.result = AuthResult.FAILED;
            authData.message = "Wallet balance dropped below 10k";
        }
    }
}
```




<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>