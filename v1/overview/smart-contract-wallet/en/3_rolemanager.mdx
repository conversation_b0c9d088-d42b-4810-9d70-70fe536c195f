---
title: "Role Manager"
icon: 
description: 
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

`Cobo Safe` is designed in the [Role-Based Access Control (RBAC)](https://en.wikipedia.org/wiki/Role-based\_access\_control) approach.

The admins of an organization can create a `Role` and assign permissions to the `Role`. Team members, often referred to as `Delegates` in `Cobo Safe`, can only acquire permissions through their delegated roles.

`Role Manager` is a module in `Cobo Safe`. It is used to manage the `Delegate-Role` relationship.

Developer can create their own `Role Managers` by implementing the following interfaces:

```solidity
interface IRoleManager {
    function getRoles(address delegate) external view returns (bytes32[] memory);

    function hasRole(address delegate, bytes32 role) external view returns (bool);
}
```

* `getRoles()` is used to query the `Role` or `Roles` assigned to a `Delegate`
* `hasRole()` is used to check whether a specific `Role` has been assigned to the `Delegate`

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>