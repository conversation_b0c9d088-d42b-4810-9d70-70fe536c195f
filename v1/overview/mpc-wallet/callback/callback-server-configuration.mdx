---
title: "Callback Server Configuration"
icon: ""
description:
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

You'll need to complete the following configurations on the server where the callback server is deployed:

* Generate the callback server’s RSA private key

```
openssl genrsa -out callback-server-pri.pem 4096
```

* Export the callback server’s RSA public key

```
openssl rsa -in callback-server-pri.pem -pubout >callback-server-pub.key
```

* Copy the exported RSA public key (i.e. the callback-server-pub.key file) to the server where the TSS Node is deployed
* Copy the RSA public key of the TSS Node to the configuration files of the callback server
* If you use the callback server codes provided by Cobo Custody, please refer to the configuration instructions in the samples
* After you’ve successfully completed TSS Node initialization, execute the following command to query the RSA public key of the TSS Node:

```
sudo ./tss-node.sh info
```

Output example:

```
INFO[2022-12-14T07:09:44Z] Callback public key:
-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA4ki4fHY4/oSJOYBxBUI1
GUdLCUF7wMFYlfkpI/bSf7fGvzaygtZdORG6OR4vAoKxvqK/ZbJITMR5rS7pNxOv
vpLbsdVfJZGQn6wW8wLEx9ZB18JXt99oTESaxzoxMteXBC6tgYqWhSS386LfjSxQ
a4EvPunTRdYrO9VmnDWHM06/c76UxcS038pJ4OByXx7sH0xzS4MZQhZWyfYkqBT2
Ym9g46VXQ+CVZyWYXKPMyGPVTR1979lfJ1bFpYADNU3YUSK1mq0xaH9lQORFBs6/
0kK8m27BGR7h59rFJGgeZ2SNGzcEmiAp7rjOtFpLj1OmNXtjGhTxpn8omBrdH86+
qZfKsIm+O84e9S7uPMnx9hIpJHTcPoseW0d+WBoNplJQg22LUswPwaVgSHMB2U1B
aFkEzMAkRmGIejlMG25uYaVlA0Mf9E+/JaZSGPVuq43lhHCE08pNxGrak6/vTPTv
jAM449yTHrjDCDrFk/LdHqk5Ipt0b9CeHbCsQSs4BxHlDp2GEBJn1OEiizDwScWb
YiFi3a6PAvqDO56KEUFMXpC8CzgJ8svmQoldIAWSKGpdnuxUMcD+glCsi7K7+FI+
HDepO/4ejCm9DzuTUHG4PE0Ymb3PV8cZbLlGSZD/yG1541/xlHmzlOpAa8OdZBfb
s8wADQvpbJTyPkkMyq/i5SkCAwEAAQ==
-----END PUBLIC KEY-----
```

The Callback public key will be the RSA public key of the TSS Node.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>