---
title: "Callback Server Examples"
icon: ""
description:
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

Cobo Custody provides callback server examples in different programming languages. You can refer to these examples to quickly deploy a callback server:

[https://github.com/CoboGlobal/cobo-mpc-callback-server-examples](https://github.com/CoboGlobal/cobo-mpc-callback-server-examples)&#x20;

Alternatively, JWT code libraries also cover multiple programming languages. You can refer to this user guide and JWT’s [official website](https://jwt.io/libraries) to quickly deploy a callback server.&#x20;

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>