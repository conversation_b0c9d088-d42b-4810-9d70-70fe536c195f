---
title: "KeyGen Request Description"
icon: ""
description:
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>


## Callback API Request Description

### request_type

TypeKeyGen = 1

### request_detail

The format is a serialized JSON string of structure below.


<ParamField body="threshold" type="Int" required>Signature threshold.</ParamField>
<ParamField body="node_ids" type="[]String" required>Node IDs associated with the generation of all MPC key shares under the MPC wallet.</ParamField>
<ParamField body="curve" type="Int" required><p>Signature algorithm that has been used.</p><p>  0: SECP256K1 </p><p>  2: ED25519 </p></ParamField>

### extra_info

The format is a serialized JSON string of structure below.


<ParamField body="cobo_id" type="String" required>Unique ID of the key generation (KeyGen) request.</ParamField>
<ParamField body="api_request_id" type="String" required>Request ID passed in by the user using WaaS APIs; if the request is not sent via WaaS APIs, Cobo will automatically generate a Request ID.</ParamField>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>