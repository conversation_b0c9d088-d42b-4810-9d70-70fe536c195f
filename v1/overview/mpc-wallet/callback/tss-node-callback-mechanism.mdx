---
title: "TSS Node Callback Mechanism"
icon: ""
description:
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

After the TSS Node has been successfully set up, it will automatically connect to Cobo Custody Web and listen for tasks sent by the Cobo Custody backend.&#x20;

Currently, there are three types of tasks: key generation (KeyGen), transaction signing (KeySign), and soft key recovery (KeyReshare).&#x20;

If the callback mechanism is not set up, the TSS Node will immediately execute each task upon receiving it from the Cobo Custody backend.&#x20;

If the callback mechanism is set up, the TSS Node will send an approval request to the callback server upon receiving a task from the Cobo Custody backend. The TSS Node will execute the task only if it is approved by the callback server.

The TSS Node and the callback server connect via the HTTP communication protocol. Cobo Custody has also implemented the JSON Web Token (JWT) that is signed with the RS256 algorithm. For more information on JWT, please [click here.](https://jwt.io/introduction)&#x20;

* During TSS Node initialization, the TSS Node and the callback server will each generate and configure a RSA private/public key pair&#x20;
* When the TSS Node receives a task, it will construct CallbackRequest and generate the JWT token using its RSA private key&#x20;
* The TSS Node will send a request to the callback server via the HTTP POST method&#x20;
* The callback server will validate the JWT signature using the TSS Node's RSA public key&#x20;
* The callback server will construct CallbackRequest and sign it using its RSA private key before sending the JWT back to the TSS Node
* Once the TSS Node receives the HTTP response, it will validate the JWT signature using the callback server's RSA public key&#x20;
* Once validated, the TSS Node will execute or reject the task based on the response from the callback server

<img src="/v1/images/tss_node_callback_server.png" />

To get started with the TSS Node callback mechanism, please refer to the following steps:&#x20;

1. Callback Server Requirements&#x20;
2. Callback Server Configuration&#x20;
3. TSS Node Configuration&#x20;
4. TSS Node & Callback Server Startup

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>