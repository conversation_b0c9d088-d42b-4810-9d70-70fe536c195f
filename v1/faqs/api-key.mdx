---
title: "API Key"
description: "Frequently Asked Questions about API Key"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
<Tip> Navigate to your desired sections via the sidebar   👉 </Tip>

### How can I obtain the public and private API keys?
<Expandable title="Answer">
      <ResponseField >
      Cobo employs the ECDSA algorithm for signature verification. We strongly recommend that you generate your public and private API keys locally to ensure that <PERSON><PERSON> neither has access to nor stores your private keys. After generating your keys locally, you can manage the public keys by heading to Cobo Custody Web-Wallet-API. For more information on private key generation, signing and verification, please refer to https://github.com/CoboGlobal/. For API references, please go to /v1/api-references/overview/authentication.
      </ResponseField>
  </Expandable>

### Why cannot I add an API key on Cobo Custody Web?
<Expandable title="Answer">
      <ResponseField >
      Only the admin is authorized to add API keys on Cobo Custody Web. Please contact your admin if you don't have the permission
      </ResponseField>
  </Expandable>

### Why did my API signature verification fail?
<Expandable title="Answer">
      <ResponseField >
      Your API signature verification may have failed due to the following reasons:

The public keys are not associated with the correct environments (i.e., production, development) on Cobo Custody Web.

Incorrect API key types. Please make sure that you are using the correct key type (i.e., "querying", "withdraw and querying", "operation and querying").

The incorrect HOST has been used.
- Development: https://api.dev.cobo.com; corresponding Cobo Custody Web page: https://home.develop.cobo.com/.
- Production: https://api.cobo.com; corresponding Cobo Custody Web page: https://home.custody.cobo.com/.

If the issue still persists, please provide us with the parameters and return values for troubleshooting.
      </ResponseField>
  </Expandable>


### Do all API keys have a validity period of 90 days?
<Expandable title="Answer">
      <ResponseField >
      If an API key is not associated with an IP whitelist, its validity period is limited to 90 days. However, once an IP address is added to the API key, it becomes permanently valid.
      </ResponseField>
  </Expandable>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>