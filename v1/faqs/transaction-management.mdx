---
title: "Transaction Management"
description: "Frequently Asked Questions about Transaction Management"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
<Tip> Navigate to your desired sections via the sidebar   👉 </Tip>

### How to query the transaction details of a batch withdraw request?
    <Expandable title="Answer">
        <ResponseField >
         You can use the [GET /v1/custody/transactions_by_request_ids/](/v1/api-references/custody-wallet/transactions_by_request_ids) endpoint
         to query the transaction details of a batch withdraw request submitted under a Custodial Wallet. The request IDs should be provided in the `request_ids` field of the GET request, separated by commas.
         For an MPC Wallet, you can use the [GET /v1/custody/mpc/transactions_by_request_ids/](/v1/api-references/mpc-wallet/mpc_transactions_by_request_ids) endpoint instead. A maximum of 50 request IDs can be included in a single request.
        </ResponseField>
  </Expandable>

### How do I query the details of a pending transaction?
    <Expandable title="Answer">
        <ResponseField >
        You may use the [GET /v1/custody/pending_transactions/](/v1/api-references/custody-wallet/pending_transactions)
        endpoint to query the details of a pending transaction under a Custodial Wallet. For an MPC Wallet, you may refer to the `status` field with code 501 CONFIRMATION in [any transactional APIs](/v1/api-references/mpc-wallet/mpc_list_transactions) to retrieve the details of a pending transaction.
        Prior to using any endpoints, however, you need to first head to Cobo Custody Web and enable the "Transaction Notification - Includes Block Confirmation Number" Status feature.
        Failure to enable this feature will result in the inability to fetch transaction information. For more information, please refer to  /api-references/development/transaction-notification. Do note that some transactions cannot be retrieved due to fast on-chain confirmations (e.g., TRON).
        </ResponseField>
  </Expandable>


### How can I retrieve transaction records for all wallets?
    <Expandable title="Answer">
        <ResponseField >
            There are several methods to retrieve wallet transaction records:
            - **API Callback**: Enable the Transaction Notification feature on the Cobo Custody Web and set up the Cobo API callback service to retrieve transaction records. [Click here](/v1/api-references/development/transaction-notification) for more information.
            - **Cobo Custody Web**: Visit the Cobo Custody Web and navigate to the corresponding wallet. Switch to the "Transactions" tab and export transaction records using available filters.
            - **API Call**: Use the "[transactions\_by\_time](/v1/api-references/custody-wallet/transactions\_by\_time)" endpoint to fetch a transaction list that contains details such as transaction hash, amount, and timestamp. Filter the returned data using parameters such as time range, coin type, transaction type (e.g., withdraw, deposit), and address.
            - **API Call**: Use the "[transaction\_history](/v1/api-references/custody-wallet/transaction\_history)" endpoint to query transaction records for a specific coin and address. Filter the returned data using parameters such as side, max\_id, min\_id, begin\_time, end\_time, and include\_financial. The maximum number of transactions that can be returned is 50.
        </ResponseField>
  </Expandable>


 ###  In rare instances where an on-chain transaction that has been successfully confirmed is rolled back, how will Cobo handle the transaction that should have been credited?
    <Expandable title="Answer">
        <ResponseField >
            For Cobo Full Custody: If a transaction has reached the required number of confirmations but is later rolled back due to a hard fork, we will contact you promptly to resolve the issue.

            For Cobo MPC Co-Managed Custody and MPC Lite: The transaction confirmation count serves only as a reference (provided by Cobo). You maintain complete control over your blockchain addresses, enabling you to autonomously manage rolled-back transactions without requiring Cobo's assistance.
        </ResponseField>
  </Expandable>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>