---
title: "Coin Management"
description: "Frequently Asked Questions about Coin Management"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
<Tip> Navigate to your desired sections via the sidebar   👉 </Tip>


### How do I query the withdraw fees?
    <Expandable title="Answer">
        <ResponseField >
         The withdraw fees may fluctuate based on how busy a blockchain network is. You can refer to the `abs_estimate_fee` field
          of the [GET /v1/custody/coin_info](/v1/api-references/custody-wallet/coin_info) endpoint to check the estimated withdraw fees associated with a Custodial Wallet.
          For an MPC Wallet, please use the [GET /v1/custody/mpc/estimate_fee/](/v1/api-references/mpc-wallet/mpc_estimate_fee) endpoint instead.
</ResponseField>
  </Expandable>

### What do `amount` and `abs_amount` stand for in the interface?
<Expandable title="Answer">
   <ResponseField >In the realm of cryptocurrencies, each coin possesses a smallest unit.
   For instance, a satoshi is the smallest unit of Bitcoin. Each Bitcoin equals 100 million satoshis (i.e., 100,000,000).
   Co<PERSON> denominates amount in terms of the smallest unit specific to each cryptocurrency to prevent complications associated with the misuse of floating point numbers.
   If you transfer 1 Bitcoin, for instance, the `amount` will be displayed as 100,000,000 and the `abs_amount` will be 1.
   This conversion is calculated using the formula: abs_amount = amount / pow(10, decimal).</ResponseField>
</Expandable>

### How do I query the minimum deposit amount for a specific coin?
   <Expandable title="Answer">
      <ResponseField >
      You can refer to the `minimum_deposit_threshold` field of the [GET /v1/custody/get_supported_coins/](/v1/api-references/custody-wallet/get_supported_coins)
       endpoint to query the minimum deposit amount for a specific coin within a Custodial Wallet. If the response returns a zero, it indicates that the there is no minimum deposit requirement for the selected type of coins. Note that coins held under an MPC Wallet do not have any minimum deposit requirements.
      </ResponseField>
  </Expandable>


### Why does the coin amount returned by the 'org\_info' field of the API endpoint differ from the amount shown on the Cobo Custody Web dashboard?
   <Expandable title="Answer">
      <ResponseField >
            The data returned by the org\_info field of the API endpoint should match the balance of the corresponding coin in your wallet.
            However, the coin balances displayed on the Cobo Custody Web dashboard (as seen in the first screenshot) are snapshots and may experience a delay of up to one hour.
            If you need to query real-time balances, we recommend that you navigate to the specific wallet first and verify the balance of the corresponding coin within that wallet (as seen in the second screenshot).
            <img src="/v1/images/faq-coin-management-02.png" />
            <img src="/v1/images/faq-coin-management-01.png" />
      </ResponseField>
  </Expandable>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>