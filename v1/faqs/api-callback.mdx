---
title: "API Callback"
description: "Frequently Asked Questions about API Callback"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
<Tip> Navigate to your desired sections via the sidebar   👉 </Tip>


### Do I need to verify a callback?
<Expandable title="Answer">
      <ResponseField >
      For security reasons, we highly recommend that you complete the signature verification and IP whitelisting when receiving <PERSON><PERSON>'s callback messages. You can obtain <PERSON><PERSON>'s pubkey by heading to Cobo Custody Web-Wallet-API Callback. For more information on signature verification, refer to /api-references/development/callback-signature.
      </ResponseField>
  </Expandable>

### How many times can an API callback notification be pushed?
<Expandable title="Answer">
      <ResponseField >
      Each API callback can be pushed for a maximum of 14 times. The time intervals between each push are incremental, starting at 10-minute intervals and progressively extending to hourly, bi-hourly, and so forth.
      </ResponseField>
  </Expandable>

### Why do I need to configure API callback confirmation?
<Expandable title="Answer">
      <ResponseField >
      Given the potential single-point failure risks associated with API servers, we strongly recommend that you maintain a dedicated callback server for configuring API callback confirmation. This not only enhances risk isolation but also facilitates effective internal security control. In the event of an exception during API callback confirmation, Cobo's 24/7 customer support will provide you with real-time alerts, enabling immediate internal inspection to address any potential security vulnerabilities.
      </ResponseField>
  </Expandable>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>