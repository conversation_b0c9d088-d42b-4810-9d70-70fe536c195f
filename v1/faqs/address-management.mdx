---
title: "Address Management"
description: "Frequently Asked Questions about Address Management "
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
<Tip> Navigate to your desired sections via the sidebar   👉 </Tip>


### How to batch-generate addresses?
   <Expandable title="Answer">
      <ResponseField >You may use the [POST /v1/custody/new_addresses/](/v1/api-references/custody-wallet/new_addresses) endpoint to batch-generate addresses under a Custodial Wallet.
         To batch-generate addresses under an MPC Wallet, you may use the [POST /v1/custody/mpc/generate_addresses/](/v1/api-references/mpc-wallet/mpc_generate_addresses) endpoint instead. A maximum of 200 addresses can be generated in a single request.</ResponseField>
  </Expandable>

### How to verify whether a withdraw address is legitimate?
    <Expandable title="Answer">
        <ResponseField >You can use the [GET /v1/custody/is_valid_address/](/v1/api-references/custody-wallet/is_valid_address) endpoint to query whether a withdraw address under a Custodial Wallet is legitimate.
        For an MPC Wallet, please use the [GET /v1/custody/mpc/is_valid_address/](/v1/api-references/mpc-wallet/mpc_is_valid_address) endpoint instead. If the response returns an zero, it indicates that the address is deemed invalid.</ResponseField>
  </Expandable>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>