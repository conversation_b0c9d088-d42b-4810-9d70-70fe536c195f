---
title: "Managing Wallet Balances"
sidebarTitle: "Managing Wallet Balances"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
**In Custodial Wallet**, you can query the wallet balance using the [Get Account Details](/v1/api-references/custody-wallet/org_info) , where the balance of each type of coin will be returned in the balance field of the response.
Note that because Cobo will automatically aggregate funds in deposit addresses, the balance in a deposit address may not reflect the actual balance of the selected type of coin.

<Tip>In Custodial Wallet, tokens deposited into these addresses will undergo automated aggregation (token sweep) by Cobo. You are exempt from the necessity to pre-fund or fund transaction fees for your deposit addresses. This translates into substantial savings in both development and operational efforts for your team.</Tip>
<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

api_secret = "your_api_secret"  # your wallet api secret
# init cobo client
client = Client(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# get account balance
response = client.get_account_info()
print(f"Account balance: {response.result}")


```
```java Java
package com.cobo.custody.api.client.impl;
import java.util.List;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.OrgInfo;

public class CoboApiExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret
    // init cobo client
    CoboApiRestClient client = CoboApiClientFactory.newInstance(
        new LocalSigner(apiSecret),
        Env.DEV,
        false).newRestClient();

    // get account balance
    ApiResponse<OrgInfo> getOrgInfo = client.getOrgInfo();
    System.out.println("Addresses History:" + getAddressHistory.getResult());
    }
}


```
</CodeGroup><br />
<Accordion title="View Response - Custodial Wallet">
```json
{
  "success": true,
  "result": {
    "name": "test",
    "assets": [
      {
        "coin": "ETH",
        "display_code": "ETH",
        "description": "Ethereum",
        "decimal": 18,
        "can_deposit": true,
        "can_withdraw": true,
        "balance": "0",
        "abs_balance": "0",
        "fee_coin": "ETH",
        "abs_estimate_fee": "0.0003648",
        "confirming_threshold": 12,
        "dust_threshold": 1,
        "token_address": "",
        "require_memo": false
      },
      {
        "coin": "BTC",
        "display_code": "BTC",
        "description": "Bitcoin",
        "decimal": 8,
        "can_deposit": true,
        "can_withdraw": true,
        "balance": "102730",
        "abs_balance": "0.0010273",
        "fee_coin": "BTC",
        "abs_estimate_fee": "0.00013513",
        "confirming_threshold": 3,
        "dust_threshold": 546,
        "token_address": "",
        "require_memo": false
      }
    ]
  }
}


```
</Accordion>


**In MPC Wallet**, you can query the balance of each address using the [Get Balances List](/v1/api-references/mpc-wallet/mpc_list_balances) up to 50 addresses.
<CodeGroup>
```python Python
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
from cobo_custody.client.mpc_client import MPCClient

api_secret = "your api secret"  # your wallet api secret

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# get balance list
response = client.list_balances("ETH",0,50)
print(f"Address History: {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.MPCListBalances;

public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret
    String coin = "ETH";  // Your testing coin

    // init client
    CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
    new LocalSigner(apiSecret),
    Env.DEV,
    false).newMPCRestClient();

    // get balance list
    ApiResponse<MPCListBalances> listBalances = client.listBalances(coin,0,50);
    System.out.println("Balance List: " + generateAddressResponse.getResult());
    }
}
```
</CodeGroup><br />
<Accordion title="View Response - MPC Wallet">
```json
{
  "success": true,
  "result": {
    "total": 2,
    "coin_data": [
      {
        "address": "******************************************",
        "coin": "ETH",
        "chain_code": "ETH",
        "display_code": "ETH",
        "description": "Ethereum",
        "balance": "49999999999999999986",
        "decimal": 18
      }
    ],
    "nft_data": [
      {
        "nft_code": "NFT_ETH_BLUE_CHURCH",
        "token_id": "200",
        "address": "******************************************",
        "chain_code": "ETH",
        "contract_address": "******************************************",
        "balance": "1"
      }
    ]
  }
}
```
</Accordion>


Transactional APIs can be used to retrieve your deposit and withdraw history for account reconciliation.
Please refer to <Tooltip tip="Coming Soon">Best Practices of Managing Transaction History</Tooltip> article for more information.
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>