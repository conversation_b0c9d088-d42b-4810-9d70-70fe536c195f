---
title: "API Error Handling and Reporting "
"og:title": "Best Practices for API Error Handling"
"og:description": "Learn how to troubleshoot, report, and enhance the reliability of your blockchain applications.
Elevate your development skills with best practices on error management in API integrations."
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

## Overview

Understanding how to handle API errors is crucial for ensuring a positive user experience and smooth business operations when collaborating with any third-party APIs. These errors may include:


1. Timeouts due to unavailability of the third-party service.
2. Incorrect request formats due to user errors or non-fatal software errors.
3. Runtime errors due to system exceptions or other unexpected issues.

The approach to handling individual errors when utilizing third-party APIs depends on the nature of each API call and the specific business context. The following will outline best practices for managing API errors and resolving common issues.

## Error Codes

The error codes returned by Cobo play a crucial role in identifying specific issues that have arisen during API calls. Understanding these error codes is essential for troubleshooting and ensuring the smooth integration of APIs. Cobo provides two distinct types of error codes: Cobo Error Codes and HTTP Error Codes.

Cobo Error Codes are typically generated during the processing of API calls and may result from various factors, such as incorrect inputs and server exceptions. For more information, please [refer to](/v1/api-references/development/error-codes) the description corresponding to each error code.

HTTP Error Codes, on the other hand, indicate client-side errors and may start with “4”. These errors are usually resulted from issues with parameters in the HTTP request you provided.

HTTP Error Codes that start with “5” indicate server-side errors. In such cases, specific error information cannot be provided. We recommend that you:

    1. Do not send the API request again immediately.
    2. Double-check the parameters you provided in your request. It might be that one of the parameters was not formatted correctly, thus resulting in a failure in our backend.
    3. Contact Cobo’s customer support or seek assistance on Cobo Discord.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>