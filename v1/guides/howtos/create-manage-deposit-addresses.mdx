---
title: "Creating and Managing Deposit Addresses"
sidebarTitle: "Managing Deposit Addresses"
"og:title": "Creating Deposit Addresses"
"og:description": "Learn how to securely create, manage and view deposit addresses in your custodial wallet. Explore our guide to enhance your crypto wallet management. "
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

## Overview

User deposit addresses are commonly used by retail-facing platforms to receive funds from external users. These addresses are assigned to each user to easily distinguish the source of deposit transactions.

With Cobo Wallet-as-a-Service, you can create and manage deposit addresses for your applications in both **Custodial Wallet** and **MPC Wallet**.

## Create Addresses

You can create up to 200 addresses by using [Cobo SDKs](https://github.com/CoboGlobal) and corresponding APIs ([Custodial Wallet API](/v1/api-references/custody-wallet/new_addresses), [MPC Wallet API](/v1/api-references/mpc-wallet/mpc_generate_addresses) )

**Custodial Wallet**
<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

api_secret = "your_api_secret"  # your wallet api secret
# init cobo client
client = Client(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# new deposit addresses
response = client.batch_new_deposit_address("GETH",1)
print(f"New deposit addresses: {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;
import java.util.List;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.NewAddresses;

public class CoboApiExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret
    String coin = "GETH";  // Your testing coin
    // init cobo client
    CoboApiRestClient client = CoboApiClientFactory.newInstance(
        new LocalSigner(apiSecret),
        Env.DEV,
        false).newRestClient();

    // create new deposit addresses
    ApiResponse<NewAddresses> newAddresses = client.newAddresses(coin, 1, false);
    System.out.println("generateAddresses:" + newAddresses.getResult());
    }
}
```
</CodeGroup><br />
<Accordion title="View Response - Custodial Wallet">
```json
{
  "success": true,
  "result": {
    "coin": "GETH",
    "address": "******************************************"
  }
}
```
</Accordion>

**MPC Wallet**
<CodeGroup>
```python Python
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
from cobo_custody.client.mpc_client import MPCClient

api_secret = "your api secret"  # your wallet api secret

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

response = mpc_client.generate_addresses("GETH",1)
print(f"New deposit addresses: {response.result}")
```
```java Java 
package com.cobo.custody.api.client.impl;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.MPCAddressList;

public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret
    String coin = "GETH";  // Your testing coin

    // init client
    CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
    new LocalSigner(apiSecret),
    Env.DEV,
    false).newMPCRestClient();

    // new deposit addresses
    ApiResponse<MPCAddressList> generateAddressResponse = mpcClient.generateAddresses(chain_code, 1);
    System.out.println("Generated Address: " + generateAddressResponse.getResult());
    }
}
```
</CodeGroup><br />
<Accordion title="View Response - MPC Wallet">
```json
{
  "success": true,
  "result": {
    "addresses": [
      {
        "id": 721935051680543203,
        "address": "******************************************",
        "hd_path": "m/44/60/1/0/82",
        "encoding": 0
      }
    ]
  }
}
```
</Accordion>

You need to specify the `Coin` field in the format of **Chain Code_Token Code** (e.g., “ETH_USDT”, “TRON_USDC”). Token Code is not required for native coins (e.g., “BTC”, “ETH”). You can query the types of coins supported by your wallet via the [Get Supported Coins](/v1/api-references/custody-wallet/get_supported_coins).

These addresses can be used to receive coins under the same chain, and EVM addresses can be used to receive coins for all EVM-compatible chains.

For BTC, Cobo supports both _segwit_ (3 address) and _native_segwit_ (bc1 address) addresses. You can input “True”  in the `native_segwit` field to generate bc1 addresses.

Please make sure you have added coins on Cobo Custody Web before creating any deposit addresses via API.

## Manage Addresses

You can view your address list using the Get Address History ([Cusotidal Wallet API](/v1/api-references/custody-wallet/address_history), [MPC Wallet API](/v1/api-references/mpc-wallet/mpc_list_addresses)).
This API allows you to retrieve addresses by coin types. Each API request can retrieve up to 20 addresses, in either ascending or descending order.

**Cusotidal Wallet**
<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

api_secret = "your_api_secret"  # your wallet api secret
# init cobo client
client = Client(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# get address history
response = client.get_address_history("GETH")
print(f"Transaction history: {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;
import java.util.List;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.*;

public class CoboApiExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret
    String coin = "GETH";  // Your testing coin
    // init cobo client
    CoboApiRestClient client = CoboApiClientFactory.newInstance(
        new LocalSigner(apiSecret),
        Env.DEV,
        false).newRestClient();

    // get address history
    ApiResponse<List<Address>> getAddressHistory = client.getAddressHistory(coin);
    System.out.println("Addresses History:" + getAddressHistory.getResult());
    }
}
```
</CodeGroup><br />
<Accordion title="View Response - Custodial Wallet">
```json
{
  "success": true,
  "result": [
    {
      "coin": "GETH",
      "address": "******************************************"
    },
    {
      "coin": "GETH",
      "address": "******************************************"
    }
  ]
}

```
</Accordion>

**MPC Wallet**
<CodeGroup>
```python Python
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
from cobo_custody.client.mpc_client import MPCClient

api_secret = "your api secret"  # your wallet api secret

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# get address history
response = mpc_client.list_addresses("GETH")
print(f"Address History: {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.MPCAddressList;

public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret
    String coin = "GETH";  // Your testing coin

    // init client
    CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
    new LocalSigner(apiSecret),
    Env.DEV,
    false).newMPCRestClient();

    // new deposit addresses
    ApiResponse<MPCAddressList> generateAddressResponse = mpcClient.listAddresses(coin);
    System.out.println("Generated Address: " + generateAddressResponse.getResult());
    }
}
```
</CodeGroup><br />
<Accordion title="View Response - MPC Wallet">
```json
{
  "success": true,
  "result": {
    "total": 2,
    "addresses": [
      {
        "id": 721941011752862138,
        "address": "******************************************",
        "encoding": 0,
        "hd_path": "m/44/60/1/0/85"
      },
      {
        "id": 721941011752862137,
        "address": "******************************************",
        "encoding": 0,
        "hd_path": "m/44/60/1/0/84"
      }
    ]
  }
}

```
</Accordion>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>