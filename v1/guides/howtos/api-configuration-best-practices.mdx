---
title: "Best Security Practices for API Configuration"
sidebarTitle: "API Configuration Best Practices"
"og:title": "Best Practices for API Configuration Security"
"og:description": "Explore the top security practices for API configuration to fortify your digital assets. Learn essential tips to safeguard sensitive data, prevent unauthorized access, and ensure robust API security.
                    Stay ahead of potential threats with our comprehensive guide."
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
### 1. Ensure the security of TSS Node and MPC key-shares

The TSS Node is a program for managing MPC key-shares. To ensure the secure maintenance of your TSS Node, please follow the security practices below:

- Cobo offers three deployment methods for the TSS Node. The generic version is suitable for regular Linux servers or Apple Macbooks,
  and a version specifically tailored for deployment on Intel® Software Guard Extensions (SGX) servers is also available.
  For optimal security, we recommend using SGX servers.
- During the installation process, the TSS Node requires access to Ubuntu APT and Docker.io image repositories.
  Ensure to grant the associated network permissions and promptly revoke them following the TSS Node initialization.
  When generating root extended public keys, MPC key-shares, and transaction signatures, the TSS Node will interact with <PERSON><PERSON>'s servers through the public network.
  Kindly ensure that the relevant network permissions are only open during these specific processes and are promptly closed afterward.
- During the initialization of the TSS Node, you will be required to enter a password for encrypting the MPC key-share database.
  We recommend using a password manager (e.g., 1Password, Bitwarden) to generate a complicated password between 16 to 32 characters.
  The password must be kept secure at all times.
- The MPC key-share database file and the corresponding password must be backed up.We recommend storing them separately on different secure devices,
  with strict restrictions for accessing these devices:
    - Apply additional encryption to all backup data
    - Use a secure hardware-encrypted USB drive for backup
    - Store backup devices in a secure deposit box and restrict access permissions
- Cobo offers dedicated callback services for TSS Node. We strongly recommend deploying and configuring these callback services on a separate server,
  different from the one hosting the TSS Node. For security reasons, make sure to activate callback services for all transactions.
- Cobo will regularly update TSS Node software packages to introduce new features and performance enhancements. For optimal functionality and security,
  ensure that your TSS Node is always running the latest version.

### 2. Proper use the 'request_id' field in withdraw requests

The 'request_id' serves as the unique identifier for each withdraw request, corresponding to a specific withdraw action.
This ensures that even in the event of network issues, duplicate withdraw requests sent to Cobo will be rejected due to the repeated 'request_id'.
We recommend using a Universally Unique Identifier (UUID).

### 3. Properly configure withdraw confirmation callbacks
 - The callback URL designated by the client should be hosted on a server isolated from the one storing the API private key to minimize the risk of a single point of failure.
     For instance, it could be a dedicated server hosting a risk control engine.
     <u>We have noticed instances where clients either fail to validate withdraw requests or use callback URLs located on the same server that initiates withdraw requests.</u>
 - Please conduct verification for all withdraw requests:
     - a.Check if request_id, coin, to_addr, amount, etc., are valid and consistent with your business requests.
     - b.Verify high-risk withdraw behaviors through internal risk control audits. This may involve server security checks, user confirmation for withdraw authenticity, and more.
 - Use the HTTPS protocol and enable two-way verification to prevent man-in-the-middle attacks. For Cobo's callback messages, verify the signature using Cobo's public key. We strongly recommend using Cobo SDKs, as they come embedded with default signature verification logic.
 - Validate the timestamp carried in the request header to be close to the local server's time. An alert should be triggered if the difference exceeds a specific threshold.
 - Cache signature information for a specific period to identify duplicate signatures. If repeated signatures are detected, implement appropriate risk control measures or contact <NAME_EMAIL> to safeguard against replay attacks.

### 4. Properly configure transaction notification callbacks

<ol>
<li>Upon receiving a push message from Cobo, please call the transaction querying endpoint and use the specific 'id' field to validate the transaction.
    <u>Do not rely solely on push messages for deposit and withdraw confirmations.</u></li>
<li>Due to unpredictable factors such as network delays, a callback may be repeated.
    We recommended using the 'id' field as the unique identifier for each transaction and employing database read-write locks to prevent duplicate accounting.</li>
<li>Please use the HTTPS protocol and enable two-way verification to prevent man-in-the-middle attacks.
    For Cobo's callback messages, verify the signature using Cobo’s pubkey. We strongly recommend using Cobo SDKs, as they come embedded with default signature verification logic.</li>
</ol>


### 5. Configure risk control rules and set up Cobo Guard
Cobo offers a flexible and configurable set of risk control strategies to safeguard your digital assets. Currently, it supports six types of strategies:
<ol>
<li>Manage all withdrawals: In the event of abnormal business activity, you can swiftly tighten wallet controls by configuring a rule that subjects all withdraw requests to review.</li>
<li>Manage all withdrawals initiated via APIs</li>
<li>Manage all withdrawals initiated via the Cobo Custody Web</li>
<li>Manage withdrawals to addresses not in the whitelist</li>
<li>Manage withdrawals to addresses in the whitelist</li>
<li>Set withdraw limits (i.e., per transaction, per hour, per day): Please set a threshold based on your specific business requirements</li>
</ol>

Different risk control strategies can be combined with various control actions to create flexible and customized risk control rules. Control actions include automated approval, automated rejection, and assigning a specified number of approvers for review.
For instance, you can require approval from any one of four approvers for a transaction, or require approvals from at least two out of four approvers for a withdraw exceeding $10,000.

To adjust the priority of your risk control rules, use the up/down arrow under the 'Operations' tab. These rules will be applied sequentially from priority 1 to n. If a withdraw triggers multiple risk control rules, the one with the highest priority will be executed.

Important note: <u>After editing your risk control rules, click 'Save Changes' and approve the request on your Cobo Guard.You can then refresh the page to verify if the rules are in effect. We also recommend conducting a test transaction to ensure that the rules align with your business requirements.</u>

### 6. Safeguard your Cobo Custody account and Cobo Guard
<ol>
<li>Refrain from using easily crackable, simple passwords and avoid storing passwords casually, such as in browser auto-fill. When setting up 2FA or Cobo Guard, exercise caution not to disclose the QR codes or secrets (e.g., ensure there are no others nearby, avoid sending them through screenshots).</li>
<li>Never share your password or 2FA with anyone, including colleagues or Cobo staff. Note that Cobo staff will never request your password or 2FA in any form.</li>
<li>Stay vigilant against phishing websites and take note of the official Cobo Custody website: https://home.custody.cobo.com/. You can also set up an anti-phishing phrase on the Cobo Custody Web by navigating to "Settings - Anti-Phishing Code". This phrase will be visible in the upper-right corner of the webpage after each login.</li>
<li>Never lend a device with Cobo Guard to others or disclose your iPhone password. Ensure that a password or biometric unlock is always required when using a device with Cobo Guard.</li>
</ol>

### 7. Properly configure permissions to prevent internal collusion and malicious activities.
Exercise caution when granting advanced roles such as admins, approvers, and withdrawers.
Admins have the authority to perform high-risk operations, including team and wallet management, risk control configurations, and more.
We recommend that you grant admin roles to only the organization owner.
Additionally, ensure the separation of duties by assigning the withdrawer and approver roles to different individuals.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>