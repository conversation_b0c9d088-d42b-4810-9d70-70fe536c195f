---
title: "Safeguarding API Security Key"
sidebarTitle: "Safeguarding API Security Key"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>


Currently, <PERSON><PERSON> uses the asymmetric Elliptic Curve Digital Signature Algorithm (ECDSA). Clients will need to first generate a local key-pair (i.e., public and private keys) and then inform <PERSON><PERSON> about the public key. <PERSON><PERSON> will use this public key to verify whether a request from the client has a corresponding private key signature. We strongly recommend using <PERSON><PERSON>'s SDKs, which come with built-in signature verification logic. [Click here](https://www.cobo.com/developers/sdks-and-tools/sdks/waas/python) to download <PERSON><PERSON>’s SDKs.

If you choose not to use Cobo's SDKs for WaaS API calls, please [refer to](https://www.cobo.com/developers/api-references/overview/authentication) this link for a better understanding of <PERSON><PERSON>'s API authentication mechanism. Aside from the public API endpoints, <PERSON><PERSON> requires that each API call request be signed. The api_key corresponds to your public key and must be manually added on Cobo Custody Web. The api_secret represents your private key and should be securely stored. You can use Cobo's SDKs to create the corresponding api_secret and api_key.

For API key security, we recommend the following best practices:

- Generate and store api_key and api_secret on a trusted device. We recommend encrypting the api_secret and then decrypting it during use to ensure it is never transmitted between other servers, networks (e.g., Telegram or instant messengers), or individuals.
- Add IP whitelists. Note that Cobo only accepts requests from designated servers.
- Implement Role-Based Access Control (RBAC) to restrict permissions associated with each API key or token. Ensure that every key is assigned only the essential permissions required for its intended function, adhering to the principle of least privilege.
- Enforce a robust API key rotation policy to systematically update keys at regular intervals. This practice not only mitigates the risk of compromised keys but also guarantees the revocation of outdated or unused keys.
- Implement short-term expirations for API keys or tokens to reduce the window of opportunity for potential attackers. Short-lived keys necessitate more frequent renewal, contributing to heightened security measures by limiting access time.
- Encrypt API keys both in transit (HTTPS) and at rest. Employ encryption and robust storage mechanisms to safeguard keys on servers.
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>