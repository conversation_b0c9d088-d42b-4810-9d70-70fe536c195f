---
title: "Deposit and Withdraw Processes"
sidebarTitle: "Deposit and Withdraw Processes"
"og:title": "Deposit and Withdraw Processes"
"og:description": " Discover the withdraw and deposit processes for both Custodial Wallets and MPC Wallets."
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

For digital asset custodians, crypto transactions can be broadly categorized into two types: withdrawals and deposits.
A withdraw transaction is initiated by the client directly on the Cobo platform.
Conversely, a deposit transaction is initiated by a third party, wherein the receiving address is associated with a Cobo Custodial Wallet and/or an MPC Wallet.

### Withdraw process for Custodial Wallets and MPC Wallets:

<img src="/v1/images/custodial-wallet-withdrawal-process.png" />

Notes for withdraw transactions initiated from Custodial Wallets:
 - The transaction status that can be queried includes "success" and "failed" (applicable to both API calls and API callbacks).
 - If you tick "Push Pending Transaction" under Transaction Notification on the Cobo Custody Web, you will receive a push notification whenever there is a change in the number of block confirmations for a specific transaction. The notification will indicate the transaction status as 'pending' and include essential details such as the number of confirmed blocks and the threshold. Please note that each notification for a given number change will be sent only once.
 - You can also check the number of confirmed blocks for a specific transaction by navigating to the "Transactions" tab on the Cobo Custody Web.

<img src="/v1/images/mpc-wallet-withdrawal-process.png" />

### Deposit process for Custodial Wallets and MPC Wallets:

<img src="/v1/images/custodial-mpc-deposit-process.png" />

Notes for deposit transactions associated with Custodial Wallets:
 - Cobo reserves the right to reject a deposit transaction if it triggers compliance risks or violates risk control rules. Transactions declined by Cobo will not be displayed on the Cobo Custody Web, and API callback notifications will not be pushed.
 - If the transaction amount falls below the minimum deposit requirement, it will not be displayed on the Cobo Custody Web, and API callback notifications will not be pushed. For best practices, kindly refer to this [guide](/v1/guides/howtos/querying-deposit-transactions).

| Status      | Description |
| ----------- | ----------- |
| PENDING | Transaction is being confirmed|
| SUCCESS | Transaction completed|
| FAILED | Transaction failed|

Notes for deposit transactions associated with MPC Wallets:
 - Clients have the flexibility to choose whether to perform AML checks (e.g., Travel Rule, KYT) for a specific transaction.
   Additionally, they can decide whether to record the transaction in their account.
 - MPC Wallets do not have a minimum deposit requirement.

| Value | Status | Description |
| ----------- | ----------- | ----------- |
| 101 | PENDING_APPROVAL | Transaction is pending approval |
| 201 | QUEUED | Transaction is in queue|
| 301 | PENDING_SIGNATURE | Transaction is pending signatures |
| 401 | BROADCASTING | Transaction is being broadcasted |
| 402 | BROADCAST_FAILED | Transaction failed to be broadcasted on chain|
| 403 | PENDING_ON_CHAIN | Transaction successfully broadcasted on chain|
| 501 | PENDING_CONFIRMATION | Transaction is being confirmed |
| 900 | SUCCESS | Transaction completed |
| 901 | FAILED | Transaction failed|

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>