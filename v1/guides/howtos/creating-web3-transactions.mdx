---
title: "How to Create Web3 Transactions in MPC Wallets"
sidebarTitle: "Creating Web3 Transactions"
"og:title": "Creating Web3 Transactions in MPC Wallets"
"og:description": "Learn how to create Web3 transactions in MPC Wallets using Cobo APIs."
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
## Overview

Web3 transactions typically involve interacting with smart contracts on the blockchain, with applications extending to decentralized applications (dApps), non-fungible tokens (NFTs), and beyond. This guide will outline how developers can create Web3 transactions in MPC Wallets using the APIs provided by Cobo.

Note: The code samples below are only applicable to Web3 Wallets under the MPC Wallets.

## Code Samples

#### Initiating a withdraw transaction using Web3 Wallets (e.g., sending 0.01 ETH)


Step 1: Initialize the mpc_client

Step 2: Create the withdraw transaction

<CodeGroup>
```python Python
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEVELOP_ENV
from cobo_custody.signer.local_signer import LocalSigner
import time

api_secret = "your api secret"  # your wallet api secret
chain_code = "ETH"  # your target chain
coin_code = "ETH"  # your target coin
amount = "100000000000000000"  # withdraw amount：0.01ETH
from_address = "your mpc wallet address"  # your mpc wallet address
to_address = "to address"  # to address

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEVELOP_ENV, debug=False)

request_id = f"MPCTransaction-{int(time.time() * 1000)}" # your custom request_id
# create withdraw transaction
response = mpc_client.create_transaction(
   coin=coin_code,
   request_id=request_id,
   amount=amount,
   from_addr=from_address,
   to_addr=to_address,
   gas_price=6500000000,
   gas_limit=21000,
)
print(f"Withdraw: {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.transaction.MPCPostTransaction;

public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
        String apiSecret = "your_api_secret"; // your wallet api secret

        // init client
        CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newMPCRestClient();

        // create withdraw transaction
        String coin = "GETH";
        String requestId = String.valueOf(System.currentTimeMillis());
        String fromAddr = "your mpc wallet address";
        String toAddr = "to address";
        BigInteger amount = new BigInteger("100000000000000000");
        String toAddressDetails = null;
        BigDecimal fee = null;
        BigInteger gasPrice = new BigInteger("6500000000");
        BigInteger gasLimit = new BigInteger("21000");
        Integer operation = null;
        String extraParameters = null;
        ApiResponse<MPCPostTransaction> res = mpcClient.createTransaction(coin, requestId, amount, fromAddr, toAddr,
                toAddressDetails, fee, gasPrice, gasLimit, operation, extraParameters, null, null, null, null);
        System.out.println("Withdraw: " + res.getResult());
    }
}
```
</CodeGroup><br />
#### Interacting with a smart contract using Web3 Wallets

Step 1: Initialize the mpc_client

Step 2: Pass in calldata to call a smart contract

<CodeGroup>
```python Python
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEVELOP_ENV
from cobo_custody.signer.local_signer import LocalSigner
import time

api_secret = "your api secret"  # your wallet api secret
chain_code = "ETH"  # your target chain
coin_code = "ETH"  # your target coin
amount = "0"
from_address = "your mpc wallet address"  # your mpc wallet address
to_address = "contract address"  # contract address
extra_parameters = '{"calldata": "0xa8059cbb000000000000000000000000971948873f869e4517311b190d7eb31e30eba0ef000000000000000000000000000000000000000000000000002386f26fc10000"}' # use calldata to call or deploy contract

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEVELOP_ENV, debug=False)

request_id = f"MPCTransaction-{int(time.time() * 1000)}" # your custom request_id

response = mpc_client.create_transaction(
   coin=coin_code,
   request_id=request_id,
   amount=amount,
   from_addr=from_address,
   to_addr=to_address,
   gas_price=6500000000,
   gas_limit=22385,
   extra_parameters=extra_parameters,
)
print(f"call contract: {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.transaction.MPCPostTransaction;

public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
        String apiSecret = "your_api_secret"; // your wallet api secret

        // init client
        CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newMPCRestClient();

        // create withdraw transaction
        String coin = "GETH";
        String requestId = String.valueOf(System.currentTimeMillis());
        String fromAddr = "your mpc wallet address";
        String toAddr = "contract address";
        BigInteger amount = new BigInteger("0");
        String toAddressDetails = null;
        BigDecimal fee = null;
        BigInteger gasPrice = new BigInteger("6500000000");
        BigInteger gasLimit = new BigInteger("22385");
        Integer operation = null;
        String extraParameters = "{\"calldata\": \"0xa8059cbb000000000000000000000000971948873f869e4517311b190d7eb31e30eba0ef000000000000000000000000000000000000000000000000002386f26fc10000\"}"

        ApiResponse<MPCPostTransaction> res = mpcClient.createTransaction(coin, requestId, amount, fromAddr, toAddr,
                toAddressDetails, fee, gasPrice, gasLimit, operation, extraParameters, null, null, null, null);
        System.out.println("call contract: " + res.getResult());
    }
}
```
</CodeGroup><br />
For more information on the 'create_transaction' endpoint, please [click here](/v1/api-references/mpc-wallet/mpc_create_transaction).
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>