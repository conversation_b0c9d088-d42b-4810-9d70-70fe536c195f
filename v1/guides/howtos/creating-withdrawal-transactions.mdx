---
title: "How to Create a Withdraw Transaction via APIs"
sidebarTitle: "Creating Withdraw Transactions"
"og:title": "How to Create Withdraw Transaction via APIs"
"og:description": "Learn how to securely create crypto withdrawals through our APIs. Explore code samples in programming languages such as Python and Java. "
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

Withdraw transactions in MPC Wallets and Custodial Wallets follow distinct creation processes via APIs.
In MPC Wallets, kindly take note of the distinctions between transactions created on Account-based blockchains and those on UTXO-based blockchains.

### Code Samples for MPC Wallets

The following code samples focus on Account-based blockchains.
For information on how to create a transaction on UTXO-based blockchains, please refer to this [guide](/v1/guides/howtos/creating-utxo-transactions).

The following codes demonstrate the transfer of 0.1 ETH to a specified receiving address via Account-based blockchains.

<CodeGroup>
```python Python
from cobo_custody.client.mpc_client import MP<PERSON>lient
from cobo_custody.config import DEVELOP_ENV
from cobo_custody.signer.local_signer import LocalSigner
import time

api_secret = "your api secret"  # your wallet api secret
chain_code = "ETH"  # your target chain
coin_code = "ETH"  # your target coin
amount = "100000000000000000"  # withdraw amount：0.01ETH
from_address = "your mpc wallet address"  # your mpc wallet address
to_address = "your address"  # your address

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEVELOP_ENV, debug=False)

request_id = f"MPCTransaction-{int(time.time() * 1000)}"  # your custom request_id
response = mpc_client.create_transaction(
   coin=coin_code,
   request_id=request_id,
   amount=amount,
   from_addr=from_address,
   to_addr=to_address,
   gas_price=**********,
   gas_limit=21000,
)
print(f"Withdraw: {response.result}")

```
```java Java
package com.cobo.custody.api.client.impl;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.transaction.MPCPostTransaction;

import java.math.BigInteger;
public class CobоCustodyApiClientExample {
   private static CoboMPCApiRestClient mpcClient;
   public static void main(String[] args) {
       String apiSecret = "your_api_secret_here";


       mpcClient = CoboApiClientFactory.newInstance(
               new LocalSigner(apiSecret),
               Env.DEVELOP,
               false).newMPCRestClient();

       String coin = "ETH";
       String requestId = String.valueOf(System.currentTimeMillis());
       String fromAddr = "******************************************";
       String toAddr = "******************************************";
       BigInteger amount = new BigInteger("100000000000000000");
       String toAddressDetails = null;
       BigInteger fee = null;
       BigInteger gasPrice = new BigInteger("**********");
       BigInteger gasLimit = new BigInteger("21000");
       Integer operation = null;
       String extraParameters = null;
       ApiResponse<MPCPostTransaction> response = mpcClient.createTransaction(
               coin, requestId, fromAddr,toAddr, amount, toAddressDetails, gasPrice, gasLimit, fee, operation, extraParameters
       );

       System.out.println(response.getResult());
   }
}

```
</CodeGroup>

For more information on the "mpc_create_transaction" endpoint, [click here](/v1/api-references/mpc-wallet/mpc_create_transaction).

### Code Samples for Custodial Wallets

In Custodial Wallets, an on-chain transaction will be triggered if the receiving address is an external address.
Conversely, if the receiving address belongs to a Custodial Wallet managed by Cobo,
the transaction will be executed off-chain without incurring any gas fees.

<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEVELOP_ENV
from cobo_custody.signer.local_signer import LocalSigner
import time

api_secret = "your_api_secret"  # your wallet api secret
chain_code = "ETH"  # your target chain
coin_code = "ETH"  # your target coin
amount = 100000000000000000  # withdraw amount
to_address = "your address"  # your address

# init cobo client
client = Client(signer=LocalSigner(api_secret), env=DEVELOP_ENV, debug=False)

request_id = f"ApiTransaction-{int(time.time() * 1000)}"    # your custom request_id
response = client.withdraw(
   coin=coin_code,
   request_id=request_id,
   amount=amount,
   address=to_address,
)
print(f"Withdraw: {response.result}")

```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigInteger;
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;

public class CoboApiExample {
public static void main(String[] args) {
String apiSecret = "your_api_secret"; // your wallet api secret
String coin = "ETH"; // your target coin
String requestId = String.valueOf(System.currentTimeMillis()); // your custom request_id
String toAddr = "your_address"; // your address
BigInteger withdraw_amount = new BigInteger("100000000000000000"); // withdraw amount

// init cobo client
CoboApiRestClient client = CoboApiClientFactory.newInstance(
new LocalSigner(apiSecret),
Env.DEVELOP,
false).newRestClient();

ApiResponse<String> withdraw = client.withdraw(
coin,
requestId,
toAddr,
withdraw_amount,
null,
null,
null
);
System.out.println("response data: withdraw" + withdraw.getResult());


}
}

```
</CodeGroup>
For more information on the "new_withdraw_request" endpoint, [click here](/v1/api-references/custody-wallet/new_withdraw_request).

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>