---
title: "How to Configure API Callback "
sidebarTitle: "Configuring API Callback "
"og:title": "Configuring API Callback"
"og:description": "Explore the workflow to set up API callback service on Cobo. Learn best security practices for deploying your API server. "
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

API callback is a service provided by Cobo to its clients, delivering push messages to a client-designated callback URL when an event is triggered (e.g., a new deposit or withdraw).
API callback is commonly used for event-driven architectures and is particularly useful when immediate responses are needed.
To use API callback on Cobo, the client will need to first set up the API callback service and configure a specific URL on the Cobo Custody Web.
Currently, Cobo supports two types of API callback: Transaction Notification and Withdraw Confirmation.

**Transaction Notification:** When a deposit/withdraw transaction is scanned on chain by Co<PERSON>, it will send a push message to an URL specified by the client.
This serves to provide clients with real-time updates on all transactions and aid in transaction reconciliation. For more information, please [click here](/v1/api-references/development/transaction-notification).

**Withdraw Confirmation:** <PERSON><PERSON> will send a push message to the callback URL using the POST method, containing withdraw details.
Clients are required to respond to Cobo in the RESPONSE format, indicating either 'OK' or 'Deny' in the message body. If no response or a 'Deny' response is provided, the withdraw request will be rejected.
For more information, please [click here](/v1/api-references/development/withdrawal-confirmation).

The following provides instructions on configuring the API callback service, along with code samples for callback endpoints.


<Note>
1. Make sure to modify the coboPubKey, URL, and customCheck based on your business needs.
2. This demo sets up an HTTP server that can receive POST requests from Cobo.
</Note>



```java Java
package com.cobo.callback.controller;

import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.impl.LocalSigner;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.lang3.StringUtils;

@RestController
public class CallbackController {
   // Configure coboPub based on your operating environment (i.e., development or production environment)
   private static final String coboPubKey = Env.DEV.coboPub;

   @RequestMapping(value="/custody_callback", method = RequestMethod.POST)
   public static String custodyCallback(@RequestHeader("Biz-Timestamp") String timestamp,
                                                        @RequestHeader("Biz-Resp-Signature") String signature,
                                                        @RequestBody String body) {
       boolean verifyResult = false;
       try {
           if (!StringUtils.isEmpty(timestamp) && !StringUtils.isEmpty(signature)) {
               String content = body + "|" + timestamp;
               verifyResult = LocalSigner.verifyEcdsaSignature(content, signature, coboPubKey);
           }
       } catch (Exception e) {
           e.fillInStackTrace();
       }

       verifyResult &= customCheck(body);

       return verifyResult ? "ok" : "deny";
   }

   public static boolean customCheck(String body) {
       //add you checking policy
       return true;
   }
}
```

#### Dependency configurations

```java Java
plugins {
  id 'java'
  id 'org.springframework.boot' version '3.1.4'
  id 'io.spring.dependency-management' version '1.1.3'
}

group = 'com.cobo'
version = '0.0.1-SNAPSHOT'

java {
  sourceCompatibility = '17'
}

repositories {
  mavenCentral()
  maven { url 'https://jitpack.io' }
}

dependencies {
  implementation 'org.springframework.boot:spring-boot-starter'
  implementation 'org.springframework.boot:spring-boot-starter-web:3.1.4'
  implementation 'org.apache.commons:commons-lang3:3.12.0'
  implementation 'com.github.CoboGlobal:cobo-java-api:v0.62'
  implementation "org.slf4j:slf4j-simple:1.7.9"
  testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

tasks.named('test') {
  useJUnitPlatform()
}

```

Security recommendations for configuring and deploying a callback server:

1. HTTPS: Configure the callback server to use HTTPS, and employ the SSL/TLS handshake to establish an encrypted link between the client and server to enable secure data transmission.
2. Callback verification: Ensure that you only accept callback requests from trusted sources. You can employ token validation, IP whitelisting, or other methods to verify if requests originate from legitimate sources.
3. Firewall and security group rules: Set up firewall rules on your cloud server or network devices to allow inbound and outbound callback traffic. Only permit specific ports and IP ranges to access the callback server.
4. Logging: Enable detailed logging on the callback server to track and analyze the history of callback requests. This is instrumental for troubleshooting and monitoring system performance.
5. Timeout and retry policies: Define appropriate timeout and retry policies for callback requests to ensure proper handling and retries in case of network issues or server unavailability.
6. Security: Ensure that the operating system, web server, and applications running on your server are up-to-date, with the latest security updates and patches installed.
7. Monitoring and alerts: Configure server monitoring to detect the status and performance of the callback server. In the event of failures or anomalies, set up alerts to promptly notify your operations team.
8. Fault Tolerance: Ensure that the callback server can tolerate and appropriately handle duplicate callback requests to prevent data duplication or other issues.
9. Documentation and notifications: Provide documentation or notifications to enable the initiator of callback requests to understand how to correctly construct and handle callback requests.
10. Disaster recovery and backup: Configure a disaster recovery or backup server to ensure that services continue to operate even if the primary callback server experiences a failure.
11. Compliance: Ensure that your callback server complies with applicable regulations and legal requirements, especially those related to data privacy and security.

Cobo API callback signature

To enhance security, Cobo will apply ECDSA signatures to every RESPONSE message. For more information, please [click here](/v1/api-references/development/callback-signature).
To authenticate the signature, you are required to retrieve the corresponding pubkey by navigating to the "API Callback" section on the Cobo Custody Web.

<img src="/v1/images/callback.png" />

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>