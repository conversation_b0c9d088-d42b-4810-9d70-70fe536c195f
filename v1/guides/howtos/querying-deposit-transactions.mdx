---
title: "Querying Deposit Transactions "
sidebarTitle: "Querying Deposit Transactions"
"og:title": "Querying Deposit Transactions"
"og:description": "Learn how to query crypto transaction details through API callbacks and API endpoints. Explore code samples in Python, Java, and more. "
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

Deposit transactions refer to transactions initiated by third parties with the target address being an address under Cobo’s Custodial Wallets or MPC Wallets.
Currently, Cobo provides two methods for clients to query about their deposit details.

- Transaction Notification: When Cobo receives a notification of a successful deposit transaction, it will send an HTTP request to a URL designated by the client.
You can query the transaction details in this push message.

- API Querying: This method allows you to query transaction details via API endpoints.
You can opt to traverse/iterate over a collection of data or specify a parameter for querying.

Below are code samples to show you how to query deposit transaction details by time and use the outputs as the most recent deposit record.

#### Code Samples for MPC Wallets:


<CodeGroup>
```python Python
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
from cobo_custody.client.mpc_client import MPCClient

api_secret = "your api secret"  # your wallet api secret

# init cobo client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

response = mpc_client.list_transactions(transaction_type=1000, order_by="created_time", order="DESC")
print(f"Get Transactions By Time: {response.result}")

```
```java Java
package com.cobo.custody.api.client.impl;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.transaction.MPCTransactions;

public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret

    // init client
    CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
    new LocalSigner(apiSecret),
    Env.DEV,
    false).newMPCRestClient();

    // get deposit transaction
    ApiResponse<MPCTransactions> listTransactionsResponse = mpcClient.listTransactions(null, null, null, "created_time", "DESC", 1000, null, null, null, 1);
    System.out.println("Deposit Transactions: " + listTransactionsResponse.getResult());
    }
}

```
</CodeGroup>

#### Code Samples for Custodial Wallets:
<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

api_secret = "your_api_secret"  # your wallet api secret
# init cobo client
client = Client(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# get deposit transaction
response = client.get_transactions_by_time(side="deposit")
print(f"Get Transactions By Time: {response.result}")

```
```java Java
package com.cobo.custody.api.client.impl;

import java.util.List;
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.transaction.Side;
import com.cobo.custody.api.client.domain.transaction.Transaction;

public class CoboApiExample {
    public static void main(String[] args) {
    String apiSecret = "your_api_secret"; // your wallet api secret

    // init cobo client
    CoboApiRestClient client = CoboApiClientFactory.newInstance(
        new LocalSigner(apiSecret),
        Env.DEV,
        false).newRestClient();

    // get deposit transaction
    ApiResponse<List<Transaction>> getTransactionsByTime = client.getTransactionsByTime(null, Side.Deposit, null, 0, 0, 1, null);
    System.out.println("getTransactionsByTime: " + getTransactionsByTime.getResult());
    }
}

```
</CodeGroup>

#### Things to Note

1. The deposit processes over account-based blockchains and UTXO-based blockchains are similar.
2. Deposit transactions include those occurring under Custodial Wallets and MPC Wallets.
3. For Custodial Wallets, the minimum deposit amounts vary for different coins.
The specific values can be obtained in the 'minimum_deposit_threshold' field returned by the ['Get Coin Details'](/v1/api-references/custody-wallet/coin_info) endpoint.
**If your deposit amount is below this specified value, the coins will not be credited to your account.**
If the returned value is zero, deposits of any amount will be credited.
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>