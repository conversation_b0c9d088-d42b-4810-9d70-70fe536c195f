---
title: Get Transaction Details
api: "GET /v1/custody/transaction/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint retrieves a transaction by its ID from the WaaS API. The endpoint requires authentication and takes a single query parameter, 'id', which is the ID of the transaction to retrieve.
---
<Note>Only successful deposit/withdraw requests will be considered as 'Transaction' here</Note>

#### Request
<ParamField query="id" type="String" required>Cobo Unique Transaction ID</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
    <Expandable title="object">
        <Snippet file="full_custodian_transaction_snippet.mdx" />
    </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  "GET" " /v1/custody/transaction/ ",
  {
    "id": "20210112173820000166329000003582",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch(
    "GET"
    " /v1/custody/transaction/ "
    {
        "id": "20210112173820000166329000003582",

    },
    api_key, api_secret, host
).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
request(
    "GET"
    " /v1/custody/transaction/ "
    {
        "id": "20210112173820000166329000003582",

    })
```

</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "id": "20210112173820000166329000003582",
    "coin": "BTC",
    "display_code": "BTC",
    "description": "Bitcoin",
    "decimal": 8,
    "address": "**********************************",
    "source_address": "******************************************",
    "side": "withdraw",
    "amount": "80000",
    "abs_amount": "0.0008",
    "txid": "239861be9a4afe080c359b7fe4a1d035945ec46256b1a0f44d1267c71de8ec28",
    "vout_n": 0,
    "request_id": "web_send_by_user_327_1610444045047",
    "status": "success",
    "abs_cobo_fee": "0",
    "request_created_time": 1610444045158,
    "created_time": 1610445878970,
    "last_time": 1610445878970,
    "confirmed_num": 3,
    "remark":"hello test",
    "tx_detail": {
      "txid": "239861be9a4afe080c359b7fe4a1d035945ec46256b1a0f44d1267c71de8ec28",
      "blocknum": 665711,
      "blockhash": "00000000000000000007137dde44472364eb80126b7d98c12ffb8d37ba11ad33",
      "fee": 0,
      "actualgas": 17812,
      "gasprice": 1,
      "hexstr": ""
    },
    "source_address_detail": "******************************************",
    "memo": "",
    "confirming_threshold": 3,
    "fee_coin": "BTC",
    "fee_amount": 50000,
    "fee_decimal": 8,
    "type": "external",
    "tx_request_type": ""
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>