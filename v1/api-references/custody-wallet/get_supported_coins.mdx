---
title: Get Supported Coins
api: "GET /v1/custody/get_supported_coins/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with a list of supported coins for the WaaS API.
---

<Tip> FAQ：[How do I query the minimum deposit amount for a specific coin?](/v1/faqs/coin-management#how-do-i-query-the-minimum-deposit-amount-for-a-specific-coin)</Tip>


#### Request

None

#### Response

<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
<Expandable title="object">
<ResponseField name="coin" type="String">The coin symbol.</ResponseField>
<ResponseField name="display_code" type="String">Coin ticker symbol (not unique, changeable, for reference only )</ResponseField>
<ResponseField name="description" type="Int">Full name of coin (not unique, changeable, for reference only )</ResponseField>
<ResponseField name="decimal" type="Int">Coin balance decimal places</ResponseField>
<ResponseField name="can_deposit" type="Bool">Deposit accessibility</ResponseField>
<ResponseField name="can_withdraw" type="Bool">Withdraw accessibility</ResponseField>
<ResponseField name="require_memo" type="Bool">Memo for specified coins(EOS,XLM,XRP,IOST)</ResponseField>
<ResponseField name="minimum_deposit_threshold" type="String">Coin minimum deposit threshold</ResponseField>
</Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/get_supported_coins/",{},
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch(
  "GET",
  "/v1/custody/mpc/get_supported_chains/",
  {},
  api_key,
  api_secret,
  host
)
  .then((res) => {
    res.json().then((data) => {
      console.log(JSON.stringify(data, null, 4));
    });
  })
  .catch((err) => {
    console.log(err);
  });
```

```go Go
Request("GET", "/v1/custody/get_supported_coins/", map[string]string{})
```

</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
      "coin": "ETH",
      "display_code": "ETH",
      "description": "Ethereum",
      "decimal": 18,
      "can_deposit": true,
      "can_withdraw": true,
      "require_memo": false,
      "minimum_deposit_threshold": "0"

    },
    {
      "coin": "BTC",
      "display_code": "BTC",
      "description": "Bitcoin",
      "decimal": 8,
      "can_deposit": true,
      "can_withdraw": true,
      "require_memo": false,
      "minimum_deposit_threshold": "0"
    }

]
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>