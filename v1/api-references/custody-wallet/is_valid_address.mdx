---
title: Verify Valid Address
api: "GET /v1/custody/is_valid_address/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint checks whether a given cryptocurrency address is valid and returns a boolean value. It also supports memo for certain coins. Authentication is required to access this endpoint.
---

<Tip> FAQ：[How to verify whether a withdraw address is legitimate?](/v1/faqs/address-management#how-to-verify-whether-a-withdrawal-address-is-legitimate)</Tip>

#### Request
<ParamField query="coin" type="String" required>The cryptocurrency symbol.</ParamField>
<ParamField query="address" type="String" required>The cryptocurrency address.(Address|Memo)</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="bool">Whether the address is valid or not.</ResponseField>

<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/is_valid_address/",
  {
    "coin": "ETH",
    "address": "******************************************",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/is_valid_address/',
        {
            "coin": "ETH",
            "address": "******************************************",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/is_valid_address/", map[string]string{
    "coin": "ETH",
    "address": "******************************************",
})
```
</RequestExample>


<ResponseExample>
```json
{
  "success": true,
  "result": true
}
----
{
  "success": true,
  "result": false
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>