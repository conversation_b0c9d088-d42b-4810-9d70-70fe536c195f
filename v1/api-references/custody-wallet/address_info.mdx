---
title: Get Address Info
api: "GET /v1/custody/address_info/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns information about a given address for a specific coin. It checks if the address belongs to the custody wallet and raises an exception if it doesn't. The response is a JSON object containing the coin and address fields.
---


#### Request

<ParamField query="coin" type="String" required>The coin symbol.</ParamField>
<ParamField query="address" type="String" required>The address to retrieve information for.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
   <Expandable title="object">
        <ResponseField name="coin" type="String">The coin symbol.</ResponseField>
        <ResponseField name="address" type="String">The address to retrieve information for.</ResponseField>
    </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/address_info/",
  {
    "coin": "ETH",
    "address": "******************************************",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/address_info/',
        {
            "coin": "ETH",
            "address": "******************************************",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/address_info/", map[string]string{
    "coin": "ETH",
    "address": "******************************************",
})
```

</RequestExample>


<ResponseExample>
```json
{
  "success": true,
  "result": {
    "coin": "ETH",
    "address": "******************************************"
  }
}
----
{
  "success": false,
  "error_code": 12015,
  "error_description": "****************************************** is not Cobo Address of Coin ETH"
}
```
</ResponseExample>


<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>