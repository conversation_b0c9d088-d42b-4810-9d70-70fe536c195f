---
title: Withdraw to Investor's Wallet
api: POST /v1/custody/trading_withdraw/ 
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows users to withdraw funds from their exchange account. The user must provide their exchange account token, the coin they wish to withdraw, the amount they wish to withdraw, and a unique request ID. The endpoint returns a JSON response with information about the withdraw, including the request ID, the coin, the absolute amount withdrawn, the estimated amount received, the withdraw status, the absolute fee, and the fee.
---

#### Request
<ParamField body="exchange_account_token" type="String" required>from exchange account token (aquire from Web)</ParamField>
<ParamField body="coin" type="String" required>Coin code</ParamField>
<ParamField body="amount" type="Int" required>Int amount (e.g. if 1 BTC is to be withdrawn, the amount should be multiplied by 100,000,000 (<PERSON><PERSON><PERSON>))</ParamField>
<ParamField body="request_id" type="String" required>Request ID (Universally unique ID for each request)</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
    <Expandable title="object">
    <ResponseField name="request_id" type="String" >Request ID (Universally unique ID for each request)</ResponseField>
    <ResponseField name="coin" type="String" >Coin code</ResponseField>
    <ResponseField name="amount" type="Int" >Int amount contains decimals (e.g. if 1 BTC is to be withdrawn, the amount should be multiplied by 100,000,000 (Satoshis))</ResponseField>
    <ResponseField name="abs_amount" type="String" >Absolute amount. If you trade 1.5 BTC, then the abs_amount is 1.5</ResponseField>
    <ResponseField name="status" type="String" >Status: ok, pending, failed, human_check</ResponseField>
    <ResponseField name="fee" type="Int" >Fee amount (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>
    <ResponseField name="abs_fee" type="String" >Absolute fee value. For examle, abs_cobo_fee 0.00005 means exactly 0.00005BTC</ResponseField>
    <ResponseField name="estimated_amount_received" type="Int" >Estimated amount received (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/trading_withdraw/",
    {
        "exchange_account_token": "token",
        "coin": "BTC",
        "amount": "*********",
        "request_id": "UNIQUE_ID_FOR_WITHDRAW"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/trading_withdraw/',
        {
            "exchange_account_token": "token",
            "coin": "BTC",
            "amount": "*********",
            "request_id": "UNIQUE_ID_FOR_WITHDRAW"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/trading_withdraw/", map[string]string{
    "exchange_account_token": "token",
    "coin": "BTC",
    "amount": "*********",
    "request_id": "UNIQUE_ID_FOR_WITHDRAW"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "request_id": "UNIQUE_ID_FOR_WITHDRAW",
    "coin": "BTC",
    "amount": *********,
    "abs_amount": "1",
    "status": "ok",
    "fee": 500000,
    "abs_fee": "0.0005",
    "estimated_amount_received": "********"
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>