---
title: Get Trading Transfer Information
api: GET /v1/custody/trading_transfer_info/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns information about a trading transfer request. It requires a 'request_id' parameter in the query string. The response contains details such as the coin, amount, fee, and status of the transfer request.
---
#### Request
<ParamField query="request_id" type="String" required>Request ID (Universally unique ID for each request)</ParamField>

#### Response
<ResponseField name="request_id" type="String" >Request ID (Universally unique ID for each request)</ResponseField>
<ResponseField name="coin" type="String" >Coin code</ResponseField>
<ResponseField name="amount" type="Int" >Int amount contains decimals (e.g. if 1 BTC is to be deposited, the amount should be multiplied by 100,000,000 (Satoshis))</ResponseField>
<ResponseField name="abs_amount" type="String" >Absolute amount. If you trade 1.5 BTC, then the abs_amount is 1.5</ResponseField>
<ResponseField name="status" type="String" >Status: ok, pending, failed, human_check</ResponseField>
<ResponseField name="fee" type="Int" >Fee amount (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>
<ResponseField name="abs_fee" type="String" >Absolute fee value. For examle, abs_cobo_fee 0.00005 means exactly 0.00005BTC</ResponseField>
<ResponseField name="estimated_amount_received" type="Int" >Estimated amount received (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/trading_transfer_info/",
    {
        "request_id": "UNIQUE_ID_FOR_TRANSFER"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/trading_transfer_info/',
        {
            "request_id": "UNIQUE_ID_FOR_TRANSFER"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/trading_transfer_info/", map[string]string{
    "request_id": "UNIQUE_ID_FOR_TRANSFER"
})
```
</RequestExample>

<ResponseExample>

</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>