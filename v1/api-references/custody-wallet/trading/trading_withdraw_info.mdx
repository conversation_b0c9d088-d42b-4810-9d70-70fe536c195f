---
title: Get Trading Withdraw Information
api: GET /v1/custody/trading_withdraw_info/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns information about a trading withdraw request. It requires a valid request ID and returns details such as the coin code, amount, fee, and status of the request. The response also includes the estimated amount received after deducting the fee.
---
#### Request
<ParamField query="request_id" type="String" required>Request ID (Universally unique ID for each request)</ParamField>

#### Response 
<ResponseField name="request_id" type="String" >Request ID (Universally unique ID for each request)</ResponseField>
<ResponseField name="coin" type="String" >Coin code</ResponseField>
<ResponseField name="amount" type="Int" >Int amount contains decimals (e.g. if 1 BTC is to be withdrawn, the amount should be multiplied by 100,000,000 (<PERSON><PERSON><PERSON>))</ResponseField>
<ResponseField name="abs_amount" type="String" >Absolute amount. If you trade 1.5 BTC, then the abs_amount is 1.5</ResponseField>
<ResponseField name="status" type="String" >Status: ok, pending, failed, human_check</ResponseField>
<ResponseField name="fee" type="Int" >Fee amount (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>
<ResponseField name="abs_fee" type="String" >Absolute fee value. For examle, abs_cobo_fee 0.00005 means exactly 0.00005BTC</ResponseField>
<ResponseField name="estimated_amount_received" type="Int" >Estimated amount received (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/trading_withdraw_info/",
    {
        "request_id": "UNIQUE_ID_FOR_WITHDRAW"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/trading_withdraw_info/',
        {
            "request_id": "UNIQUE_ID_FOR_WITHDRAW"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/trading_withdraw_info/", map[string]string{
    "request_id": "UNIQUE_ID_FOR_WITHDRAW"
})
```
</RequestExample>

<ResponseExample>

</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>