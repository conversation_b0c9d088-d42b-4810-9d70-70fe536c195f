---
title: Get Transactions By time and offset
api: "GET /v1/custody/transactions_by_time_ex/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of transactions filtered by time range, transaction status, coins, transaction side (deposit/withdraw) and address. As the extension of ***[Get Transactions By Time](https://www.cobo.com/developers/v1/api-references/custody-wallet/transactions_by_time)***, this endpoint introduced a new 'offset' parameter used for performing pagination query of transactions. The response includes transaction details such as transaction hash, amount, and timestamp.
---

#### Request
<ParamField query="coins" type="String" >Coin codes. Separated by commas</ParamField>
<ParamField query="side" type="Int" >1: TRANSACTION_DEPOSIT; 2: TRANSACTION_WITHDRAW </ParamField>
<ParamField query="status" type="Int" >
  | Status Type | Code |
  | ----------- | ----------- |
  | PENDING_APPROVAL| 101|
  | SENT| 201|
  | PENDING_CONFIRMATION| 501|
  | SUCCESS| 900|
  | FAILED| 901|
</ParamField>
<ParamField query="address" type="String" >Deposit address</ParamField>
<ParamField query="begin_time" type="Long" >Begin timestamp(milliseconds). If set, transactions whose transaction created time is greater than or equal to this will be returned.</ParamField>
<ParamField query="end_time" type="Long" >End timestamp (milliseconds). If set, the transactions whose created time is less than this will be returned.</ParamField>
<ParamField query="limit" type="Integer" >Page size. If not set, the default size will be 50, and the maximum size will also be 50.</ParamField>
<ParamField query="offset" type="Integer" >Offset specifies the starting index for the current query, indicating the number of transactions to skip before fetching and returning the transactions in the result.</ParamField>
<ParamField query="order_by" type="String" >Sorting method. Default: created_time; other option: last_time</ParamField>
<ParamField query="order" type="String" >Sorting order. Options: ASC (default), DESC</ParamField>
<ParamField query="txid" type="String" >Transaction ID</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
  <Expandable title="object">
        <Snippet file="full_custodian_transaction_snippet.mdx" />
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  'GET',
  '/v1/custody/transactions_by_time_ex/',
  {
    "coins": "COBO_HNS",
    "side": 2,
    "status": 900,
    "begin_time": 1699249938000,
    "limit": 10,
    "offset": 0,
    "order": "DESC",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/transactions_by_time_ex/',
        {
            "coins": "COBO_HNS",
            "side": 2,
            "status": 900,
            "begin_time": 1699249938000,
            "limit": 10,
            "offset": 0,
            "order": "DESC",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/transactions_by_time_ex/", map[string]string{
    "coins": "COBO_HNS",
    "side": 2,
    "status": 900,
    "begin_time": 1699249938000,
    "limit": 10,
    "offset": 0,
    "order": "DESC",
})
```
</RequestExample>


<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
        "id": "20231229164409000331925000008218",
        "coin": "COBO_HNS",
        "display_code": "COBO_HNS",
        "description": "",
        "decimal": 6,
        "address": "rs1qsppf0ypx5lmjepc5s55rqwdlefk9n2saglxzm6",
        "source_address": "rs1qt8f7pjuc6de5ljgtm96l2gkl4x7pq497j8qdn3",
        "side": "withdraw",
        "amount": "500",
        "abs_amount": "0.0005",
        "txid": "7d4c49b653633f65cff08932bab41e2fe594b3d90c7f1978d9e4a0eb678b7468",
        "vout_n": 0,
        "request_id": "IntegrationTest-854963652255465838",
        "status": "success",
        "abs_cobo_fee": "0",
        "created_time": 1703839219000,
        "last_time": 1703839450000,
        "confirmed_num": 2,
        "remark": "",
        "tx_detail": {
            "txid": "7d4c49b653633f65cff08932bab41e2fe594b3d90c7f1978d9e4a0eb678b7468",
            "blocknum": 1199326,
            "blockhash": "7cc460715566bd2fe2a545902ec8a52ac6ae79f28331bf58d6e07256bbb550ca",
            "fee": 0,
            "actualgas": 250000,
            "gasprice": 1,
            "hexstr": ""
        },
        "source_address_detail": "rs1qt8f7pjuc6de5ljgtm96l2gkl4x7pq497j8qdn3",
        "memo": "",
        "confirming_threshold": 2,
        "fee_coin": "COBO_HNS",
        "fee_amount": 550000,
        "fee_decimal": 6,
        "type": "external"
    },
    {
        "id": "20231229161638000343969000005740",
        "coin": "COBO_HNS",
        "display_code": "COBO_HNS",
        "description": "",
        "decimal": 6,
        "address": "rs1qsppf0ypx5lmjepc5s55rqwdlefk9n2saglxzm6",
        "source_address": "rs1q5gqkxzd6g02csmv9m22450p6hw5sqmrptuanjy",
        "side": "withdraw",
        "amount": "500",
        "abs_amount": "0.0005",
        "txid": "26316634afed164e24418bf2e01daf0466407ddf14de2b928a369b5184e1fba1",
        "vout_n": 0,
        "request_id": "IntegrationTest-854957104955502882",
        "status": "success",
        "abs_cobo_fee": "0",
        "created_time": 1703837659000,
        "last_time": 1703837798000,
        "confirmed_num": 2,
        "remark": "",
        "tx_detail": {
            "txid": "26316634afed164e24418bf2e01daf0466407ddf14de2b928a369b5184e1fba1",
            "blocknum": 1199299,
            "blockhash": "1a280b2d298ed217c8a4f9dbe42ae93e70c286bbe25350d9d70009a66779deb1",
            "fee": 0,
            "actualgas": 250000,
            "gasprice": 1,
            "hexstr": ""
        },
        "source_address_detail": "rs1q5gqkxzd6g02csmv9m22450p6hw5sqmrptuanjy",
        "memo": "",
        "confirming_threshold": 2,
        "fee_coin": "COBO_HNS",
        "fee_amount": 550000,
        "fee_decimal": 6,
        "type": "external"
    }
  ]
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>