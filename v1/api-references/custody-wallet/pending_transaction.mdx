---
title: 
api: GET /v1/custody/pending_transaction/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with the details of a pending transaction specified by its ID.
---

<Note>it's possible that there may be no pending transactions for various reasons.
      Firstly, it depends on whether the customer has selected the option to receive callbacks for pending transactions.
      Additionally, it's possible that the blockchain processing speed for this particular cryptocurrency is fast,so transactions are confirmed without going through a pending state.</Note>

#### Request
<ParamField query="id" type="String" required>ID of the pending transaction.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
  <Expandable title="object">
         <Snippet file="full_custodian_transaction_snippet.mdx" />
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/pending_transaction/",
    {
        "id": "20181114112147059335000000359061"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/pending_transaction/',
        {
            "id": "20181114112147059335000000359061"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/pending_transaction/", map[string]string{
    "id": "20181114112147059335000000359061"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
        "id": "20181114112147059335000000359061",
        "coin": "XRP",
        "display_code": "XRP",
        "description": "Ripple",
        "address": "rfKyCMyoV6Ln2GZ7YDbrBrnXCbAyBbxRqB|1175609358",
        "source_address": "rfsEqW6MTpfsedoCNZ6DfDy8r8yFENvtjE",
        "side": "withdraw",
        "amount": "1",
        "decimal": 6,
        "abs_amount": "0.000001",
        "txid": "AB50566D8463CB11CE8CABC976D5A338EC5D86BE63FFC85E81CDA8B6B9918905",
        "vout_n": 0,
        "request_id": "IntegrationTest-575171218722709682",
        "status": "pending",
        "created_time": 1637333969545,
        "last_time": 1637333969545,
        "confirming_threshold": 1,
        "confirmed_num": 0,
        "waiting_audit": false,
        "remark": "",
        "tx_detail": {
            "txid": "AB50566D8463CB11CE8CABC976D5A338EC5D86BE63FFC85E81CDA8B6B9918905",
            "blocknum": 0,
            "blockhash": "",
            "fee": 0,
            "actualgas": 0,
            "gasprice": 1,
            "hexstr": ""
        },
        "source_address_detail": "rfsEqW6MTpfsedoCNZ6DfDy8r8yFENvtjE",
        "memo": "1175609358",
        "type": "external"
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>