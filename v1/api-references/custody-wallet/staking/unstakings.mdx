---
title: Get Unstaking Data
api: GET /v1/custody/unstakings/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with a list of unstaking and liquidating positions for the specified coin.
---
#### Request
<ParamField query="coin" type="String" >The coin to filter the positions by.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
  <Expandable title="object">
    <ResponseField name="coin" type="String" >The coin of the position.</ResponseField>
    <ResponseField name="coin_decimal" type="Int" >Coin decimal</ResponseField>
    <ResponseField name="amount" type="Int" >The amount of the position.</ResponseField>
    <ResponseField name="unstake_time" type="Int" >Unstake time(mileseconds timestamp)</ResponseField>
    <ResponseField name="liquidate_time" type="Int" >Liquidate time(mileseconds timestamp)</ResponseField>
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/unstakings/",
    {
        "coin": "ZEL",
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/unstakings/',
        {
            "coin": "ZEL",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/unstakings/", map[string]string{
    "coin": "ZEL",
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
      "coin": "ZEL",
      "coin_decimal": 8,
      "amount": 4000000000,
      "unstake_time": 1575388800000,
      "liquidate_time": 1575388800000
    },
    {},
    {}
  ]
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>