---
title: Get All Staking Product List
api: GET /v1/custody/staking_products/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of staking products available for a given coin and language. The response is a JSON array containing objects with information about each staking product, such as the name, annual yield, and minimum staking amount.
---
#### Request 

<ParamField query="coin" type="String" >The coin to filter staking products by.</ParamField>
<ParamField query="language" type="String" >The language to use for staking product names and descriptions.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
    <Expandable title="object">
        <ResponseField name="name" type="String" >Product name</ResponseField>
        <ResponseField name="description" type="String" >Product description</ResponseField>
        <ResponseField name="doc_src" type="String" >Product detailed description doc</ResponseField>
        <ResponseField name="coin" type="String" >Coin code</ResponseField>
        <ResponseField name="coin_decimal" type="Int" >Coin decimal</ResponseField>
        <ResponseField name="reward_coin" type="String" >Reward coin</ResponseField>
        <ResponseField name="reward_coin_decimal" type="Int" >Reward coin decimal</ResponseField>
        <ResponseField name="unstake_fee" type="String" >Unstake fee</ResponseField>
        <ResponseField name="min_amount" type="String" >Min. amount to stake</ResponseField>
        <ResponseField name="rate" type="String" >Yearly ROI</ResponseField>
        <ResponseField name="rate_type" type="Int" >1:fixed rate 2:estimated rate</ResponseField>
        <ResponseField name="days" type="Int" >Reward period</ResponseField>
        <ResponseField name="stake_type" type="String" >Staking type（Masternode, DPoS, etc）</ResponseField>
        <ResponseField name="lockup" type="Bool" >If there's a lockup period</ResponseField>
        <ResponseField name="start_stake_time" type="Int" >Start stake time(mileseconds timestamp)</ResponseField>
        <ResponseField name="stop_stake_time" type="Int" >Stop stake time(mileseconds timestamp)</ResponseField>
        <ResponseField name="start_staking_time" type="Int" >Start staking time(mileseconds timestamp)</ResponseField>
        <ResponseField name="stop_staking_time" type="Int" >Stop staking time(mileseconds timestamp)</ResponseField>
        <ResponseField name="liquidate_time" type="Int" >Liquidate time(mileseconds timestamp)</ResponseField>
        <ResponseField name="reward_liquidate_time" type="Int" >Reward liquidate time(mileseconds timestamp)</ResponseField>
        <ResponseField name="product_id" type="Int" >Product id(unique)</ResponseField>
        <ResponseField name="product_group" type="String" >product of group </ResponseField>
  </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/staking_products/",
    {
        "coin": "ZEL",
        "language": "zh",
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/staking_products/',
        {
            "coin": "ZEL",
            "language": "zh",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/staking_products/", map[string]string{
    "coin": "ZEL",
    "language": "zh",
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
      "name": " TETH PoS\u6d4b\u8bd5",
      "description": "TETH PoS test",
      "doc_src": "https://www.cobo.com",
      "coin": "TETH",
      "coin_decimal": 18,
      "reward_coin": "TETH",
      "reward_coin_decimal": 18,
      "unstake_fee": "0",
      "min_amount": "1000000",
      "rate": "0.2000000000",
      "rate_type": 1,
      "days": 0,
      "stake_type": "pos",
      "lockup": false,
      "start_stake_time": 1691388900000,
      "stop_stake_time": 1691391600000,
      "start_staking_time": 1691391600000,
      "stop_staking_time": 1691398800000,
      "liquidate_time": 1691398860000,
      "reward_liquidate_time": 1691398920000,
      "product_id": 236082,
      "product_group": " TETHPPSW"
    },
    {
      "name": "TETH PoS \u6d4b\u8bd51",
      "description": "TETH PoS \u6d4b\u8bd51",
      "doc_src": "https://www.cobo.com",
      "coin": "TETH",
      "coin_decimal": 18,
      "reward_coin": "TETH",
      "reward_coin_decimal": 18,
      "unstake_fee": "0",
      "min_amount": "1000000",
      "rate": "0.1200000000",
      "rate_type": 1,
      "days": 0,
      "stake_type": "pos",
      "lockup": false,
      "start_stake_time": 1691386800000,
      "stop_stake_time": 1691391600000,
      "start_staking_time": 1691391660000,
      "stop_staking_time": 1691398860000,
      "liquidate_time": 1691398920000,
      "reward_liquidate_time": 1691362980000,
      "product_id": 236081,
      "product_group": " TETHPPSW_1"
    }
  ]
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>