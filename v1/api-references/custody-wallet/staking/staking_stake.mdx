---
title: Stake
api: POST /v1/custody/staking_stake/ 
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows users to stake a certain amount of cryptocurrency on a particular product. The user must provide the product ID and the amount they wish to stake. The endpoint will then attempt to stake the specified amount on the specified product. If the product ID is invalid, an error will be returned.
---

#### Request
<ParamField body="product_id" type="Int" required>The ID of the product to stake on.</ParamField>
<ParamField body="amount" type="Int" required>The amount of cryptocurrency to stake.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="String" >Success is "", failed is null.</ResponseField>



<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/staking_stake/",
    {
        "product_id": 2751
        "amount": 4000000000
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/staking_stake/',
        {
            "product_id": 2751
            "amount": 4000000000
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/staking_stake/", map[string]string{
    "product_id": 2751
    "amount": 4000000000
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": true
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>