---
title: Get Staking Data
api: GET /v1/custody/stakings/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of staking information for a given coin and language. The response includes staking ID, coin, decimal, amount, reward coin, reward coin decimal, reward amount, and product information. The product information is parsed based on the language parameter.
---
#### Request
<ParamField query="coin" type="String" >The coin to query staking information for.</ParamField>
<ParamField query="language" type="String" >The language to parse product information in.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
  <Expandable title="object">
    <ResponseField name="staking_id" type="Int" >The ID of the staking.</ResponseField>
    <ResponseField name="coin" type="String" >The code of the staked coin.</ResponseField>
    <ResponseField name="coin_decimal" type="Int" >The decimal of the staked coin.</ResponseField>
    <ResponseField name="amount" type="Int" >The amount of the staked coin.</ResponseField>
    <ResponseField name="reward_coin" type="String" >The code of the reward coin.</ResponseField>
    <ResponseField name="reward_coin_decimal" type="Int" >The decimal of the reward coin.</ResponseField>
    <ResponseField name="reward_amount" type="String" >The amount of the reward coin.</ResponseField>
    <ResponseField name="product" type="object" >
        <Expandable title="object">
            <ResponseField name="name" type="String" >Product name</ResponseField>
            <ResponseField name="description" type="String" >Product description</ResponseField>
            <ResponseField name="doc_src" type="String" >Product detailed description doc</ResponseField>
            <ResponseField name="coin" type="String" >Coin code</ResponseField>
            <ResponseField name="coin_decimal" type="Int" >Coin decimal</ResponseField>
            <ResponseField name="reward_coin" type="String" >Reward coin</ResponseField>
            <ResponseField name="reward_coin_decimal" type="Int" >Reward coin decimal</ResponseField>
            <ResponseField name="unstake_fee" type="String" >Unstake fee</ResponseField>
            <ResponseField name="min_amount" type="String" >Min. amount to stake</ResponseField>
            <ResponseField name="rate" type="String" >Yearly ROI</ResponseField>
            <ResponseField name="rate_type" type="Int" >1:fixed rate 2:estimated rate</ResponseField>
            <ResponseField name="days" type="Int" >Reward period</ResponseField>
            <ResponseField name="stake_type" type="String" >Staking type（Masternode, DPoS, etc）</ResponseField>
            <ResponseField name="lockup" type="Bool" >If there's a lockup period</ResponseField>
            <ResponseField name="start_stake_time" type="Int" >Start stake time(mileseconds timestamp)</ResponseField>
            <ResponseField name="stop_stake_time" type="Int" >Stop stake time(mileseconds timestamp)</ResponseField>
            <ResponseField name="start_staking_time" type="Int" >Start staking time(mileseconds timestamp)</ResponseField>
            <ResponseField name="stop_staking_time" type="Int" >Stop staking time(mileseconds timestamp)</ResponseField>
            <ResponseField name="liquidate_time" type="Int" >Liquidate time(mileseconds timestamp)</ResponseField>
            <ResponseField name="reward_liquidate_time" type="Int" >Reward liquidate time(mileseconds timestamp)</ResponseField>
            <ResponseField name="product_id" type="Int" >Product id(unique)</ResponseField>
            <ResponseField name="product_group" type="String" >product of group </ResponseField>
        </Expandable>
    </ResponseField>
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/stakings/",
    {
        "coin": "ZEL",
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/stakings/',
        {
            "coin": "ZEL",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/stakings/", map[string]string{
    "coin": "ZEL",
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [
  {
    "staking_id": 57520,
    "coin": "TETH",
    "coin_decimal": 18,
    "amount": "23391613625225404",
    "reward_coin": "TETH",
    "reward_coin_decimal": 18,
    "reward_amount": 873919022232,
    "product": {
      "name": " TETH PoS\u6d4b\u8bd5",
      "description": "TETH PoS test",
      "doc_src": "https://www.cobo.com",
      "coin": "TETH",
      "coin_decimal": 18,
      "reward_coin": "TETH",
      "reward_coin_decimal": 18,
      "unstake_fee": "0",
      "min_amount": "1000000",
      "rate": "0.2000000000",
      "rate_type": 1,
      "days": 0,
      "stake_type": "pos",
      "lockup": false,
      "start_stake_time": 1691387100000,
      "stop_stake_time": 1691389800000,
      "start_staking_time": 1691389800000,
      "stop_staking_time": 1691397000000,
      "liquidate_time": 1691397060000,
      "reward_liquidate_time": 1691397120000,
      "product_id": 236080,
      "product_group": " TETHPPSW"
    }
  },
  {
    "staking_id": 57523,
    "coin": "TETH",
    "coin_decimal": 18,
    "amount": "1063172",
    "reward_coin": "TETH",
    "reward_coin_decimal": 18,
    "reward_amount": 28,
    "product": {
      "name": " TETH PoS\u6d4b\u8bd5",
      "description": "TETH PoS test",
      "doc_src": "https://www.cobo.com",
      "coin": "TETH",
      "coin_decimal": 18,
      "reward_coin": "TETH",
      "reward_coin_decimal": 18,
      "unstake_fee": "0",
      "min_amount": "1000000",
      "rate": "0.2000000000",
      "rate_type": 1,
      "days": 0,
      "stake_type": "pos",
      "lockup": false,
      "start_stake_time": 1691388900000,
      "stop_stake_time": 1691391600000,
      "start_staking_time": 1691391600000,
      "stop_staking_time": 1691398800000,
      "liquidate_time": 1691398860000,
      "reward_liquidate_time": 1691398920000,
      "product_id": 236082,
      "product_group": " TETHPPSW"
    }
  }
  ]
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>