---
title: Get Transactions By Request IDs
api: "GET /v1/custody/transactions_by_request_ids/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing transaction information for the given request IDs. The request IDs should be provided as a comma-separated string in the 'request_ids' parameter of the GET request. The maximum number of request IDs that can be provided is 50.
---

<Tip> FAQ：[How to query the transaction details of a batch withdraw request?](/v1/faqs/transaction-management#how-to-query-the-transaction-details-of-a-batch-withdrawal-request)</Tip>



#### Request
<ParamField query="request_ids" type="String" required>Request ID</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
  <Expandable title="object">
        <Snippet file="full_custodian_transaction_snippet.mdx" />
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/transactions_by_request_ids/",
  {
    "request_ids": "web_send_by_user_2045_1681202509548,IntegrationTest-758556063289348454"
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch("GET", "/v1/custody/transactions_by_request_ids/",
        {
            "request_ids": "web_send_by_user_2045_1681202509548,IntegrationTest-758556063289348454"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/transactions_by_request_ids/", map[string]string{
    "request_ids": "web_send_by_user_2045_1681202509548,IntegrationTest-758556063289348454"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
      "id": "20230410161529000336305000000596",
      "coin": "COBO_HNS",
      "display_code": "COBO_HNS",
      "description": "",
      "decimal": 6,
      "address": "rs1qhqr75h43kxuj4qn42rn2a7kgrehmrx79z67l85",
      "source_address": "rs1qt8f7pjuc6de5ljgtm96l2gkl4x7pq497j8qdn3",
      "side": "withdraw",
      "amount": "500",
      "abs_amount": "0.0005",
      "txid": "d9cf9cd81d8e74afd7638988fe0abe04874cf3dc0a91a92daee723a063efa648",
      "vout_n": 0,
      "request_id": "IntegrationTest-758556063289348454",
      "status": "success",
      "abs_cobo_fee": "0.000000025",
      "created_time": 1680853859732,
      "last_time": 1681114653298,
      "confirmed_num": 2,
      "remark":"hello test",
      "request_created_time": 1680853859732,
      "tx_detail": {
        "txid": "d9cf9cd81d8e74afd7638988fe0abe04874cf3dc0a91a92daee723a063efa648",
        "blocknum": 820583,
        "blockhash": "3cec854e701ae9c360f59a829f3089faaf24ce9ec4c7dad63494fcb8bc395d5c",
        "fee": 0,
        "actualgas": 250000,
        "gasprice": 1,
        "hexstr": ""
      },
      "source_address_detail": "rs1qt8f7pjuc6de5ljgtm96l2gkl4x7pq497j8qdn3",
      "memo": "",
      "confirming_threshold": 2,
      "fee_coin": "COBO_HNS",
      "fee_amount": 550000,
      "fee_decimal": 6,
      "type": "external",
      "tx_request_type": ""
    },
    {
      "id": "20230411164409000324843000007282",
      "coin": "COBO_HNS",
      "display_code": "COBO_HNS",
      "description": "",
      "decimal": 6,
      "address": "rs1qhqr75h43kxuj4qn42rn2a7kgrehmrx79z67l85",
      "source_address": "rs1qt8f7pjuc6de5ljgtm96l2gkl4x7pq497j8qdn3",
      "side": "withdraw",
      "amount": "50000",
      "abs_amount": "0.05",
      "txid": "f3666096ea70aa44f1766d56a5681266f7c83f6aa36130c1fc2d2cdc89b1127a",
      "vout_n": 0,
      "request_id": "web_send_by_user_2045_1681202509548",
      "status": "success",
      "abs_cobo_fee": "0.0000025",
      "created_time": 1681202509877,
      "last_time": 1681202710566,
      "confirmed_num": 2,
      "request_created_time": 1681202509877,
      "tx_detail": {
        "txid": "f3666096ea70aa44f1766d56a5681266f7c83f6aa36130c1fc2d2cdc89b1127a",
        "blocknum": 822051,
        "blockhash": "515d242200dbb049cbac9902df7a3982d05f4537790e1226b8a8b98490774f38",
        "fee": 0,
        "actualgas": 250000,
        "gasprice": 1,
        "hexstr": ""
      },
      "source_address_detail": "rs1qt8f7pjuc6de5ljgtm96l2gkl4x7pq497j8qdn3",
      "memo": "",
      "confirming_threshold": 2,
      "fee_coin": "COBO_HNS",
      "fee_amount": 550000,
      "fee_decimal": 6,
      "type": "external",
      "tx_request_type": ""
    }
  ]
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>