---
title: 
api: GET /v1/custody/withdraw_info_by_request_id/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns information about a withdraw transaction given a request ID. The response is a JSON object containing details about the transaction such as the transaction hash, amount, and status.
---
#### Request
<ParamField query="request_id" type="String" required>Withdraw Request ID</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
  <Expandable title="object">
        <ResponseField name="id" type="String">Cobo Unique Transaction ID</ResponseField>
        <ResponseField name="coin" type="String">Coin code (Cobo has internal symbols for each coin to ensure they are all unique)</ResponseField>
        <ResponseField name="display_code" type="String">Coin ticker symbol (not unique, changeable, for reference only)</ResponseField>
        <ResponseField name="description" type="String">Full name of coin (not unique, changeable, for reference only)</ResponseField>
        <ResponseField name="address" type="String">Deposit address</ResponseField>
        <ResponseField name="memo" type="String">Memo for specified coins(EOS,XLM,XRP,IOST)</ResponseField>
        <ResponseField name="source_address" type="String">Source address</ResponseField>
        <ResponseField name="source_address_detail" type="String">Separate by comma if more than one source address</ResponseField>
        <ResponseField name="side" type="enum(deposit / withdraw)">Transaction type</ResponseField>
        <ResponseField name="amount" type="String">Transaction value (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>
        <ResponseField name="decimal" type="Int">Amount decimal</ResponseField>
        <ResponseField name="abs_amount" type="String">Transaction value (Note that this is an absolute value. If you trade 1.5 BTC, then the value is 1.5)</ResponseField>
        <ResponseField name="abs_cobo_fee" type="String">lute fee value. For examle, abs_cobo_fee 0.00005 means exactly 0.00005BTC</ResponseField>
        <ResponseField name="txid" type="String">Transaction ID, which can be found on the corresponding public chain</ResponseField>
        <ResponseField name="vout_n" type="String">For transactions on public blockchains that allow for multiple deposit addresses in a single transaction, this value indicates the transaction index on the corresponding public blockchain</ResponseField>
        <ResponseField name="request_id" type="String">Request ID</ResponseField>
        <ResponseField name="status" type="enum(success / failed / pending)">Transaction status. If using the 'pending_transaction' and 'pending_transaction' interfaces for querying, the status can only be 'pending'</ResponseField>
        <ResponseField name="request_created_time" type="Long">Withdraw request creation time</ResponseField>
        <ResponseField name="created_time" type="Long">Transaction creation time</ResponseField>
        <ResponseField name="last_time" type="Long">Transaction success/failure time</ResponseField>
        <ResponseField name="confirming_threshold" type="Int">Confirmed numbers required for a successful transaction</ResponseField>
        <ResponseField name="confirmed_num" type="Int">Confirmed numbers</ResponseField>
        <ResponseField name="remark" type="String">Transaction remark</ResponseField>
        <ResponseField name="fee_coin" type="String">Fee coin code</ResponseField>
        <ResponseField name="fee_amount" type="Int">Fee amount (Note that the value here contains decimals. For example, a BTC value of 100,000,000 here is actually 1 BTC)</ResponseField>
        <ResponseField name="fee_decimal" type="Int">Fee decimal</ResponseField>
        <ResponseField name="type" type="String">external, internal shows if it's a Loop tx or external(on-chain)</ResponseField>
        | Transaction Request Type | Code |
        | ----------- | ----------- |
        | REQUEST_FROM_WEB| 100|
        | REQUEST_FROM_API| 200|
        | REQUEST_FROM_GAS_STATION| 1500|
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/withdraw_info_by_request_id/",
    {
        "request_id": "unique_123456"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/withdraw_info_by_request_id/',
        {
            "request_id": "unique_123456"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/withdraw_info_by_request_id/", map[string]string{
    "request_id": "unique_123456",
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "id": "",
    "coin": "XRP",
    "display_code": "XRP",
    "description": "Ripple",
    "address": "rfKyCMyoV6Ln2GZ7YDbrBrnXCbAyBbxRqB",
    "source_address": "",
    "side": "withdraw",
    "amount": "1",
    "decimal": 6,
    "abs_amount": "0.000001",
    "txid": "B209B991B3B3877F88692B45AB8D476DEE1EBD4CACF7847760D93A797465EE80",
    "vout_n": 0,
    "request_id": "IntegrationTest-575197675203117556",
    "status": "pending",
    "created_time": 1637137814143,
    "last_time": 1637137814143,
    "memo": "1175609358",
    "confirming_threshold": 1,
    "remark": "",
    "confirmed_num": 0,
    "type": "external",
    "fee_amount": 1000000,
    "fee_coin": "XRP",
    "fee_decimal": 6
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>