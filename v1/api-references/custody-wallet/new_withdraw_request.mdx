---
title: 
api: POST /v1/custody/new_withdraw_request/ 
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows users to create a new withdraw request for a specified coin. The request requires authentication and the necessary parameters include the coin, request ID, withdraw address, and amount. Optional parameters include a memo, force_external, and force_internal. The response will contain information about the newly created withdraw request.
---

#### Request
<ParamField body="coin" type="String" required>The coin to withdraw.</ParamField>
<ParamField body="request_id" type="String" required>A unique ID for the withdraw request.</ParamField>
<ParamField body="address" type="String" required>The address to withdraw to.</ParamField>
<ParamField body="amount" type="Int" required>The amount to withdraw.</ParamField>
<ParamField body="memo" type="String" >Needed when you withdraw EOS, XRP, IOST</ParamField>
<ParamField body="remark" type="String" >The remark to withdraw.</ParamField>
<ParamField body="force_external" type="String" >Non-empty means: force the transaction on-chain even if it's a Loop Tx</ParamField>
<ParamField body="force_internal" type="String" >Non-empty means: force the transaction off-chain even if it's not a Loop Tx, if it cannot be handled as a Loop tx, it will be rejected</ParamField>

#### Response
<ResponseField name="success" type="bool">true or false</ResponseField>
<ResponseField name="result" type="String" >Success is "", failed is null.</ResponseField>


<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/new_withdraw_request/",
    {
        "coin": "ETH",
        "address": "******************************************",
        "amount": 100000000000000,
        "request_id": "unique_123456",
        "remark": "hello test"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/new_withdraw_request/',
        {
            "coin": "ETH",
            "address": "******************************************",
            "amount": 100000000000000,
            "request_id": "unique_123456",
            "remark": "hello test"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/new_withdraw_request/", map[string]string{
    "coin": "ETH",
    "address": "******************************************",
    "amount": 100000000000000,
    "request_id": "unique_123456",
    "remark": "hello test",
})
```
</RequestExample>

<ResponseExample>
```json success
{
  "success": true,
  "result": ""
}
```
```json failed
{
    "success": false,
    "result": null,
    "errorCode":"12009",
    "errorMessage":"Request id api_request_id_123458 is duplicated",
    "errorId":"0400ccb7ad3f4ada8430f08b8f83a30d",
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>