---
title: Get Addresses Info
api: "GET /v1/custody/addresses_info/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns information about a list of addresses for a specific coin. The endpoint accepts GET and POST requests with parameters 'coin' and 'address'. The 'coin' parameter specifies the coin type, and the 'address' parameter is a comma-separated list of addresses. The response contains the coin type and a comma-separated list of addresses that belong to the custody wallet of the authenticated user.
---


#### Request
<ParamField query="coin" type="String" required>The coin type.</ParamField>
<ParamField query="address" type="String" required>A comma-separated list of addresses.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
   <Expandable title="object">
        <ResponseField name="coin" type="String">The coin type.</ResponseField>
        <ResponseField name="addresses" type="String">A comma-separated list of addresses that belong to the custody wallet of the authenticated user.</ResponseField>
    </Expandable>
</ResponseField>

<RequestExample>
 ```python Python
request(
  "GET",
  "/v1/custody/addresses_info/",
  {
    "coin": "ETH",
    "address": "******************************************,******************************************,******************************************,******************************************",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/addresses_info/',
        {
            "coin": "ETH",
            "address": "******************************************,******************************************,******************************************,******************************************",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/addresses_info/", map[string]string{
    "coin": "ETH",
    "address": "******************************************,******************************************,******************************************,******************************************",
})
```
</RequestExample>

And here is an example response from the API endpoint:


<ResponseExample>
```json
{
  "success": true,
  "result": {
    "coin": "ETH",
    "addresses": "******************************************,******************************************,******************************************"
  }
}

```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>