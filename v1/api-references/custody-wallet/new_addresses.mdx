---
title: Get New Deposit Addresses
api: "POST /v1/custody/new_addresses/"
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint generates new addresses for a given coin and returns a list of the generated addresses. The number of addresses to generate is specified in the 'count' parameter of the POST request.The maximum number of addresses that can be generated in one request is 200. The endpoint also checks if the organization has permission to add addresses and if the number of addresses being added exceeds the minutely limit.
---

<Tip> FAQ：[How to batch-generate addresses?](/v1/faqs/address-management#how-to-batch-generate-addresses)</Tip>

<Note>For BTC address, it supports generating segwit (3 address) and native_ Segwit (bc1 address), see below for details. (*
Cobo supports withdraw to any BTC address type)</Note>


#### Request
<ParamField body="coin" type="String" required>The coin to generate addresses for.</ParamField>
<ParamField body="count" type="Int" required>The number of addresses to generate.</ParamField>
<ParamField body="native_segwit" type="Bool">Whether to generate native segwit addresses.</ParamField>
<ParamField body="encoding" type="Int">The addresses encoding formats. This property only applies to coin of ADA. Available options:9、10.For more information, please [refer to](/v1/api-references/development/address-encoding) the enum value corresponding to each encoding</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
   <Expandable title="object">
        <ResponseField name="coin" type="String">The coin for which addresses were generated.</ResponseField>
        <ResponseField name="addresses" type="String[]">List of generated addresses.</ResponseField>
    </Expandable>
</ResponseField>




<RequestExample>
```python Python
request(
  "POST",
  "/v1/custody/new_addresses/",
  {
    "coin": "ETH"
            "count": 4

},
api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('POST', '/v1/custody/new_addresses/',
        {
            "coin": "ETH"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("POST", "/v1/custody/new_addresses/", map[string]string{
    "coin": "ETH",
})
```
</RequestExample>


<ResponseExample>
```json
{
  "success": true,
  "result": {
    "coin": "ETH",
    "addresses": [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************"
    ]
  }
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>