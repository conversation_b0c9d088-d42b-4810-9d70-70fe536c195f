---
title: Get Internal Address Info
api: "GET /v1/custody/internal_address_info/"
description: '<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns information about whether a given address is an internal address or not. If the address is internal, it also returns the name of the organization and wallet associated with the address. The endpoint takes three parameters: `coin`, `address`, and `memo` (optional). If the address contains a `|` character, the address is split into two parts: the address itself and the memo. Otherwise, the memo parameter is used. The endpoint requires authentication.'
---

<Note>To check if the address available for Loop transaction.
More info about Loop: [https://loop.top/](https://loop.top/)</Note>



#### Request
<ParamField query="coin" type="String" required>The coin type of the address.</ParamField>
<ParamField query="address" type="String" required>The address to check.</ParamField>
<ParamField query="memo" type="String">The memo associated with the address (if any).</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
    <Expandable title="object">
        <ResponseField name="coin" type="String">The coin type of the address.</ResponseField>
        <ResponseField name="address" type="String">The address being checked.</ResponseField>
        <ResponseField name="is_internal_address" type="Bool">Whether the address is an internal address or not.</ResponseField>
        <ResponseField name="internal_org" type="String">The name of the organization associated with the address (if it is an internal address).</ResponseField>
        <ResponseField name="internal_wallet" type="String">The name of the wallet associated with the address (if it is an internal address).</ResponseField>
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/internal_address_info/",
  {
    "coin": "ETH",
    "address": "******************************************",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/internal_address_info/',
        {
            "coin": "ETH",
            "address": "******************************************",
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/internal_address_info/", map[string]string{
    "coin": "ETH",
    "address": "******************************************",
})
```
</RequestExample>


<ResponseExample>
```json
{
  "success": true,
  "result": {
    "coin": "ETH",
    "address": "******************************************",
    "is_internal_address": true,
    "internal_org": "CoboWalletProduction",
    "internal_wallet": "CoboWalletProduction"
  }
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>