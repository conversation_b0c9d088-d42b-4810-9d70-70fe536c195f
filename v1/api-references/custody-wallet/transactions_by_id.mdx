---
title: Get Transactions By ID
api: "GET /v1/custody/transactions_by_id/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of transactions filtered by various parameters such as coin, address, side, and transaction ID range. The response includes transaction details such as transaction ID, coin type, amount, and timestamp.
---
<Note>Only successful deposit/withdraw requests will be considered as 'Transaction' here</Note>

#### Request
<ParamField query="coin" type="String" >Coin code (Does not return all currencies)</ParamField>
<ParamField query="side" type="enum(deposit / withdraw)" >Deposit/withdraw </ParamField>
<ParamField query="address" type="String" >Deposit/withdraw address is optional. If not included, all address history will be returned. </ParamField>
<ParamField query="max_id" type="String" >The transaction history ID limit is optional. If not included, will by default return the most recent records.(cannot be used with min_id) </ParamField>
<ParamField query="min_id" type="String" > Optional. If included, the sequence will be changed to time ASC. If not included, will by default return the most recent records, time DESC.(cannot be used with max_id) </ParamField>
<ParamField query="limit" type="Integer" >Page size is optional. If not included, the default size will be 50, and the maximum size will also be 50.</ParamField>
<ParamField query="include_financial" type="String" >Request all transactions. If it is passed in, return all transactions(Including stacking, trading)</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
  <Expandable title="object">
        <Snippet file="full_custodian_transaction_snippet.mdx" />
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  'GET',
  '/v1/custody/transactions_by_id/',
  {
    "coin": "BTC_USDT",
    "side": "deposit",
    "max_id": 20181114183534062065000000483656
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/transactions_by_id/',
        {
            "coin": "BTC_USDT",
            "side": "deposit",
            "max_id": 20181114183534062065000000483656

        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/transactions_by_id/", map[string]string{
    "coin": "BTC_USDT",
    "side": "deposit",
    "max_id": 20181114183534062065000000483656
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
        "id": "20221013214833000312228000005846",
        "coin": "BTC_USDT",
        "display_code": "USDT",
        "description": "Tether",
        "decimal": 8,
        "address": "**********************************",
        "source_address": "AutoTest_RC02",
        "side": "deposit",
        "amount": "6000000",
        "abs_amount": "0.06",
        "txid": "L65954e38419b55abb10b37fc6fca54f",
        "vout_n": 0,
        "request_id": null,
        "status": "success",
        "abs_cobo_fee": "0",
        "created_time": 1665668913144,
        "last_time": 1665668913144,
        "confirmed_num": 3,
        "remark": "",
        "tx_detail": {
            "txid": "L65954e38419b55abb10b37fc6fca54f",
            "blocknum": 0,
            "blockhash": "",
            "fee": 0,
            "actualgas": 0,
            "gasprice": 1,
            "hexstr": ""
        },
        "source_address_detail": "AutoTest_RC02",
        "confirming_threshold": 3,
        "fee_coin": "BTC",
        "fee_decimal": 8,
        "fee_amount": 0,
        "type": "internal",
        "tx_request_type": ""
    },
    {
        "id": "20221013213858000312228000007739",
        "coin": "BTC_USDT",
        "display_code": "USDT",
        "description": "Tether",
        "decimal": 8,
        "address": "**********************************",
        "source_address": "AutoTest_RC02",
        "side": "deposit",
        "amount": "23000000",
        "abs_amount": "0.23",
        "txid": "L3dbb8e30d8da66c9ea0d7171e1fbe2c",
        "vout_n": 0,
        "request_id": null,
        "status": "success",
        "abs_cobo_fee": "0",
        "created_time": 1665668338625,
        "last_time": 1665668338625,
        "confirmed_num": 3,
        "remark": "",
        "tx_detail": {
            "txid": "L3dbb8e30d8da66c9ea0d7171e1fbe2c",
            "blocknum": 0,
            "blockhash": "",
            "fee": 0,
            "actualgas": 0,
            "gasprice": 1,
            "hexstr": ""
        },
        "source_address_detail": "AutoTest_RC02",
        "confirming_threshold": 3,
        "fee_coin": "BTC",
        "fee_decimal": 8,
        "fee_amount": 0,
        "type": "internal",
        "tx_request_type": ""
    }
  ]
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>