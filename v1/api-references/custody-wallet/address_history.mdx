---
title: Get Address History
api: "GET /v1/custody/address_history/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of addresses associated with a custody wallet for a given coin, with pagination support. The response is a JSON array of objects, each containing a 'coin' field and an 'address' field.
---

#### Request
<ParamField query="coin" type="String" required>The coin symbol.</ParamField>
<ParamField query="page_index" type="Int">The page index, starting from 0.</ParamField>
<ParamField query="page_length" type="Int">The number of items per page.</ParamField>
<ParamField query="sort_flag" type="Int">The sort flag, 0 for ascending and 1 for descending.</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
    <Expandable title="object">
        <ResponseField name="coin" type="String" required>The coin symbol.</ResponseField>
        <ResponseField name="address" type="String" required>The address associated with the custody wallet.</ResponseField>
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/address_history/",
  {
    "coin": "ETH",
    "page_index": 0,
    "page_length": 20,
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/address_history/',
        {
          "coin": "ETH",
          "page_index": 0,
          "page_length": 20,
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/address_history/", map[string]string{
    "coin": "ETH",
    "page_index": 0,
    "page_length": 20,
})
```
</RequestExample>


<ResponseExample>
```json
{
  "success": true,
  "result": [
    {
      "coin": "ETH",
      "address": "******************************************"
    },
    {
      "coin": "ETH",
      "address": "******************************************"
    },
    {
      "coin": "ETH",
      "address": "******************************************"
    }
  ]
}
```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>