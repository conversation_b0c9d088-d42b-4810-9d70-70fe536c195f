---
title: Get Supported NFT Collections
api: GET /v1/custody/mpc/get_supported_nft_collections/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with a list of supported NFT collections on the specified chain. The response includes the NFT code, chain code, contract address, description, display code, and standard for each supported collection.
---

<Note>This API is only intended for MPC web3 wallets and does not support MPC send/receive wallets.</Note>

#### Request
<ParamField query="chain_code" type="String" required>chain code</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="nft_collections" type="object[]">
            <Expandable title="object">
                <ResponseField name="nft_code" type="String" >NFT Token code</ResponseField>
                <ResponseField name="chain_code" type="String" >chain code</ResponseField>
                <ResponseField name="contract_address" type="String" >NFT contract address</ResponseField>
                <ResponseField name="description" type="String" >NFT description</ResponseField>
                <ResponseField name="display_code" type="String" >NFT display code</ResponseField>
                <ResponseField name="standard" type="String" >NFT contract standard</ResponseField>
            </Expandable>
        </ResponseField>

      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_supported_nft_collections/",
    {
        "chain_code": "ETH"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/get_supported_nft_collections/', {
  "chain_code": "ETH"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_supported_nft_collections/", map[string]string{
 "chain_code": "ETH"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "nft_collections": [
      {
        "nft_code": "NFT_ETH_MOOD",
        "chain_code": "ETH",
        "contract_address": "******************************************",
        "description": "Moodies",
        "display_code": "MOOD",
        "standard": "ERC721"
      },
      {
        "nft_code": "NFT_ETH_GOBLIN",
        "chain_code": "ETH",
        "contract_address": "******************************************",
        "description": "",
        "display_code": "GOBLIN",
        "standard": "ERC721"
      }
    ]
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>