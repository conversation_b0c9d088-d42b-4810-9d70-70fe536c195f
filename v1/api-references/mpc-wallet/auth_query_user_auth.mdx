---
title: Query User Auth
api: GET /v1/custody/guard/query_user_auth/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint is a GET request that requires a 'user_id' parameter. It returns a JSON response containing the user's authentication information queried from PrimeBrokerManager. Authentication is required to access this endpoint.
---

#### Request
<ParamField query="user_id" type="String" required>unique user ID</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="pubkey" type="String" >The bound Auth public key, or empty if the binder_id has expired</ResponseField>
        <ResponseField name="addresses" type="object[]" >
          <Expandable title="object">
            <ResponseField name="chain_coin" type="String" >bound coin</ResponseField>
            <ResponseField name="address" type="String" >bound address</ResponseField>
          </Expandable>
        </ResponseField>

       </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/guard/query_user_auth/",
    {
        "user_id": "cobo_168108513539918"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/guard/query_user_auth/',
        {
            "user_id": "cobo_168108513539918"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/guard/query_user_auth/", map[string]string{
    "user_id": "cobo_168108513539918"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "pubkey": "480da241874516ca9a25b1776e2b3ce6d2d5ea481d9a83e28874e57d4a2786ea1db059aefb2093cf7775da68c5381cf401c821aa967927c3abc46c7545c90438",
    "addresses": [
      {
        "chain_coin": "ETH",
        "address": "0x123"
      }
      …
    ]
  }
}


```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>