---
title: Get Max Sendable Amount
api: GET /v1/custody/mpc/get_max_send_amount/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns the maximum sendable amount and the corresponding transaction fee for a given address. It takes in various parameters such as the coin code, fee rate, to_address, and from_address. The response includes the coin decimal, fee coin, the maximum sendable amount, and the corresponding transaction fee.
---

#### Request
<ParamField query="coin" type="String" required>coin code</ParamField>
<ParamField query="fee_rate" type="Float" required>gas price (unit: wei) for account model or transaction fees per byte for UTXO model</ParamField>
<ParamField query="to_address" type="String" required>to address</ParamField>
<ParamField query="from_address" type="String">
Account Model: this parameter is required

UTXO Model: this parameter is optional, the input will be selected based off the current wallet if the parameter is empty or not passed
</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
  <Expandable title="object">
    <ResponseField name="coin" type="String">coin code</ResponseField>
    <ResponseField name="coin_decimal" type="Int" >coin decimal precision</ResponseField>
    <ResponseField name="fee_coin" type="String">fee coin code</ResponseField>
    <ResponseField name="fee_decimal" type="Int" >fee coin decimal precision</ResponseField>
    <ResponseField name="max_send_value" type="Int" >the maximum sendable amount for the given address or current wallet</ResponseField>
    <ResponseField name="fee_per_byte" type="Float" >transaction fees per byte for UTXO model</ResponseField>
    <ResponseField name="fee_amount" type="Int" >transaction fee for UTXO model</ResponseField>
    <ResponseField name="gas_price" type="Int" >gas price for account model</ResponseField>
    <ResponseField name="gas_limit" type="Int" >gas limit for account model</ResponseField>
  </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_max_send_amount/",
    {
        "coin": "ETH",
        "fee_rate": ***********,
        "to_address": "******************************************",
        "from_address": "******************************************"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/get_max_send_amount/', {
    "coin": "ETH",
    "fee_rate": ***********,
    "to_address": "******************************************",
    "from_address": "******************************************"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_max_send_amount/", map[string]string{
    "coin": "ETH",
    "fee_rate": ***********,
    "to_address": "******************************************",
    "from_address": "******************************************"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "coin": "ETH",
    "coin_decimal": 18,
    "fee_coin": "ETH",
    "fee_decimal": 18,
    "max_send_value": 841165119243622,
    "fee_per_byte": 0,
    "fee_amount": 0,
    "gas_price": ***********,
    "gas_limit": 21000
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>