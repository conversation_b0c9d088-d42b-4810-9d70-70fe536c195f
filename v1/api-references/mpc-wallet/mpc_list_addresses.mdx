---
title: Get Addresses List
api: GET /v1/custody/mpc/list_addresses/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing a list of wallet addresses and their associated information. The endpoint takes in a chain code and optional parameters for pagination and sorting. The response includes the total count of addresses and a list of addresses with their respective ID, address, encoding, and HD path.
---
#### Request
<ParamField query="chain_code" type="String" required>Chain code of the wallet addresses.</ParamField>
<ParamField query="start_id" type="String" >ID of an address to start with; the latest address information will be returned if neither the start ID nor the end ID is specified</ParamField>
<ParamField query="end_id" type="String" >ID of an address to end with; the latest address information will be returned if neither the start ID nor the end ID is specified</ParamField>
<ParamField query="limit" type="Int" >number of entries per page; max: 50; default: 50</ParamField>
<ParamField query="sort" type="Int" >address sorting method; 1: based on creation time, from earliest to latest; other value: based on creation time, from latest to earliest (default)</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="total" type="Int" >Total number of addresses</ResponseField>
        <ResponseField name="addresses" type="object[]">
            <Expandable title="object">
                <ResponseField name="id" type="String" >The ID of the generated address.</ResponseField>
                <ResponseField name="address" type="String" >The generated address.</ResponseField>
                <ResponseField name="hd_path" type="String" >The HD path of the generated address.</ResponseField>
                <ResponseField name="encoding" type="Int" >The encoding of the generated address. For more information, please [refer to](/v1/api-references/development/address-encoding) the enum value corresponding to each encoding </ResponseField>
                <ResponseField name="description" type="String" >The address description.</ResponseField>
            </Expandable>
        </ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/list_addresses/",
    {
        "chain_code": "ETH",
        "start_id": "721887227156373678",
        "end_id": "",
        "limit": 2,
        "sort": 0
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/list_addresses/', {
    "chain_code": "ETH",
    "start_id": "721887227156373678",
    "end_id": "",
    "limit": 2,
    "sort": 0
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/list_addresses/", map[string]string{
    "chain_code": "ETH",
    "start_id": "721887227156373678",
    "end_id": "",
    "limit": 2,
    "sort": 0
})
```
</RequestExample>

<ResponseExample>
```json
{
 "success": true,
 "result": {
      "total": 2,
      "addresses": [
        {
          "id": "721941011752862138",
          "address": "******************************************",
          "encoding": 0,
          "hd_path": "m/44/60/1/0/85",
          "description":""
        },
        {
          "id": "721941011752862137",
          "address": "******************************************",
          "encoding": 0,
          "hd_path": "m/44/60/1/0/84",
          "description":""
        }
      ]
  }
}


```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>