---
title: Query Binding
api: GET /v1/custody/guard/query_binding/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with the binding information for a given binder ID. The endpoint requires authentication and the user must have query permission. The binder ID is passed as a parameter in the GET request.
---

#### Request
<ParamField query="binder_id" type="String" required>CoboAuth binding code</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="status" type="Int" >Binding code status</ResponseField>
        <ResponseField name="pubkey" type="String" >The bound Auth public key, or empty if the binder_id has expired</ResponseField>
        <ResponseField name="addresses" type="object[]" >
          <Expandable title="object">
            <ResponseField name="chain_coin" type="String" >bound coin</ResponseField>
            <ResponseField name="address" type="String" >bound address</ResponseField>
          </Expandable>
        </ResponseField>

       </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/guard/query_binding/",
    {
        "binder_id": "Ph8YP+LTQyqcfHE/A+eXUw=="
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/guard/query_binding/',
        {
            "binder_id": "Ph8YP+LTQyqcfHE/A+eXUw=="
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/guard/query_binding/", map[string]string{
    "binder_id": "Ph8YP+LTQyqcfHE/A+eXUw=="
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "status": 0,
    "pubkey": "480da241874516ca9a25b1776e2b3ce6d2d5ea184d9a83e28874e57d4a2786ea1db059aefb2093cf6665da68c5381cf401c128aa967927c3abc46c7545c90438",
    "addresses": [
      {
        "chain_coin": "ETH",
        "address": "0x123"
      }
      …
    ]
  }
}




```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>