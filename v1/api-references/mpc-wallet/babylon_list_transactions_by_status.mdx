---
title: Babylon List Transactions By Status
api: GET /v1/custody/mpc/babylon/list_transactions_by_status/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint is used to query all staking transactions based on the provided parameter `status`, `address`, `min_cobo_id` and `limit`.
---

#### Request
<ParamField query="status" type="Int" required>
| Status Type | Code |
| ----------- | ----------- |
| STAKING_TX_INIT| 1|
| STAKING_TX_WAITING_BROADCASTING| 100|
| STAKING_TX_BROADCASTED| 200|
| STAKING_TX_PENDING| 300|
| STAKING_ACTIVE| 400|
| STAKING_OVERFLOW| 490|
| STAKING_FAILED| 499|
| STAKING_UNBONDINGREQUESTED| 500|
| STAKING_UNBONDING| 510|
| STAKING_UNBONDED| 520|
| STAKING_WITHDRAWN| 600|
<Tip>The 300 status indicates that the transaction has been confirmed on the BTC chain but not yet indexed by Babylon, while the 400 status indicates that the transaction has been successfully confirmed by the Babylon indexer.</Tip>
<Tip>The 490 status indicates that the transaction has been successfully confirmed by the Babylon indexer, but because it exceeds Babylon's staking cap, it will not be included in the earnings calculation.</Tip>
<Tip>The 500 status indicates that the unbonding request for the staking transaction has been sent to Babylon. The 510 status means that the unbonding transaction is waiting for Babylon to reach the required number of locked blocks. The 520 status indicates that the staking transaction has been successfully unbonded (either due to natural expiration or successful unbonding). Once this status is reached, the staking transaction can be withdrawn.</Tip>
<Tip>The 600 status indicates that the staking transaction has been fully withdrawn and the staking transaction is closed.</Tip>
</ParamField>
<ParamField query="address" type="String">staking address</ParamField>
<ParamField query="min_cobo_id" type="String">> cobo_id; details of staking transactions whose cobo ID are greater than this value will be returned</ParamField>
<ParamField query="limit" type="Int">entries per page; max: 50; default: 50</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object[]">
    <Expandable title="object">
        <ResponseField name="request_id" type="String" >Transaction request ID</ResponseField>
        <ResponseField name="cobo_id" type="String" >Unique transaction ID in Cobo</ResponseField>
        <ResponseField name="staking_info" type="String" >Detailed information of the staking transaction, in JSON format</ResponseField>
        <ResponseField name="status" type="Int" >
        | Status Type | Code |
        | ----------- | ----------- |
        | STAKING_TX_INIT| 1|
        | STAKING_TX_WAITING_BROADCASTING| 100|
        | STAKING_TX_BROADCASTED| 200|
        | STAKING_TX_PENDING| 300|
        | STAKING_ACTIVE| 400|
        | STAKING_OVERFLOW| 490|
        | STAKING_FAILED| 499|
        | STAKING_UNBONDINGREQUESTED| 500|
        | STAKING_UNBONDING| 510|
        | STAKING_UNBONDED| 520|
        | STAKING_WITHDRAWN| 600|
        </ResponseField>
        <ResponseField name="fee_rate" type="String" >transaction fees per byte</ResponseField>
        <ResponseField name="max_staking_fee" type="Int" >maximum fee for the staking transaction</ResponseField>
        <ResponseField name="sign_result" type="Json" >signature result, which is in JSON format and contains the raw transaction hex</ResponseField>
        <ResponseField name="tx_hash" type="String" >transaction hash</ResponseField>
    </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/babylon/list_transactions_by_status/",
    {
        "status": 400,
        "address": "tb1pk3gty3x43zkej7v9tuw78qtfsl3xgsp4v707acd8yqsw8lqvfhystl0rec",
        "cobo_id": "20240617181735000387960000000501"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/babylon/list_transactions_by_status/', {
    "status": 400,
    "address": "tb1pk3gty3x43zkej7v9tuw78qtfsl3xgsp4v707acd8yqsw8lqvfhystl0rec",
    "cobo_id": "20240617181735000387960000000501"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/babylon/list_transactions_by_status/", map[string]string{
    "status": 400,
    "address": "tb1pk3gty3x43zkej7v9tuw78qtfsl3xgsp4v707acd8yqsw8lqvfhystl0rec",
    "cobo_id": "20240617181735000387960000000501"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": [{
    "request_id": "1718619454210",
    "cobo_id": "20240617181735000387960000000502",
    "staking_info": "{\"asset_coin\": \"SIGNET_BTC\", \"stake_address\": \"tb1pufc6zv4wquhcshgr4lay4lqa6vua3m435hqmml9w74ycxxnv329qdl6cq2\", \"amount\": 50000, \"stake_block_time\": 64000, \"finality_provider\": \"88b32b005d5b7e29e6f82998aff023bff7b600c6a1a74ffac984b3aa0579b384\"}",
    "status": 400,
    "fee_rate": "10.00000000",
    "max_staking_fee": null,
    "sign_result": "{\"raw_tx\": \"0200000000010197a8e32b25590962075bf8517aa117459abd26593731e3d58ecf3c8cfb1b9a750200000000000000000350c30000000000002251205e5882d46eadd557b170a4dc73970bb663c37b262da3b7eb75ff9627c51d695b0000000000000000496a476262743400b450b244d588ad9979855f1de3816987e2644035679feee1a72020e3fc0c4dc988b32b005d5b7e29e6f82998aff023bff7b600c6a1a74ffac984b3aa0579b384fa00125f0c0000000000225120b450b244d588ad9979855f1de3816987e2644035679feee1a72020e3fc0c4dc90140ca3ce98e6837b4404af7e1087072e7306d132ae1ccbf5377f75bd7f2ee7678c9e623e167deaca981269cc317b6c25359b6b399bfb577d5414c17002ca34e463809080300\"}",
    "tx_hash": "7311feeeb9278f487be3c84662210540d8ac2dc24972721d7978915ef649f8c8"
  }]
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>