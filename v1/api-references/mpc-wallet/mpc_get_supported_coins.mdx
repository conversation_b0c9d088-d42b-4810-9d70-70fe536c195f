---
title: Get Supported Coins
api: GET /v1/custody/mpc/get_supported_coins/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing a list of supported coins for a given chain. The response includes the coin code, display code, description, decimal, deposit and withdraw availability, and confirming threshold.
---
#### Request
<ParamField query="chain_code" type="String" required>Chain code for which to retrieve supported coins.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="coins" type="object[]">
            <Expandable title="object">
                <ResponseField name="coin" type="String" >Coin code</ResponseField>
                <ResponseField name="display_code" type="String" >Abbreviation (reference only, subject to change)</ResponseField>
                <ResponseField name="description" type="String" >Full name (reference only, subject to change)</ResponseField>
                <ResponseField name="decimal" type="Int" >Decimal precision</ResponseField>
                <ResponseField name="can_deposit" type="Bool" >Whether deposit is supported</ResponseField>
                <ResponseField name="can_withdraw" type="Bool" >Whether withdraw is supported</ResponseField>
                <ResponseField name="confirming_threshold" type="Int" >Number of confirmations required</ResponseField>
            </Expandable>
        </ResponseField>
      </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_supported_coins/",
    {
        "chain_code": "ETH"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/get_supported_coins/', {
  "chain_code": "ETH"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_supported_coins/", map[string]string{
 "chain_code": "ETH"
})
```
</RequestExample>

<ResponseExample>
```json
{
 "success": true,
 "result": {
      "coins": [
        {
          "coin": "ETH",
          "display_code": "ETH",
          "description": "Ethereum",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 12
        },
        {
          "coin": "ETH_USDT",
          "display_code": "USDT",
          "description": "",
          "decimal": 6,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 12
        }
      ]
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>