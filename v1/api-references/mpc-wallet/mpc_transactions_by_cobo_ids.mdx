---
title: Get Transactions By Cobo ID
api: GET /v1/custody/mpc/transactions_by_cobo_ids/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of transaction requests based on the provided cobo_ids and status. The cobo_ids parameter is a comma-separated list of cobo ids. The status parameter is an optional integer representing the status of the transaction request. The response is a JSON object containing an array of transaction request objects.
---
#### Request
<ParamField query="cobo_ids" type="String" required>List of Cobo Unique IDs for transactions, separated by commas, cannot be empty or greater than 50</ParamField>
<ParamField query="status" type="Int" >
| Status Type | Code |
| ----------- | ----------- |
| PENDING_APPROVAL| 101|
| QUEUED| 201|
| PENDING_SIGNATURE| 301|
| BROADCASTING| 401|
| BROADCAST_FAILED| 402|
| PENDING_CONFIRMATION| 403|
| CONFIRMATION| 501|
| REVERTING| 502|
| SUCCESS| 900|
| FAILED| 901|
| REORG| 902|

<Tip>The 403 status indicates that the transaction has been successfully broadcast but not yet confirmed, while the 501 status indicates that the transaction has been successfully confirmed on the blockchain and is awaiting further confirmations.</Tip>
</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
           <Snippet file="mpc_transaction_snippet.mdx"/>
      </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/transactions_by_cobo_ids/",
    {
        "cobo_ids": "20221222170157000336673000008161",
        "status": 101
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/transactions_by_cobo_ids/', {
    "cobo_ids": "20221222170157000336673000008161",
    "status": 101
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/transactions_by_cobo_ids/", map[string]string{
    "cobo_ids": "20221222170157000336673000008161",
    "status": 101
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "total": 28,
    "transactions": [
      {
        "cobo_id": "20221222170157000336673000008161",
        "request_id": "web_send_by_user_915_1671699717647",
        "status": 101,
        "coin_detail": {
          "coin": "GETH",
          "display_code": "GETH",
          "description": "Ethereum Goerli Testnet",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 32
        },
        "amount_detail": {
          "amount": "9000000000000000",
          "abs_amount": "0.009"
        },
        "fee_detail": {
          "fee_coin_detail": {
            "coin": "GETH",
            "display_code": "GETH",
            "description": "Ethereum Goerli Testnet",
            "decimal": 18,
            "can_deposit": True,
            "can_withdraw": True,
            "confirming_threshold": 32
          },
          "gas_price": 300000000,
          "gas_limit": 21000,
          "fee_used": 0
        },
        "source_addresses": "",
        "from_address": "******************************************",
        "to_address": "******************************************",
        "tx_hash": "",
        "vout_n": 0,
        "nonce": null,
        "confirmed_number": 0,
        "replace_cobo_id": "",
        "transaction_type": 303,
        "operation": 100,
        "block_detail": {
          "block_hash": "",
          "block_height": 0,
          "block_time": 0
        },
        "tx_detail": {
          "tx_hash": ""
        },
        "extra_parameters": "",
        "created_time": 1671699717956,
        "updated_time": 1671699717956,
        "failed_reason": null,
        "max_priority_fee": null,
        "max_fee": null,
        "approval_process": {"spender_result": 1, "spender_review_threshold": 1, "spender_status": [{"spender_person": "Cobo_Test_spender", "status": "approve"}], "spender_complete_time": 1681906749496, "approver_result": 1, "approver_review_threshold": 1, "approver_status": [{"approve_person": "Cobo_Test_approver", "status": "approve"}], "approver_complete_time": 1681906758619},
        "remark": "",
        "memo": "",
        "gas_station_child_id": ""
      }
    ]
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>