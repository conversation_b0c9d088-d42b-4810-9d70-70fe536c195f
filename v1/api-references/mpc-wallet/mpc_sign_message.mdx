---
title: Sign Message
api: POST /v1/custody/mpc/sign_message/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> Sign Message
---

<Note>This API is only intended for MPC web3 wallets and does not support MPC send/receive wallets.</Note>

<Note>Users can use [mpc_sign_messages_by_cobo_ids](/v1/api-references/mpc-wallet/mpc_sign_messages_by_cobo_ids) and
[mpc_sign_messages_by_request_ids](/v1/api-references/mpc-wallet/mpc_sign_messages_by_request_ids) to query details of signed messages.</Note>

#### Request

<ParamField body="chain_code" type="String" required>chain code</ParamField>
<ParamField body="request_id" type="String" required>Withdraw request ID (Universally unique ID for each user's withdraw request and less than or equal to 120 characters)</ParamField>
<ParamField body="from_address" type="String" required>source address</ParamField>
<ParamField body="sign_version" type="String" required>sign version, EIP-191: 1 , EIP-712: 2</ParamField>
<ParamField body="extra_parameters" type="Json" required>
EIP-191:
    - message: Base64 encode of the original message
```python Sample
extra_parameters: '{"message": "YWFhYQ=="}'
```
EIP-712:
    - structured_data: Json of EIP-712 structured data, please refer to https://eips.ethereum.org/EIPS/eip-712 for EIP-712 info
```python Sample
"extra_parameters": '{"structured_data": "{\\"types\\":{\\"EIP712Domain\\":[{\\"name\\":\\"name\\",\\"type\\":\\"string\\"},{\\"name\\":\\"version\\",\\"type\\":\\"string\\"},{\\"name\\":\\"chainId\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"verifyingContract\\",\\"type\\":\\"address\\"}],\\"OrderComponents\\":[{\\"name\\":\\"offerer\\",\\"type\\":\\"address\\"},{\\"name\\":\\"zone\\",\\"type\\":\\"address\\"},{\\"name\\":\\"offer\\",\\"type\\":\\"OfferItem[]\\"},{\\"name\\":\\"consideration\\",\\"type\\":\\"ConsiderationItem[]\\"},{\\"name\\":\\"orderType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"startTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"zoneHash\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"salt\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"conduitKey\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"counter\\",\\"type\\":\\"uint256\\"}],\\"OfferItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"}],\\"ConsiderationItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"recipient\\",\\"type\\":\\"address\\"}]},\\"primaryType\\":\\"OrderComponents\\",\\"domain\\":{\\"name\\":\\"Seaport\\",\\"version\\":\\"1.4\\",\\"chainId\\":\\"5\\",\\"verifyingContract\\":\\"0x00000000000001ad428e4906aE43D8F9852d0dD6\\"},\\"message\\":{\\"offerer\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\",\\"offer\\":[{\\"itemType\\":\\"3\\",\\"token\\":\\"0x357Fd2942e8Ee435D7D21859ECAe99Bd597D8779\\",\\"identifierOrCriteria\\":\\"200\\",\\"startAmount\\":\\"1\\",\\"endAmount\\":\\"1\\"}],\\"consideration\\":[{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"9750000000000\\",\\"endAmount\\":\\"9750000000000\\",\\"recipient\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\"},{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"250000000000\\",\\"endAmount\\":\\"250000000000\\",\\"recipient\\":\\"0x0000a26b00c1F0DF003000390027140000fAa719\\"}],\\"startTime\\":\\"1681126098\\",\\"endTime\\":\\"1683718098\\",\\"orderType\\":\\"1\\",\\"zone\\":\\"0x004C00500000aD104D7DBd00e3ae0A5C00560C00\\",\\"zoneHash\\":\\"0x0000000000000000000000000000000000000000000000000000000000000000\\",\\"salt\\":\\"24446860302761739304752683030156737591518664810215442929808540646255434554088\\",\\"conduitKey\\":\\"0x0000007b02230091a7ed01230072f7006a004d60a8d4e71d599b8104250f0000\\",\\"totalOriginalConsiderationItems\\":\\"2\\",\\"counter\\":\\"0\\"}}"}'
```
</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
            <ResponseField name="cobo_id" type="String" >Unique transaction ID</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/sign_message/",
    {
        "chain_code": "ETH",
        "request_id": "1671699717647",
        "from_address": "******************************************",
        "sign_version": 2,
        "extra_parameters": '{"structured_data": "{\\"types\\":{\\"EIP712Domain\\":[{\\"name\\":\\"name\\",\\"type\\":\\"string\\"},{\\"name\\":\\"version\\",\\"type\\":\\"string\\"},{\\"name\\":\\"chainId\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"verifyingContract\\",\\"type\\":\\"address\\"}],\\"OrderComponents\\":[{\\"name\\":\\"offerer\\",\\"type\\":\\"address\\"},{\\"name\\":\\"zone\\",\\"type\\":\\"address\\"},{\\"name\\":\\"offer\\",\\"type\\":\\"OfferItem[]\\"},{\\"name\\":\\"consideration\\",\\"type\\":\\"ConsiderationItem[]\\"},{\\"name\\":\\"orderType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"startTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"zoneHash\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"salt\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"conduitKey\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"counter\\",\\"type\\":\\"uint256\\"}],\\"OfferItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"}],\\"ConsiderationItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"recipient\\",\\"type\\":\\"address\\"}]},\\"primaryType\\":\\"OrderComponents\\",\\"domain\\":{\\"name\\":\\"Seaport\\",\\"version\\":\\"1.4\\",\\"chainId\\":\\"5\\",\\"verifyingContract\\":\\"0x00000000000001ad428e4906aE43D8F9852d0dD6\\"},\\"message\\":{\\"offerer\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\",\\"offer\\":[{\\"itemType\\":\\"3\\",\\"token\\":\\"0x357Fd2942e8Ee435D7D21859ECAe99Bd597D8779\\",\\"identifierOrCriteria\\":\\"200\\",\\"startAmount\\":\\"1\\",\\"endAmount\\":\\"1\\"}],\\"consideration\\":[{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"9750000000000\\",\\"endAmount\\":\\"9750000000000\\",\\"recipient\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\"},{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"250000000000\\",\\"endAmount\\":\\"250000000000\\",\\"recipient\\":\\"0x0000a26b00c1F0DF003000390027140000fAa719\\"}],\\"startTime\\":\\"1681126098\\",\\"endTime\\":\\"1683718098\\",\\"orderType\\":\\"1\\",\\"zone\\":\\"0x004C00500000aD104D7DBd00e3ae0A5C00560C00\\",\\"zoneHash\\":\\"0x0000000000000000000000000000000000000000000000000000000000000000\\",\\"salt\\":\\"24446860302761739304752683030156737591518664810215442929808540646255434554088\\",\\"conduitKey\\":\\"0x0000007b02230091a7ed01230072f7006a004d60a8d4e71d599b8104250f0000\\",\\"totalOriginalConsiderationItems\\":\\"2\\",\\"counter\\":\\"0\\"}}"}'
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch("POST", "/v1/custody/mpc/sign_message/", {
    "chain_code": "ETH",
    "request_id": "1671699717647",
    "from_address": "******************************************",
    "sign_version": 2,
    "extra_parameters": '{"structured_data": "{\\"types\\":{\\"EIP712Domain\\":[{\\"name\\":\\"name\\",\\"type\\":\\"string\\"},{\\"name\\":\\"version\\",\\"type\\":\\"string\\"},{\\"name\\":\\"chainId\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"verifyingContract\\",\\"type\\":\\"address\\"}],\\"OrderComponents\\":[{\\"name\\":\\"offerer\\",\\"type\\":\\"address\\"},{\\"name\\":\\"zone\\",\\"type\\":\\"address\\"},{\\"name\\":\\"offer\\",\\"type\\":\\"OfferItem[]\\"},{\\"name\\":\\"consideration\\",\\"type\\":\\"ConsiderationItem[]\\"},{\\"name\\":\\"orderType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"startTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"zoneHash\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"salt\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"conduitKey\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"counter\\",\\"type\\":\\"uint256\\"}],\\"OfferItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"}],\\"ConsiderationItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"recipient\\",\\"type\\":\\"address\\"}]},\\"primaryType\\":\\"OrderComponents\\",\\"domain\\":{\\"name\\":\\"Seaport\\",\\"version\\":\\"1.4\\",\\"chainId\\":\\"5\\",\\"verifyingContract\\":\\"0x00000000000001ad428e4906aE43D8F9852d0dD6\\"},\\"message\\":{\\"offerer\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\",\\"offer\\":[{\\"itemType\\":\\"3\\",\\"token\\":\\"0x357Fd2942e8Ee435D7D21859ECAe99Bd597D8779\\",\\"identifierOrCriteria\\":\\"200\\",\\"startAmount\\":\\"1\\",\\"endAmount\\":\\"1\\"}],\\"consideration\\":[{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"9750000000000\\",\\"endAmount\\":\\"9750000000000\\",\\"recipient\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\"},{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"250000000000\\",\\"endAmount\\":\\"250000000000\\",\\"recipient\\":\\"0x0000a26b00c1F0DF003000390027140000fAa719\\"}],\\"startTime\\":\\"1681126098\\",\\"endTime\\":\\"1683718098\\",\\"orderType\\":\\"1\\",\\"zone\\":\\"0x004C00500000aD104D7DBd00e3ae0A5C00560C00\\",\\"zoneHash\\":\\"0x0000000000000000000000000000000000000000000000000000000000000000\\",\\"salt\\":\\"24446860302761739304752683030156737591518664810215442929808540646255434554088\\",\\"conduitKey\\":\\"0x0000007b02230091a7ed01230072f7006a004d60a8d4e71d599b8104250f0000\\",\\"totalOriginalConsiderationItems\\":\\"2\\",\\"counter\\":\\"0\\"}}"}'
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/sign_message/", map[string]string{
    "chain_code": "ETH",
    "request_id": "1671699717647",
    "from_address": "******************************************",
    "sign_version": 2,
    "extra_parameters": '{"structured_data": "{\\"types\\":{\\"EIP712Domain\\":[{\\"name\\":\\"name\\",\\"type\\":\\"string\\"},{\\"name\\":\\"version\\",\\"type\\":\\"string\\"},{\\"name\\":\\"chainId\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"verifyingContract\\",\\"type\\":\\"address\\"}],\\"OrderComponents\\":[{\\"name\\":\\"offerer\\",\\"type\\":\\"address\\"},{\\"name\\":\\"zone\\",\\"type\\":\\"address\\"},{\\"name\\":\\"offer\\",\\"type\\":\\"OfferItem[]\\"},{\\"name\\":\\"consideration\\",\\"type\\":\\"ConsiderationItem[]\\"},{\\"name\\":\\"orderType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"startTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endTime\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"zoneHash\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"salt\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"conduitKey\\",\\"type\\":\\"bytes32\\"},{\\"name\\":\\"counter\\",\\"type\\":\\"uint256\\"}],\\"OfferItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"}],\\"ConsiderationItem\\":[{\\"name\\":\\"itemType\\",\\"type\\":\\"uint8\\"},{\\"name\\":\\"token\\",\\"type\\":\\"address\\"},{\\"name\\":\\"identifierOrCriteria\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"startAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"endAmount\\",\\"type\\":\\"uint256\\"},{\\"name\\":\\"recipient\\",\\"type\\":\\"address\\"}]},\\"primaryType\\":\\"OrderComponents\\",\\"domain\\":{\\"name\\":\\"Seaport\\",\\"version\\":\\"1.4\\",\\"chainId\\":\\"5\\",\\"verifyingContract\\":\\"0x00000000000001ad428e4906aE43D8F9852d0dD6\\"},\\"message\\":{\\"offerer\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\",\\"offer\\":[{\\"itemType\\":\\"3\\",\\"token\\":\\"0x357Fd2942e8Ee435D7D21859ECAe99Bd597D8779\\",\\"identifierOrCriteria\\":\\"200\\",\\"startAmount\\":\\"1\\",\\"endAmount\\":\\"1\\"}],\\"consideration\\":[{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"9750000000000\\",\\"endAmount\\":\\"9750000000000\\",\\"recipient\\":\\"0x05ABA2d0A0f551E10e2E74489268206545e8C460\\"},{\\"itemType\\":\\"0\\",\\"token\\":\\"0x0000000000000000000000000000000000000000\\",\\"identifierOrCriteria\\":\\"0\\",\\"startAmount\\":\\"250000000000\\",\\"endAmount\\":\\"250000000000\\",\\"recipient\\":\\"0x0000a26b00c1F0DF003000390027140000fAa719\\"}],\\"startTime\\":\\"1681126098\\",\\"endTime\\":\\"1683718098\\",\\"orderType\\":\\"1\\",\\"zone\\":\\"0x004C00500000aD104D7DBd00e3ae0A5C00560C00\\",\\"zoneHash\\":\\"0x0000000000000000000000000000000000000000000000000000000000000000\\",\\"salt\\":\\"24446860302761739304752683030156737591518664810215442929808540646255434554088\\",\\"conduitKey\\":\\"0x0000007b02230091a7ed01230072f7006a004d60a8d4e71d599b8104250f0000\\",\\"totalOriginalConsiderationItems\\":\\"2\\",\\"counter\\":\\"0\\"}}"}'
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "cobo_id": "20221227195133000378036000001822"
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>