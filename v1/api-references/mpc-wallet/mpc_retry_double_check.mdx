---
title: Retry Double Check
api: POST /v1/custody/mpc/retry_double_check/ 
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint retries a double check for a withdraw request with the given request_id. The request must be a POST request and the user must have all permissions. The endpoint retrieves the withdraw request with the given request_id and checks if it exists and has a status of 'STATUS_WAITING_DOUBLE_CHECK'. If the request is valid, the endpoint locks the record and processes the withdraw request with the 'process_waiting_double_check_withdraw_request' method from the CustodyService class.
---
#### Request
<ParamField body="request_id" type="String" required>transaction request ID (unique identifier of a transaction request, which must correspond to a client's transaction; UUID should be used and the length should be equal to or less than 120 characters)</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">success is null. </ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/retry_double_check/",
    {
        "request_id": "722669426842706122"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/mpc/retry_double_check/', {
    "request_id": "722669426842706122"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/retry_double_check/", map[string]string{
    "request_id": "722669426842706122"
})
```
</RequestExample>

<ResponseExample>
```json
# double check success
{
  "success": true,
  "result": ""
}

# double check failed
{
  "success": false,
  "error_code": 1006,
  "error_message": "invalid request_id",
  "error_id": "6d9fd6b4a2e346b9953a3be47ebd0700",
  "error_description": "invalid request_id"
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>