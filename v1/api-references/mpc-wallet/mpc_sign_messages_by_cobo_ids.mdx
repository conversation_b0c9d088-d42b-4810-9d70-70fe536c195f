---
title: Get Signed Messages By Cobo ID
api: GET /v1/custody/mpc/sign_messages_by_cobo_ids/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing signed messages information for a list of Cobo IDs. The response includes the request ID, Cobo ID, signature and other relevant information.
---

<Note>This API is only intended for MPC web3 wallets and does not support MPC send/receive wallets.</Note>

#### Request
<ParamField query="cobo_ids" type="String" required>List of Cobo Unique IDs for signed messages, separated by commas, cannot be empty or greater than 50</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="sign_messages" type="object[]">
            <Expandable title="object">
                <ResponseField name="request_id" type="String" >Unique request ID</ResponseField>
                <ResponseField name="cobo_id" type="String" >Unique ID in Cobo</ResponseField>
                <ResponseField name="signature" type="String" >Signature</ResponseField>
                <ResponseField name="chain_code" type="String" >Chain code</ResponseField>
                <ResponseField name="coin_detail" type="object" >
                    <Expandable title="object">
                        <ResponseField name="coin" type="String" >Coin code</ResponseField>
                        <ResponseField name="chain_code" type="String" >Chain code</ResponseField>
                        <ResponseField name="display_code" type="String" >Abbreviation (reference only, subject to change)</ResponseField>
                        <ResponseField name="description" type="String" >Full name (reference only, subject to change)</ResponseField>
                        <ResponseField name="decimal" type="Int" >Decimal precision</ResponseField>
                        <ResponseField name="can_deposit" type="Bool" >Whether deposit is supported</ResponseField>
                        <ResponseField name="can_withdraw" type="Bool" >Whether withdraw is supported</ResponseField>
                        <ResponseField name="confirming_threshold" type="Int" >Number of confirmations required (may fluctuate)</ResponseField>
                    </Expandable>
                </ResponseField>
                <ResponseField name="sign_version" type="Int" >Sign version, EIP-191: 1, EIP-712: 2</ResponseField>
                <ResponseField name="from_address" type="String" >From address</ResponseField>
                <ResponseField name="extra_parameters" type="Json" >raw info of the message to be sign</ResponseField>
                <ResponseField name="created_time" type="Int" >Creation time of the transaction</ResponseField>
            </Expandable>
        </ResponseField>

      </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/sign_messages_by_cobo_ids/",
    {
        "cobo_ids": "20221222170157000336673000008161,20221222170157000336673000008162",
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/sign_messages_by_cobo_ids/', {
    "cobo_ids": "20221222170157000336673000008161,20221222170157000336673000008162",
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/sign_messages_by_cobo_ids/", map[string]string{
    "cobo_ids": "20221222170157000336673000008161,20221222170157000336673000008162",
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "sign_messages": [
      {
        "request_id": "1690349242683",
        "cobo_id": "20230726132723000341052000008222",
        "signature": "0x59328f02d00660d6c44d81e5bae985d2651614b5ee1e16764fd685530791748561b1f941a147f4e4ade4320b1c1d95179f9e058e622e632c020efbc476b2f9981c",
        "chain_code": "ETH",
        "coin_detail": {"coin": "ETH", "chain_code": "ETH", "display_code": "ETH", "description": "Ethereum Sepolia Testnet", "decimal": 18, "can_deposit": true, "can_withdraw": true, "confirming_threshold": 64},
        "from_address": "******************************************",
        "sign_version": 1,
        "extra_parameters": "{\"message\": \"YWFhYQ==\"}",
        "created_time": 1726026713801
      }
    ]
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>