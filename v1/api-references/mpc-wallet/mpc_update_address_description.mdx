---
title: Update Address Description
api: POST /v1/custody/mpc/update_address_description/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint updates the address description. The response contains the updated address along with their IDs, HD paths, encodings and description.
---

#### Request

<ParamField body="coin" type="String" required>The coin code for which the address are to be updated.</ParamField>
<ParamField body="address" type="String" required>The address to be updated.</ParamField>
<ParamField body="description" type="String" required>The address description.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="id" type="String" >The ID of the generated address.</ResponseField>
        <ResponseField name="address" type="String" >The generated address.</ResponseField>
        <ResponseField name="hd_path" type="String" >The HD path of the generated address.</ResponseField>
        <ResponseField name="encoding" type="Int" >The encoding of the generated address.For more information, please [refer to](/v1/api-references/development/address-encoding) the enum value corresponding to each encoding</ResponseField>
        <ResponseField name="description" type="String" >The address description.</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/update_address_description/",
    {
        "coin": "ETH",
        "address": "******************************************",
        "description": "TestAddress01"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/mpc/update_address_description/', {
    "coin": "ETH",
    "address": "******************************************",
    "description": "TestAddress01"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 5));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/update_address_description/", map[string]string{
    "coin": "ETH",
    "address": "******************************************",
    "description": "TestAddress01"
})
```
</RequestExample>

<ResponseExample>

```json
{
 "success": true,
 "result": {
	"id": 831483763545608598,
	"address": "******************************************",
	"encoding": 0,
	"hd_path": "m/44/60/0/0/0",
	"description": "TestAddress01"
 }
}


```
</ResponseExample>
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>