---
title: "Get Coin Details"
api: "GET /v1/custody/mpc/coin_info/"
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with the currency rate and unit for a given coin code. The endpoint requires authentication and takes a 'coin_code' parameter in the GET request.
---

<Tip> FAQ：[How do I query the withdraw fees?](/v1/faqs/coin-management#how-do-i-query-the-withdrawal-fees)</Tip>


#### Request
<ParamField query="coin" type="String" required>Coin code</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
    <Expandable title="object">
        <ResponseField name="coin" type="String">Coin code</ResponseField>
        <ResponseField name="display_code" type="String">Coin ticker symbol  (not unique, changeable, for reference only )</ResponseField>
        <ResponseField name="description" type="String">Coin code</ResponseField>
        <ResponseField name="decimal" type="Int">Coin balance decimal places</ResponseField>
        <ResponseField name="can_deposit" type="bool">Deposit accessibility</ResponseField>
        <ResponseField name="can_withdraw" type="bool">Withdraw accessibility</ResponseField>
        <ResponseField name="balance" type="String">Current balance</ResponseField>
        <ResponseField name="abs_balance" type="String">Absolute value of balance</ResponseField>
        <ResponseField name="fee_coin" type="String">Coin used for fee</ResponseField>
        <ResponseField name="abs_estimate_fee" type="String">Estimated fee</ResponseField>
        <ResponseField name="abs_estimate_fee_usd" type="String">Estimated fee(Convert to USD calculation) </ResponseField>
        <ResponseField name="confirming_threshold" type="Int">Confirmation threshold required for the coin</ResponseField>
        <ResponseField name="dust_threshold" type="Int">Coin minimum withdraw threshold</ResponseField>
        <ResponseField name="minimum_deposit_threshold" type="String">Coin minimum deposit threshold</ResponseField>
        <ResponseField name="token_address" type="String">Smart contract address for token (null for main-chain token)</ResponseField>
        <ResponseField name="require_memo" type="String">Memo for specified coins(EOS,XLM,XRP,IOST)</ResponseField>
        <ResponseField name="asset_model_type" type="String">Asset accounting method, 0: account model; 1: UTXO model</ResponseField>
    </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
  "GET",
  "/v1/custody/mpc/coin_info/",
  {
    "coin": "BTC",
  },
  api_key, api_secret, host
)
```

```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/coin_info/', {
    "coin": "BTC",
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```

```go Go
Request("GET", "/v1/custody/mpc/coin_info/", map[string]string{
    "coin": "BTC",
})
```

</RequestExample>

<ResponseExample>
```json
{
    "success": true,
    "result": {
        "coin": "BTC",
        "display_code": "BTC",
        "description": "Bitcoin",
        "decimal": 8,
        "can_deposit": true,
        "can_withdraw": true,
        "require_memo": false,
        "minimum_deposit_threshold": "10000",
        "balance": "0",
        "abs_balance": "0",
        "fee_coin": "BTC",
        "abs_estimate_fee": "0.00079252",
        "abs_estimate_fee_usd": "37.03",
        "confirming_threshold": 4,
        "dust_threshold": 546,
        "token_address": "",
        "asset_model_type": 1
    }
}

```
</ResponseExample>


<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>