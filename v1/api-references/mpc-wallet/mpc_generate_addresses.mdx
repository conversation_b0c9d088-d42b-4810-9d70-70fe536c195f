---
title: Generate Addresses
api: POST /v1/custody/mpc/generate_addresses/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint generates new addresses for a given chain code and returns a list of the generated addresses. The number of addresses to generate is specified in the 'count' parameter of the POST request. The maximum number of addresses that can be generated in one request is 200. The endpoint also checks if the organization has permission to add addresses and if the number of addresses being added exceeds the minutely limit. The response contains a list of the generated addresses along with their IDs, HD paths, and encodings.
---

<Tip> FAQ：[How to batch-generate addresses?](v1/faqs/address-management#how-to-batch-generate-addresses)</Tip>

#### Request

<ParamField body="chain_code" type="String" required>The chain code for which the addresses are to be generated.</ParamField>
<ParamField body="count" type="Int" required>The number of addresses to generate.</ParamField>
<ParamField body="encoding" type="Int">The encoding of addresses to generate. The default value is None, which means the default encoding type for each blockchain is used. For more information, please [refer to](/v1/api-references/development/address-encoding) the enum value corresponding to each encoding</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="addresses" type="object[]">
            <Expandable title="object">
                <ResponseField name="id" type="String" >The ID of the generated address.</ResponseField>
                <ResponseField name="address" type="String" >The generated address.</ResponseField>
                <ResponseField name="hd_path" type="String" >The HD path of the generated address.</ResponseField>
                <ResponseField name="encoding" type="Int" >The encoding of the generated address. For more information, please [refer to](/v1/api-references/development/address-encoding) the enum value corresponding to each encoding</ResponseField>
            </Expandable>
        </ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/generate_addresses/",
    {
        "chain_code": "ETH",
        "count": 2
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/mpc/generate_addresses/', {
    "chain_code": "ETH",
    "count": 2
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/generate_addresses/", map[string]string{
    "chain_code": "ETH",
    "count": 2
})
```
</RequestExample>

<ResponseExample>
```json
{
 "success": true,
 "result": {
      "addresses": [
        {
          "id": 721935051680543203,
          "address": "******************************************",
          "hd_path": "m/44/60/1/0/82",
          "encoding": 0
        },
        {
          "id": 721935051680543204,
          "address": "******************************************",
          "hd_path": "m/44/60/1/0/83",
          "encoding": 0
        }
      ]
   }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>