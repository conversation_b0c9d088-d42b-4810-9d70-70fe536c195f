---
title: Get Balance
api: GET /v1/custody/mpc/get_balance/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns the balance of a given address in a MPC wallet. The response contains information about the coins and NFTs held by the address, including their balance, decimal, and description.
---
#### Request 

<ParamField query="address" type="String" required>The address to get the balance of.</ParamField>
<ParamField query="chain_code" type="String" >The chain code of the coin to filter by.</ParamField>
<ParamField query="coin" type="String" >The asset coin of the coin to filter by.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="coin_data" type="object[]">
            <Expandable title="object">
                <ResponseField name="address" type="String" >The address of the coin.</ResponseField>
                <ResponseField name="coin" type="String" >The asset coin of the coin.</ResponseField>
                <ResponseField name="chain_code" type="String" >The chain code of the coin.</ResponseField>
                <ResponseField name="display_code" type="String" >Abbreviation (reference only, subject to change)</ResponseField>
                <ResponseField name="description" type="String" >Full name (reference only, subject to change)</ResponseField>
                <ResponseField name="balance" type="String" >The balance of the coin.</ResponseField>
                <ResponseField name="decimal" type="Int" >The decimal of the coin.</ResponseField>
            </Expandable>
        </ResponseField>

        <ResponseField name="nft_data" type="object[]">
            <Expandable title="object">
                <ResponseField name="address" type="String" >The address of the coin.</ResponseField>
                <ResponseField name="nft_code" type="String" >The NFT code.</ResponseField>
                <ResponseField name="token_id" type="String" >The token ID of the NFT.</ResponseField>
                <ResponseField name="contract_address" type="String" >The contract address of the NFT.</ResponseField>
                <ResponseField name="balance" type="String" >The balance of the NFT.</ResponseField>
                <ResponseField name="chain_code" type="String" >The chain code of the coin.</ResponseField>
            </Expandable>
        </ResponseField>

      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_balance/",
    {
        "address": "******************************************",
        "chain_code": "ETH",
        "coin": "ETH"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/get_balance/', {
    "address": "******************************************",
    "chain_code": "ETH",
    "coin": "ETH"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_balance/", map[string]string{
    "address": "******************************************",
    "chain_code": "ETH",
    "coin": "ETH"
})
```
</RequestExample>

<ResponseExample>
```json
{
 "success": true,
 "result": {
      "coin_data": [
        {
          "address": "******************************************",
          "coin": "ETH",
          "chain_code": "ETH",
          "display_code": "ETH",
          "description": "Ethereum",
          "balance": "49999999999999999987",
          "decimal": 18
        },
        {
          "address": "******************************************",
          "coin": "ETH",
          "chain_code": "ETH",
          "display_code": "ETH",
          "description": "Ethereum",
          "balance": "47290549999999999988",
          "decimal": 18
        }
      ],
      "nft_data": [
        {
          "nft_code": "NFT_ETH_BLUE_CHURCH",
          "token_id": "200",
          "address": "******************************************",
          "chain_code": "ETH",
          "contract_address": "******************************************",
          "balance": "1"
        }
      ]
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>