---
title: Is Valid Address
api: GET /v1/custody/mpc/is_valid_address/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint checks if a given address is valid for a specified coin. It also supports memos for certain coins. The response is a boolean indicating whether the address is valid or not.
---

<Tip> FAQ：[How to verify whether a withdraw address is legitimate?](/v1/faqs/address-management#how-to-verify-whether-a-withdrawal-address-is-legitimate)</Tip>

#### Request
<ParamField query="coin" type="String" required>Coin code</ParamField>
<ParamField query="address" type="String" required>address; please use ‘｜’ to separate the addresses if there’s also a memo</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="bool">Whether the address is valid or not.</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/is_valid_address/",
    {
        "coin": "ETH",
        "address": "******************************************"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/is_valid_address/', {
    "coin": "ETH",
    "address": "******************************************"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/is_valid_address/", map[string]string{
    "coin": "ETH",
    "address": "******************************************"
})
```
</RequestExample>

<ResponseExample>
```json
{
"success": true,
"result": false
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>