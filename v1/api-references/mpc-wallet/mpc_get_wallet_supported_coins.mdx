---
title: Get Wallet Supported Coins
api: GET /v1/custody/mpc/get_wallet_supported_coins/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with a list of supported coins for a specific MPC wallet. The response includes the coin code, chain code, display code, description, decimal, and other relevant information such as whether the coin can be deposited or withdrawn, and the confirming threshold.
---

<Note>This API is only intended for MPC send/receive wallets and does not support MPC web3 wallets.</Note>

#### Request
None

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="wallet_name" type="String" >MPC wallet name in Cobo Custody</ResponseField>
        <ResponseField name="coins" type="object[]">
            <Expandable title="object">
                <ResponseField name="coin" type="String" >Code of the coin.</ResponseField>
                <ResponseField name="chain_code" type="String" >Chain code of the coin.</ResponseField>
                <ResponseField name="display_code" type="String" >Abbreviation (reference only, subject to change)</ResponseField>
                <ResponseField name="description" type="String" >Description of the coin.</ResponseField>
                <ResponseField name="decimal" type="String" >Decimal places of the coin.</ResponseField>
                <ResponseField name="can_deposit" type="Bool" >Whether the coin can be deposited.</ResponseField>
                <ResponseField name="can_withdraw" type="Bool" >Whether the coin can be withdrawn.</ResponseField>
                <ResponseField name="confirming_threshold" type="Int" >Confirming threshold of the coin.</ResponseField>
            </Expandable>
        </ResponseField>
     </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_wallet_supported_coins/",
    {},
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch("GET", "/v1/custody/mpc/get_wallet_supported_coins/", {}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_wallet_supported_coins/", map[string]string{})
```
</RequestExample>

<ResponseExample>
```json
{
 "success": true,
  "result": {
      "wallet_name": "Test_Wallet",
      "coins": [
        {
          "coin": "GETH_UNI",
          "chain_code": "GETH",
          "display_code": "UNI",
          "description": "",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 32
        },
        {
          "coin": "ETH",
          "chain_code": "ETH",
          "display_code": "ETH",
          "description": "Ethereum",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 12
        }
      ]
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>