---
title: Change Binding
api: POST /v1/custody/guard/change_binding/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows authenticated users to change their binding to a new Prime Broker. The user's `broker_user_id` is passed in the request body, and the new `binder_id` is returned in the JSON response.
---

#### Request
<ParamField body="user_id" type="String" required>unique user ID</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="binder_id" type="String" >Binding ID, the binding ID needs to be prefixed with binder_id= to generate a QR code for the user to scan the code to bind, or the user can directly copy the binding code in the form of binder_id=xxxx to <PERSON><PERSON> for the binding operation</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/guard/change_binding/",
    {
        "user_id": "cobo_168108513539918"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/guard/change_binding/',
        {
           "user_id": "cobo_168108513539918"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/guard/change_binding/", map[string]string{
    "user_id": "cobo_168108513539918"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "binder_id": "Ph8YP+LTQyqcfHE/A+eXUw=="
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>