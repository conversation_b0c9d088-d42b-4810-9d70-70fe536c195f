---
title: Speedup Transaction
api: POST /v1/custody/mpc/speedup_transaction/ 
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows users to speed up a previously submitted transaction by replacing it with a new transaction with higher transation fee. The endpoint accepts a POST request with parameters including cobo_id, request_id, fee, gas_price, gas_limit, and fee_amount. The response is a JSON object containing the updated transaction information.
---

<Tip>
Only transactions with the status 401, 402, and 403 are eligible to be replaced.<br/ >
Once a transaction (the original transaction or the new transaction) on the chain reaches the required number of confirmations (transaction status changes to 900), other RBF-related transactions will be marked as replaced (transaction status changes to 901).
</Tip>

#### Request
<ParamField body="cobo_id" type="String" required>Unique transaction ID</ParamField>
<ParamField body="request_id" type="String" required>RBF transaction request ID (unique identifier of a transaction request, which must correspond to a client's transaction; UUID should be used and the length should be equal to or less than 120 characters)</ParamField>
<ParamField body="fee" type="Float" >Transaction fees per byte; must be higher than the original transaction fees; applicable to the UTXO model</ParamField>
<ParamField body="gas_price" type="Int" >The gas price for the new transaction(must be higher than the original gas price)</ParamField>
<ParamField body="gas_limit" type="Int" >The gas limit for the new transaction.</ParamField>
<ParamField body="fee_amount" type="Int" >transaction fee; applicable to UTXO model; use either fee or fee_amount for UTXO model.</ParamField>
<ParamField query="auto_fuel" type="Int">How the transaction utilizes the gas station, 0: no refueling(default); 1: passive use of the gas station; 2: active use of the gas station. <br /> This parameter is used when you've activated the gas station service and use the gas station to support the transaction fee for MPC transactions.</ParamField>
<Tip>The gas station will definitely refuel for the transaction when in active use, while for passive use, refueling by the gas station occurs only when the address balance is insufficient for transaction fees.</Tip>
<ParamField body="extra_parameters" type="String">
UTXO Model:
    - utxo_from_address: Given that the new transaction fee is higher, if the new transaction requires additional inputs, then the new inputs will be selected from `utxo_from_address`. Please note that the new inputs will be selected from the current wallet if this parameter is not provided.
```python Sample
extra_parameters: '{"utxo_from_address": "tb1qpllcnnpt3z9nkxtj3a0jmhdj5xvxaerhv5xs5n"}'
```
</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
            <ResponseField name="cobo_id" type="String" >Unique transaction ID</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/speedup_transaction/",
    {
        "cobo_id": "20221229151018000370189000003690",
        "request_id": "722669426842706122"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/mpc/speedup_transaction/', {
    "cobo_id": "20221229151018000370189000003690",
    "request_id": "722669426842706122"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/speedup_transaction/", map[string]string{
    "cobo_id": "20221229151018000370189000003690",
    "request_id": "722669426842706122"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "cobo_id": "20221227195133000378036000001822"
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>