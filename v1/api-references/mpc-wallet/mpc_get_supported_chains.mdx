---
title: Get Supported Chains
api: GET /v1/custody/mpc/get_supported_chains/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response with a 'chain_codes' field containing a list of supported chain codes for the MPC custody wallet associated with the authenticated user.
---
#### Request 
None

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="wallet_name" type="String">wallet name</ResponseField>
        <ResponseField name="chain_codes" type="String[ ]" >List of supported chain codes.</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_supported_chains/", {},
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/get_supported_chains/', {}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_supported_chains/", map[string]string{})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result":{
      "wallet_name": "My MPC Wallet",
      "chain_codes": [
        "BSC_BNB",
        "ETH",
        "GETH",
        "TBSC_BNB",
        "MATIC",
        "ARBITRUM_ETH"
      ]
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>