---
title: Query Statement By ID
api: GET /v1/custody/guard/query_statement/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows authenticated users to query a statement by providing a statement ID. The response is a JSON object containing information about the statement, including its ID, creation time, and a list of transactions. The endpoint requires the user to have query permission.
---
#### Request
<ParamField query="statement_id" type="String" required>unique auth statement id</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
        <ResponseField name="id" type="String" >unique auth statement id</ResponseField>
        <ResponseField name="status" type="String" >auth statement status (UNSEND: 1, WAIT: 2, APPROVE: 3, REJECT: 4, INVALID: 5, TIMEOUT: 6, SEND_TIMEOUT: 7, APPROVE_BY_OTHERS: 9, REJECT_BY_OTHERS: 10)</ResponseField>
        <ResponseField name="user_id" type="String" >unique user ID</ResponseField>
        <ResponseField name="pubkey" type="String" >Auth public key</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/guard/query_statement/",
    {
        "statement_id": "ABeWuAV7N2cwyWSxj3FW6gqcbUoqok8WzRHguqShLtXYVrmuEXWiPrcFBTJcPX24Q1CmZrxJiJCvZPTNK5ptqMhyXAuAbtRH6isJjG5Pq21jYzhCVGUoUVPHY5HTgDFCsrgFDsDVXGqUGi3ieWr26GWuAZUKX4L7s5kY"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/guard/query_statement/',
        {
            "statement_id": "ABeWuAV7N2cwyWSxj3FW6gqcbUoqok8WzRHguqShLtXYVrmuEXWiPrcFBTJcPX24Q1CmZrxJiJCvZPTNK5ptqMhyXAuAbtRH6isJjG5Pq21jYzhCVGUoUVPHY5HTgDFCsrgFDsDVXGqUGi3ieWr26GWuAZUKX4L7s5kY"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/guard/query_statement/", map[string]string{
    "statement_id": "ABeWuAV7N2cwyWSxj3FW6gqcbUoqok8WzRHguqShLtXYVrmuEXWiPrcFBTJcPX24Q1CmZrxJiJCvZPTNK5ptqMhyXAuAbtRH6isJjG5Pq21jYzhCVGUoUVPHY5HTgDFCsrgFDsDVXGqUGi3ieWr26GWuAZUKX4L7s5kY"
})
```
</RequestExample>

<ResponseExample>
```json

{
  "success": true,
  "result": {
    "id": "ABeWuAV7N2cwyWSxj3FW6gqcbUoqok8WzRHguqShLtXYVrmuEXWiPrcFBTJcPX24Q1CmZrxJiJCvZPTNK5ptqMhyXAuAbtRH6isJjG5Pq21jYzhCVGUoUVPHY5HTgDFCsrgFDsDVXGqUGi3ieWr26GWuAZUKX4L7s5kY",
    "status": 4,
    "user_id": "cobo_168108513539918",
    "pubkey": "480da241874516ca9a25b1776e2b3ce6d2d5ea184d9a83e28874e57d4a2786ea1db059aefb2093cf7775da68c5381cf401c128aa967927c3abc46c7545c90438"
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>