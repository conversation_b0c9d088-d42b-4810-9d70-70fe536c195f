---
title: Bind Addresses
api: POST /v1/custody/guard/bind_addresses/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows a prime broker to bind a list of chain addresses to a user account. The endpoint requires a POST request with a JSON payload containing the user ID and a list of chain addresses. If the payload is valid, the endpoint returns a JSON response with the result of the binding operation.
---
#### Request 

<ParamField body="user_id" type="String" required>The ID of the user account to bind the addresses to.</ParamField>
<ParamField body="addresses" type="Json" required>A list of chain addresses to bind to the user account.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="pubkey" type="String" >The bound Auth public key, or empty if the binder_id has expired</ResponseField>
        <ResponseField name="addresses" type="object[]" >
          <Expandable title="object">
            <ResponseField name="chain_coin" type="String" >bound coin</ResponseField>
            <ResponseField name="address" type="String" >bound address</ResponseField>
          </Expandable>
        </ResponseField>

      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/guard/bind_addresses/",
    {
        "user_id": "cobo_168108513539918",
        "addresses": '[{"chain_coin": "VET", "address": "******************************************"}]'
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/guard/bind_addresses/',
        {
          "user_id": "cobo_168108513539918",
          "addresses": '[{"chain_coin": "VET", "address": "******************************************"}]'
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/guard/bind_addresses/", map[string]string{
    "user_id": "cobo_168108513539918",
    "addresses": '[{"chain_coin": "VET", "address": "******************************************"}]'
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "pubkey": "480da241874516ca9a25b1776e2b3ce6d2d5ea184d9a83e28874e57d4a2786ea1db059aefb9023cf7775da68c5381cf401c128aa967927c3abc46c7545c90438",
    "addresses": [
      {
        "chain_coin": "ETH",
        "address": "0x123"
      }
      …
    ]
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>