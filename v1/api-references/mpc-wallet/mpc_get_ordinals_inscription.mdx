---
title: Get Ordinals Inscription
api: GET /v1/custody/mpc/ordinals_inscription/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing the inscription content within the given inscription ID.

---
#### Request
<ParamField query="inscription_id" type="String" required>Inscription ID</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
  <Expandable title="object">
     <ResponseField name="content" type="String" >Inscription content</ResponseField>
  </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/ordinals_inscription/",
    {
        "inscription_id": "4552501265556cf20890b75cbd3fce93386c8f2e09be316e1b820aa2eed0ed07i0"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/ordinals_inscription/', {
    "inscription_id": "4552501265556cf20890b75cbd3fce93386c8f2e09be316e1b820aa2eed0ed07i0"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/ordinals_inscription/", map[string]string{
    "inscription_id": "4552501265556cf20890b75cbd3fce93386c8f2e09be316e1b820aa2eed0ed07i0"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "content": "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"
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>