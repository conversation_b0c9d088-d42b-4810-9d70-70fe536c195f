---
title: Get Spendable List
api: GET /v1/custody/mpc/list_spendable/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing a list of spendable unspent transaction outputs (UTXOs) for a given asset coin and address. The response includes the transaction hash, output index, address, amount, and confirmation status of each UTXO.
---

<Note>This API is only intended for MPC send/receive wallets and does not support MPC web3 wallets.</Note>

#### Request
<ParamField query="coin" type="String" required>The asset coin symbol (e.g. BTC, LTC).</ParamField>
<ParamField query="address" type="String" >The address to filter UTXOs by.</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="tx_hash" type="String" >Transaction hash of the UTXO.</ResponseField>
        <ResponseField name="vout_n" type="Int" >Output index of the UTXO.</ResponseField>
        <ResponseField name="address" type="String" >Address of the UTXO.</ResponseField>
        <ResponseField name="amount" type="Int" >UTXO amount in decimal places (e.g. one bitcoin is divisible to eight decimal places, and 100000000 represents 1 BTC).</ResponseField>
        <ResponseField name="is_coinbase" type="Bool" >Whether the UTXO is a coinbase transaction</ResponseField>
        <ResponseField name="confirmed_number" type="Int" >Number of confirmations for the UTXO.</ResponseField>

      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/list_spendable/",
    {
        "coin": "BTC",
        "address": ""
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/list_spendable/', {
    "coin": "BTC",
    "address": ""
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/list_spendable/", map[string]string{
    "coin": "BTC",
    "address": ""
})
```
</RequestExample>

<ResponseExample>
```json

{
  "success": true,
  "result": {
    'utxos': [{
      'tx_hash': 'dd7e1cecf6bbde1844ee1815b780711a1e306a718bcd23cd64401b48ef88eb83',
      'vout_n': 0,
      'address': '2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE',
      'amount': 100000,
      'is_coinbase': False,
      'confirmed_number': 66716
    }, {
      'tx_hash': 'dd7e1cecf6bbde1844ee1815b780711a1e306a718bcd23cd64401b48ef88eb83',
      'vout_n': 1,
      'address': '2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE',
      'amount': 99270,
      'is_coinbase': False,
      'confirmed_number': 66716
    }]
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>