---
title: Get Tss Node Requests List
api: GET /v1/custody/mpc/list_tss_node_requests/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing a list of TSS (Threshold Signature Scheme) requests with their metadata. The requests can be filtered by request type and status. The metadata includes the COBO ID, request type, status, failed reason (if any), and additional information specific to the request type (e.g., threshold and node IDs for key generation requests).
---
#### Request
<ParamField query="request_type" type="Int" >Request type(KEYGEN type: 1, KEYRESHARE type: 2)</ParamField>
<ParamField query="status" type="int" >Request status</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="requests" type="object[]" >
          <Expandable title="object">
                <ResponseField name="cobo_id" type="String" >Unique request ID</ResponseField>
                <ResponseField name="request_type" type="Int" >Request type(KEYGEN type: 1, KEYRESHARE type: 2)</ResponseField>
                <ResponseField name="status" type="Int" >Request status（NEW: 100, WAITING_APPROVAL: 200, PENDING: 300, SUCCESS: 400, FAILED: 500）</ResponseField>
                <ResponseField name="failed_reason" type="String" >Reason for failure</ResponseField>
                <ResponseField name="meta" type="object[]" >
                  <Expandable title="object">
                      <ResponseField name="key_gen" type="object[]" >
                      <Expandable title="object">
                            <ResponseField name="cobo_id" type="String" >Unique request ID</ResponseField>
                            <ResponseField name="threshold" type="Int" >Signature Threshold</ResponseField>
                            <ResponseField name="node_ids" type="String[ ]" >Request data associated with key generation (i.e. KeyGen) and soft key recovery (i.e. KeyShare)</ResponseField>
                       </Expandable>
                     </ResponseField>
                  </Expandable>
                </ResponseField>
          </Expandable>
         </ResponseField>

       </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/list_tss_node_requests/",
    {
        "request_type": 1,
        "status": 400
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/list_tss_node_requests/', {
    "request_type": 1,
    "status": 400
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/list_tss_node_requests/", map[string]string{
    "request_type": 1,
    "status": 400
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "requests": [
      {
        "cobo_id": "20221205155958000350435000006699",
        "request_type": 1,
        "status": 400,
        "failed_reason": null,
        "meta": {
          "key_gen": {
            "cobo_id": "20221205155958000350435000006699",
            "threshold": 2,
            "node_ids": [
              "cobo5yb7BNEBwwp5XXedbhnzQfvQtp132W4dH4Jz4x4eDp4KA",
              "coboMbU7KqoVhXJSyyFeJ2fZWbXQWtZvs1WjmhnzXFB7CTowo",
              "coboWS6hKma2MxxobbpjB8W2SVmpBiisaBq4rxqVpWJAZpecK"
            ]
          }
        }
      }
    ]
  }
}




```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>