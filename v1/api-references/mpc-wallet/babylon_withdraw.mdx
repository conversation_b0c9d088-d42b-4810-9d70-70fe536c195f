---
title: Babylon Withdraw
api: POST /v1/custody/mpc/babylon/withdraw/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint performs the withdraw operation for a given staking transaction.
---

#### Request
<ParamField body="request_id" type="String" required>transaction ID (unique identifier of a transaction request, which must correspond to a client's transaction; UUID should be used and the length should be equal to or less than 120 characters)</ParamField>
<ParamField body="fee_rate" type="Float" required>transaction fees per byte</ParamField>
<ParamField body="max_fee_amount" type="Int" >maximum fee for constructing the transaction. If the actual fee exceeds this limit, the creation will fail. The default is None.</ParamField>
<ParamField body="staking_request_id" type="String">request ID of the staking transaction to be withdrawn</ParamField>
<Tip>Please note that only staking transactions with status 520 are eligible to be withdrawn.</Tip>
<ParamField body="unbonding_request_id" type="String">request ID of the unbonding transaction</ParamField>
<Tip>Please note that the `staking_request_id` and `unbonding_request_id` parameters are mutually exclusive. If the staking transaction has naturally expired, please provide the `staking_request_id` parameter. If the staking transaction has been successfully unbonded, please provide the `unbonding_request_id` parameter.</Tip>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="String">""</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/babylon/withdraw/",
    {
        "request_id": "1717059604245",
        "fee_rate": 9.5,
        "max_fee_amount": 2000,
        "unbonding_request_id": "1717059601673",
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/mpc/babylon/withdraw/', {
    "request_id": "1717059604245",
    "fee_rate": 9.5,
    "max_fee_amount": 2000,
    "unbonding_request_id": "1717059601673",
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/babylon/withdraw/", map[string]string{
    "request_id": "1717059604245",
    "fee_rate": 9.5,
    "max_fee_amount": 2000,
    "unbonding_request_id": "1717059601673",
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": ""
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>