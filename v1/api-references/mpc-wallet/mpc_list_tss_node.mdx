---
title: Get Tss Node List
api: GET /v1/custody/mpc/list_tss_node/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing a list of TSS Nodes that configured on the Custody MPC Setting page. The response includes node id, node type and if the node is online.
---

#### Request

None

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">

        <ResponseField name="tss_nodes" type="object[]" >
          <Expandable title="object">
                <ResponseField name="node_id" type="String" >Unique TSS Node ID</ResponseField>
                <ResponseField name="type" type="Int" >TSS Node type(third party: 1, node managed by yourself: 2, <PERSON><PERSON>: 3)</ResponseField>
                <ResponseField name="online" type="Bool" >if TSS node is online (online: true，offline: false)</ResponseField>
          </Expandable>
         </ResponseField>

       </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/list_tss_node/",
    {},
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/list_tss_node/', {}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/list_tss_node/", map[string]string{})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "tss_nodes": [
      {
        "node_id": "cobo7k8y49r1W9XtkSUs7v267mQmgZ7ZaV7W2noHgxA6rAJBZ",
        "type": 2,
        "online": true
      },
      {
        "node_id": "coboMbU7KqoVhXJSyyFeJ2fZWbXQWtZvs1WjmhnzXFB7CTowo",
        "type": 1,
        "online": true
      },
      {
        "node_id": "coboWS6hKma2MxxobbpjB8W2SVmpBiisaBq4rxqVpWJAZpecK",
        "type": 3,
        "online": true
      }
    ]
  }
}

```
</ResponseExample>
```

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>