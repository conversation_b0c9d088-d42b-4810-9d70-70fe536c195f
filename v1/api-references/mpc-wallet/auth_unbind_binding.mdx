---
title: Unbind Binding
api: POST /v1/custody/guard/unbind_binding/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint allows users to unbind their broker account from their MPC Wallet account. It requires a POST request with a 'user_id' field in the request body. The response is a JSON object containing a 'statement_id' field which can be used to query the status of the unbinding process.
---

#### Request

<ParamField body="user_id" type="String" required>unique user ID</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
           <ResponseField name="statement_id" type="String" >unique auth statement id</ResponseField>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/guard/unbind_binding/",
    {
        "user_id": "cobo_168108513539918"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/guard/unbind_binding/',
        {
            "user_id": "cobo_168108513539918"
        },
        api_key, api_secret, host
    ).then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/guard/unbind_binding/", map[string]string{
    "user_id": "cobo_168108513539918"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "statement_id": "ABeWuAV7N2cwyWSxj3FW6gqcbUoqok8WzRHguqShLtXYVrmuEXWiPrcFBTJcPX24Q1CmZrxJiJCvZPTNK5ptqMhyXAuAbtRH6isJjG5Pq21jYzhCVGUoUVPHY5HTgDFCsrgFDsDVXGqUGi3ieWr26GWuAZUKX4L7s5kY"
  }
}



```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>