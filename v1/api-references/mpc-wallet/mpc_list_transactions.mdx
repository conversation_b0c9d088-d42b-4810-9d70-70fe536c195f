---
title: Get Transactions List
api: GET /v1/custody/mpc/list_transactions/
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a list of transactions filtered by various parameters such as start and end time, transaction status, transaction type, coins, and addresses. The response is a JSON object containing a list of transactions.
---

#### Request
<ParamField query="start_time" type="Int" >≥ start time (unit: milliseconds); details of transactions whose creation times are equal to or later than this timestamp will be returned</ParamField>
<ParamField query="end_time" type="Int" >＜ end time (unit: milliseconds); details of transactions whose creation times are earlier than this timestamp will be returned</ParamField>
<ParamField query="status" type="Int" >
| Status Type | Code |
| ----------- | ----------- |
| PENDING_APPROVAL| 101|
| QUEUED| 201|
| PENDING_SIGNATURE| 301|
| BROADCASTING| 401|
| BROADCAST_FAILED| 402|
| PENDING_CONFIRMATION| 403|
| CONFIRMATION| 501|
| REVERTING| 502|
| SUCCESS| 900|
| FAILED| 901|
| REORG| 902|

<Tip>The 403 status indicates that the transaction has been successfully broadcast but not yet confirmed, while the 501 status indicates that the transaction has been successfully confirmed on the blockchain and is awaiting further confirmations.</Tip>
</ParamField>
<ParamField query="order_by" type="String" >sorting method; default: created_time; other option: modified_time</ParamField>
<ParamField query="order" type="String" >sorting order; options: ASC (default), DESC</ParamField>
<ParamField query="transaction_type" type="Int" >
**only use in non-web3 wallet:**
| Transaction Type | Code |
| ----------- | ----------- |
| TYPE_MPC_WEB| 100|
| TYPE_MPC_API| 102|
| TYPE_RBF_API_SPEEDUP| 103|
| TYPE_RBF_WEB_SPEEDUP| 104|
| TYPE_RBF_API_DROP| 105|
| TYPE_RBF_WEB_DROP| 106|
| TYPE_MPC_TRANSACTION_FROM_EXTERNAL| 107|
| TYPE_MPC_RESEND_WEB| 108|
| TYPE_MPC_BABYLON_STAKE| 500|
| TYPE_MPC_BABYLON_STAKE_RBF| 501|
| TYPE_FROM_DEPOSIT| 1000|

**only use in web3 wallet:**
| Transaction Type | Code |
| ----------- | ----------- |
| TYPE_MPC_WEB3_WEB| 300|
| TYPE_MPC_WEB3_MMI_TX| 301|
| TYPE_MPC_WEB3_API_TRANSACTION| 303|
| TYPE_MPC_WEB3_TRANSACTION_FROM_EXTERNAL| 307|
| TYPE_MPC_WEB3_RBF_API_SPEEDUP| 308|
| TYPE_MPC_WEB3_RBF_WEB_SPEEDUP| 309|
| TYPE_MPC_WEB3_RBF_API_DROP| 310|
| TYPE_MPC_WEB3_RBF_WEB_DROP| 311|
| TYPE_FROM_DEPOSIT| 1000|

</ParamField>
<ParamField query="coins" type="String" >coin codes; separated by commas</ParamField>
<ParamField query="from_address" type="String" >from address</ParamField>
<ParamField query="to_address" type="String" >to address</ParamField>
<ParamField query="limit" type="Int" >entries per page; max: 50; default: 50</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object[]">
      <Expandable title="object">
          <Snippet file="mpc_transaction_snippet.mdx"/>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/list_transactions/",
    {
        "start_time": "",
        "end_time": "",
        "status": 900,
        "order_by": "",
        "order": "",
        "transaction_type": 303,
        "coins": "",
        "from_address": "",
        "to_address": "",
        "limit": 2
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/list_transactions/', {
    "start_time": "",
    "end_time": "",
    "status": 900,
    "order_by": "",
    "order":"",
    "transaction_type": 303,
    "coins": "",
    "from_address": "",
    "to_address": "",
    "limit": 2
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/list_transactions/", map[string]string{
    "start_time": "",
    "end_time": "",
    "status": 900,
    "order_by": "",
    "order":"",
    "transaction_type": 303,
    "coins": "",
    "from_address": "",
    "to_address": "",
    "limit": 2
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "total": 28,
    "transactions": [
      {
        "cobo_id": "20221219161751000350944000003727",
        "request_id": "1671437866485",
        "status": 900,
        "coin_detail": {
          "coin": "GETH",
          "display_code": "GETH",
          "description": "Ethereum Goerli Testnet",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 32
        },
        "amount_detail": {
          "amount": "10",
          "abs_amount": "0.00000000000000001"
        },
        "fee_detail": {
          "fee_coin_detail": {
            "coin": "GETH",
            "display_code": "GETH",
            "description": "Ethereum Goerli Testnet",
            "decimal": 18,
            "can_deposit": True,
            "can_withdraw": True,
            "confirming_threshold": 32
          },
          "gas_price": 99609,
          "gas_limit": 21000,
          "fee_used": 2091789000
        },
        "source_addresses": null,
        "from_address": "******************************************",
        "to_address": "******************************************",
        "tx_hash": "0x224cd23f20a8dfbbe5418871f6a32ddee4463b31e77b5090e7efff1bd8bb6b74",
        "vout_n": 0,
        "nonce": 0,
        "confirmed_number": 32,
        "replace_cobo_id": "",
        "transaction_type": 303,
        "operation": 100,
        "block_detail": {
          "block_hash": "0x5d74fb7415f10d5308599c0ca037e622da7aea75cb5e0af20d492f9d5e8150ab",
          "block_height": 8162134,
          "block_time": 1671438048000
        },
        "tx_detail": {
          "tx_hash": "0x224cd23f20a8dfbbe5418871f6a32ddee4463b31e77b5090e7efff1bd8bb6b74"
        },
        "extra_parameters": "",
        "created_time": 1671437871175,
        "updated_time": 1671438549853,
        "failed_reason": null,
        "max_priority_fee": null,
        "max_fee": null,
        "approval_process": {"spender_result": 1, "spender_review_threshold": 1, "spender_status": [{"spender_person": "Cobo_Test_spender", "status": "approve"}], "spender_complete_time": 1681906749496, "approver_result": 1, "approver_review_threshold": 1, "approver_status": [{"approve_person": "Cobo_Test_approver", "status": "approve"}], "approver_complete_time": 1681906758619},
        "remark": "",
        "gas_station_child_id": ""
      },
      {
        "cobo_id": "20221219192126000352912000006483",
        "request_id": "test_002",
        "status": 900,
        "coin_detail": {
          "coin": "GETH",
          "display_code": "GETH",
          "description": "Ethereum Goerli Testnet",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 32
        },
        "amount_detail": {
          "amount": "1",
          "abs_amount": "0.000000000000000001"
        },
        "fee_detail": {
          "fee_coin_detail": {
            "coin": "GETH",
            "display_code": "GETH",
            "description": "Ethereum Goerli Testnet",
            "decimal": 18,
            "can_deposit": True,
            "can_withdraw": True,
            "confirming_threshold": 32
          },
          "gas_price": 4362985,
          "gas_limit": 21000,
          "fee_used": 91622685000
        },
        "source_addresses": null,
        "from_address": "******************************************",
        "to_address": "******************************************",
        "tx_hash": "0x532770380f9418849782fb01f5b62189d44b999b153767fee26ade7592b89888",
        "vout_n": 0,
        "nonce": 1,
        "confirmed_number": 33,
        "replace_cobo_id": "",
        "transaction_type": 303,
        "operation": 0,
        "block_detail": {
          "block_hash": "0x779a310c97d50d8c49e417d5518132221a7052ce3dfe7df5c2857444235b98fc",
          "block_height": 8162891,
          "block_time": 1671449160000
        },
        "tx_detail": {
          "tx_hash": "0x532770380f9418849782fb01f5b62189d44b999b153767fee26ade7592b89888"
        },
        "extra_parameters": "",
        "created_time": 1671448916625,
        "updated_time": 1671449659725,
        "failed_reason": null,
        "max_priority_fee": null,
        "max_fee": null,
        "approval_process": {"spender_result": 1, "spender_review_threshold": 1, "spender_status": [{"spender_person": "Cobo_Test_spender", "status": "approve"}], "spender_complete_time": 1681906749496, "approver_result": 1, "approver_review_threshold": 1, "approver_status": [{"approve_person": "Cobo_Test_approver", "status": "approve"}], "approver_complete_time": 1681906758619},
        "remark": "",
        "memo": "",
        "gas_station_child_id": ""
      }
    ]
  }
}


```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>