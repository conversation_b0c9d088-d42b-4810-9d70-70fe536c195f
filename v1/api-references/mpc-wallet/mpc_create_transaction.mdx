---
title: Create Transaction
api: POST /v1/custody/mpc/create_transaction/ 
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint creates a transaction for a given coin and request ID. The request must include the amount, from_address, and to_address. Optional parameters include fee, gas_price, gas_limit, max_fee, max_priority_fee, operation, and extra_parameters. The endpoint returns a JSON response with a 'cobo_id' field containing the ID of the created transaction.
---

#### Request
<ParamField body="coin" type="String" required>coin code</ParamField>
<ParamField body="request_id" type="String" required>transaction ID (unique identifier of a transaction request, which must correspond to a client's transaction; UUID should be used and the length should be equal to or less than 120 characters)</ParamField>
<ParamField body="amount" type="Int">
transaction amount in decimal places (e.g. one bitcoin is divisible to eight decimal places, and ********* represents 1 BTC)

Account Model: this parameter is required

UTXO Model: this parameter is optional. The transaction sending amount will be based on the `to_address_details` parameter if the `amount` is zero or empty
</ParamField>
<ParamField body="from_address" type="String" >
Account Model: this parameter is required

UTXO Model: this parameter is optional, the input will be selected based off the `extra_parameters` parameter if the `from_address` is empty or not passed. The `from_address` will also be used as the change address of UTXO transactions when utxo_change_address not specified in extra_parameters
</ParamField>
<ParamField body="to_address" type="String" >to address</ParamField>
<ParamField body="to_address_details" type="String" >
list of addresses used for receiving coins associated with the UTXO model; this value must be a JSON string and has a parameter type that fits.
<br/>Please note that the `to_address_details` parameter and `amount` parameter are alternative. Please set the `amount` parameter to 0 or leave it empty when using the `to_address_details` parameter.
```python Sample
to_address_details: '[{"to_address": "2N4J1WajwKZKpRtzzUmaW9B5GEqkppqdVY9","amount": "547"},
                       {"to_address": "tb1qycxy8d8jgff8hql09y62smw5s5mrf3ryn2j9lk","amount": "548"}]'
```
</ParamField>

<ParamField body="fee" type="Float" >transaction fees per byte; applicable to UTXO model; use either fee or fee_amount for UTXO model</ParamField>
<Tip>If you have previously called the `estimate_fee` API to obtain an estimated fee for UTXO transactions before creating the transaction, please use the `fee_amount` parameter instead of `fee` to specify the transaction fee. Otherwise, it will re-estimate the transaction fee based on the `fee` parameter when creating the transaction.</Tip>
<ParamField body="gas_price" type="Int" >gas price; applicable to ETH; unit: wei</ParamField>
<ParamField body="gas_limit" type="Int" >gas limit, applicable to ETH</ParamField>
<ParamField body="operation" type="Int" >
action type, 
100: TRANSFER (default);
200: CONTRACT_CALL
</ParamField>
<ParamField body="extra_parameters" type="String" >
UTXO Model:
    1. **input_selection:** inputs to be excluded and included in transactions associated with the UTXO model; inputs must be in correspondence with from_address (if specified); it must be a JSON string and has a structure that fits InputSelection
    2. **inputs_to_spend:** UTXO you want as input. Please note that the length of the list of UTXOs should be less than or equal to 100.
        - tx_hash: Transaction tx_hash
        - vout_n: UTXO index
    3. **inputs_to_exclude:** UTXO you don't want as input
        - tx_hash: Transaction tx_hash
        - vout_n: UTXO index
    4. **utxo_change_address:** the change address for UTXO transactions. if parameter from_address passed and utxo_change_address not passed, from_address will be used as the change address. otherwise, the permanent address (SegWit) will be used as the change address
```python Sample
extra_parameters: '{"input_selection":
                        {"inputs_to_spend":[
                              {"tx_hash": "e7ddfa61cc2a78e1661da44b10cdc862256573d2dc0485dc989fb7045ed9032c", "vout_n": 1},
 			                  {"tx_hash": "eabaee1f1a196b087e78303c6eaaeb9d1166de5f8d85f4ef7872b88bfcd60914", "vout_n": 2}],
                        "inputs_to_exclude":[
 		                      {"tx_hash": "e7ddfa61cc2a78e1661da44b10cdc862256573d2dc0485dc989fb7045ed9032c", "vout_n": 3},
 			                  {"tx_hash": "eabaee1f1a196b087e78303c6eaaeb9d1166de5f8d85f4ef7872b88bfcd60914", "vout_n": 4}]
                        },
                     "utxo_change_address": "2MznT7D2tKZKshfQPfhTuLYB7Bw9TZqb17w"
 			        }'
```
Web3 wallet:
    - calldata: request data associated with an EVM transaction; When deploying a new contract, the `to_address` parameter should be set to `******************************************`. Please note that when using `calldata`, the `gas_limit` parameter should be set to an appropriate value based on the data being sent and the contract's execution requirements.
```python Sample
extra_parameters: '{"calldata": "0xa8059cbb000000000000000000000000971948873f869e4517311b190d7eb31e30eba0ef000000000000000000000000000000000000000000000000002386f26fc10000"}'
```
    - instructions: request data associated with a Solana transaction. This parameter is in JSON format and contains a list of instructions. Each instruction includes the following parameters:
      - accounts: a list of accounts associated with Solana contract, each of which has the following attributes:
        - pubkey: account address. If the account is signer, `pubkey` needs to match the `from_address` parameter.
        - is_signer: boolean value indicating whether the account can sign transactions.
        - is_writable: boolean value indicating whether the account can be modified.
      - data: data used for calling Solana contract.
      - program_id: contract address. when calling a Solana contract, the `to_address` parameter needs to match the `program_id` parameter. If multiple contracts are being called, then the `to_address` parameter should match the `program_id` parameter of the first instruction.
```python Sample
extra_parameters: '{"instructions": "[{\\"accounts\\":[{\\"pubkey\\":\\"E4MhQWiqCLER3fFZNf8LyQFpLWW3BRxtsR5eps3c3vNS\\",\\"is_signer\\":true,\\"is_writable\\":true}],\\"data\\":\\"Canary TX 4, slot: *********\\",\\"program_id\\":\\"MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr\\"}]"}'
```
</ParamField>

<ParamField body="fee_amount" type="Int" >transaction fee; applicable to UTXO model; use either fee or fee_amount for UTXO model</ParamField>
<ParamField body="remark" type="String">The remark to withdraw. The length should be less than 2048 characters</ParamField>
<ParamField body="auto_fuel" type="Int">How the transaction utilizes the gas station, 0: no refueling(default); 1: passive use of the gas station; 2: active use of the gas station. <br /> This parameter is used when you've activated the gas station service and use the gas station to support the transaction fee for MPC transactions.</ParamField>
<Tip>The gas station will definitely refuel for the transaction when in active use, while for passive use, refueling by the gas station occurs only when the address balance is insufficient for transaction fees.<br /><br />Please note that due to the TRON chain's fee delegation mechanism, only no refueling and active use of the gas station are supported for the TRON chain; passive use of the gas station is not supported.</Tip>
<ParamField body="memo" type="String">Needed when you withdraw XRP</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
            <ResponseField name="cobo_id" type="String" >Unique transaction ID</ResponseField>
      </Expandable>
</ResponseField>


<RequestExample>
```python Python

# TRANSFER example:
request(
    "POST",
    "/v1/custody/mpc/create_transaction/",
    {
        "coin": "VET",
        "from_address": "******************************************",
        "to_address": "******************************************",
        "amount": 1,
        "request_id": "1671699717647"
    },
    api_key, api_secret, host
)


# UTXO example:
request(
    "POST",
    "/v1/custody/mpc/create_transaction/",
    {
        'coin': 'XTN',
        'request_id': 'mpc_api_1674896802004',
        'to_address': '2N4J1WajwKZKpRtzzUmaW9B5GEqkppqdVY9',
        'amount': '400003',
        'extra_parameters': '{"input_selection":
                                {"inputs_to_spend":
                                    [{"tx_hash": "e7ddfa61cc2a78e1661da44b10cdc862256573d2dc0485dc989fb7045ed9032c", "vout_n": 1},
                                     {"tx_hash": "eabaee1f1a196b087e78303c6eaaeb9d1166de5f8d85f4ef7872b88bfcd60914", "vout_n": 2}]
                                }
                             }'
    }
    api_key, api_secret, host


# Web3 example:
request(
    "POST",
    "/v1/custody/mpc/create_transaction/",
    {
        'coin': 'GETH',
        'request_id': 'mpc_api_1674896802004',
        'from_address': '******************************************',
        'to_address': '******************************************',
        'amount': '*********',
        'gas_price':2613036
        'gas_limit':21000
        'extra_parameters':'{"calldata": "0xa8059cbb000000000000000000000000971948873f869e4517311b190d7eb31e30eba0ef000000000000000000000000000000000000000000000000002386f26fc10000"}'
    }
    api_key, api_secret, host

```
```javascript JavaScript

//TRANSFER example:
coboFetch('POST', '/v1/custody/mpc/create_transaction/', {
    "coin": "VET",
    "from_address": "******************************************",
    "to_address": "******************************************",
    "amount": 1,
    "request_id": "1671699717647"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });

//UTXO example:
coboFetch('POST', '/v1/custody/mpc/create_transaction/',{
        'coin': 'XTN',
        'request_id': 'mpc_api_1674896802004',
        'to_address': '2N4J1WajwKZKpRtzzUmaW9B5GEqkppqdVY9',
        'amount': '400003',
        'extra_parameters': '{"input_selection":
                                {"inputs_to_spend":
                                    [{"tx_hash": "e7ddfa61cc2a78e1661da44b10cdc862256573d2dc0485dc989fb7045ed9032c", "vout_n": 1},
                                     {"tx_hash": "eabaee1f1a196b087e78303c6eaaeb9d1166de5f8d85f4ef7872b88bfcd60914", "vout_n": 2}]
                                }
                             }'
    }, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });


//Web3 example:
coboFetch('POST', '/v1/custody/mpc/create_transaction/',{
        'coin': 'GETH',
        'request_id': 'mpc_api_1674896802004',
        'from_address': '******************************************',
        'to_address': '******************************************',
        'amount': '*********',
        'gas_price':2613036
        'gas_limit':21000
        'extra_parameters':'{"calldata": "0xa8059cbb000000000000000000000000971948873f869e4517311b190d7eb31e30eba0ef000000000000000000000000000000000000000000000000002386f26fc10000"}'
    }, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });

```
```go Go
//TRANSFER example:
Request("POST", "/v1/custody/mpc/create_transaction/", map[string]string{
    "coin": "VET",
    "from_address": "******************************************",
    "to_address": "******************************************",
    "amount": 1,
    "request_id": "1671699717647"
})

//UTXO example:
Request("POST", "/v1/custody/mpc/create_transaction/", map[string]string{
        'coin': 'XTN',
        'request_id': 'mpc_api_1674896802004',
        'to_address': '2N4J1WajwKZKpRtzzUmaW9B5GEqkppqdVY9',
        'amount': '400003',
        'extra_parameters': '{"input_selection":
                                {"inputs_to_spend":
                                    [{"tx_hash": "e7ddfa61cc2a78e1661da44b10cdc862256573d2dc0485dc989fb7045ed9032c", "vout_n": 1},
                                     {"tx_hash": "eabaee1f1a196b087e78303c6eaaeb9d1166de5f8d85f4ef7872b88bfcd60914", "vout_n": 2}]
                                }
                             }'
    })

//Web3 example:
Request("POST", "/v1/custody/mpc/create_transaction/", map[string]string{
        'coin': 'GETH',
        'request_id': 'mpc_api_1674896802004',
        'from_address': '******************************************',
        'to_address': '******************************************',
        'amount': '*********',
        'gas_price':2613036
        'gas_limit':21000
        'extra_parameters':'{"calldata": "0xa8059cbb000000000000000000000000971948873f869e4517311b190d7eb31e30eba0ef000000000000000000000000000000000000000000000000002386f26fc10000"}'
    })
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "cobo_id": "20221227195133000378036000001822"
  }
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>