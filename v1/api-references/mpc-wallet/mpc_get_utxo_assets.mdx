---
title: Get UTXO Assets
api: GET /v1/custody/mpc/get_utxo_assets/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns a JSON response containing the asset information within the given UTXO. These assets include BRC-20, BRC-420, and NFTs, among others.

---
#### Request
<ParamField query="coin" type="String" required>Coin code</ParamField>
<ParamField query="tx_hash" type="String" required>Transaction hash of the UTXO</ParamField>
<ParamField query="vout_n" type="Int" required>Output index of the UTXO</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object">
  <Expandable title="object">
    <ResponseField name="chain_coin" type="String" >Chain coin code</ResponseField>
    <ResponseField name="txid" type="String" >Transaction hash of the UTXO</ResponseField>
    <ResponseField name="vout_n" type="Int" >Output index of the UTXO</ResponseField>
    <ResponseField name="pending" type="Bool" >Whether the transaction is pending</ResponseField>
    <ResponseField name="assets" type="object">
      <Expandable title="object">
        <ResponseField name="btc" type="Int" >The amount of BTC</ResponseField>
        <ResponseField name="brc-20" type="object">
          <Expandable title="object">
            <ResponseField name="ordi" type="object">
              <Expandable title="object">
                <ResponseField name="value" type="Int" >The value of ordi</ResponseField>
                <ResponseField name="decimal" type="Int" >Decimal precision</ResponseField>
              </Expandable>
            </ResponseField>
            <ResponseField name="sats" type="object">
              <Expandable title="object">
                <ResponseField name="value" type="Int" >The value of ordi</ResponseField>
                <ResponseField name="decimal" type="Int" >Decimal precision</ResponseField>
              </Expandable>
            </ResponseField>
          </Expandable>
        </ResponseField>
        <ResponseField name="ordinals-inscription" type="object[]">
          <Expandable title="object">
            <ResponseField name="inscription_id" type="String" >Inscription ID</ResponseField>
            <ResponseField name="metadata" type="object">
              <Expandable title="object">
                <ResponseField name="Token ID" type="String" >Token ID of NFT</ResponseField>
                <ResponseField name="Name" type="String" >Name of NFT</ResponseField>
              </Expandable>
            </ResponseField>
            <ResponseField name="parent" type="String" >Parent</ResponseField>
            <ResponseField name="address" type="String" >Address</ResponseField>
            <ResponseField name="content_type" type="String" >Content type</ResponseField>
            <ResponseField name="genesis_height" type="String" >Genesis height</ResponseField>
            <ResponseField name="genesis_transaction" type="String" >Genesis transaction</ResponseField>
            <ResponseField name="location" type="String" >Location</ResponseField>
            <ResponseField name="output" type="String" >Output</ResponseField>
            <ResponseField name="offset" type="String" >Offset</ResponseField>
            <ResponseField name="ethereum_teleburn_address" type="String" >Tthereum teleburn address</ResponseField>
          </Expandable>
        </ResponseField>
      </Expandable>
    </ResponseField>
  </Expandable>
</ResponseField>


<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/get_utxo_assets/",
    {
        "coin": "BTC",
        "tx_hash": "7e1d2c7f4293f4119180a89dcd3d3789927551d8e1408acf50cf769cc47e4e60",
        "vout_n": 0
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/get_utxo_assets/', {
    "coin": "BTC",
    "tx_hash": "7e1d2c7f4293f4119180a89dcd3d3789927551d8e1408acf50cf769cc47e4e60",
    "vout_n": 0
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/get_utxo_assets/", map[string]string{
    "coin": "BTC",
    "tx_hash": "7e1d2c7f4293f4119180a89dcd3d3789927551d8e1408acf50cf769cc47e4e60",
    "vout_n": 0
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "chain_coin": "BTC",
    "txid": "7e1d2c7f4293f4119180a89dcd3d3789927551d8e1408acf50cf769cc47e4e60",
    "vout_n": 0,
    "pending": false,
    "assets": {
      "btc": 12000000000,
      "brc_20": {
        "ordi": {
          "value": 8374897239498,
          "decimal": 18,
          "cobo_coin_code": null
        },
        "sats": {
          "value": 9830000000000000000000000000,
          "decimal": 18,
          "cobo_coin_code": null
        }
      },
      "ordinals_inscription": [{
        "inscription_id": "5e871f9ef8d17a990f20ce9e7928d09f5f1e838cef08a719936c9c5d3ae3af93i0",
        "metadata": {
          "Token ID": "227",
          "Name": "AILU 227"
        },
        "parent": "a3fb1b9aa4fd072cdc3616224c46276b064ac84d554dd750c92e53bd920a6969i0",
        "address": "**************************************************************",
        "content_type": "text/plain;charset=utf-8",
        "genesis_height": 834518,
        "genesis_transaction": "5e871f9ef8d17a990f20ce9e7928d09f5f1e838cef08a719936c9c5d3ae3af93",
        "location": "7e1d2c7f4293f4119180a89dcd3d3789927551d8e1408acf50cf769cc47e4e60:0:0",
        "output": "7e1d2c7f4293f4119180a89dcd3d3789927551d8e1408acf50cf769cc47e4e60:0",
        "offset": 0,
        "ethereum_teleburn_address": "******************************************"
      }]
    }
  }
}
```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>