---
title: Babylon Batch Broadcast Staking Transaction
api: POST /v1/custody/mpc/babylon/batch_broadcast_staking_transaction/
contentType: multipart/form-data
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint is used to batch broadcast the specified pre-signed staking transactions based on the provided parameter `request_ids`
---

#### Request
<ParamField body="request_ids" type="String" required>A comma-separated list of transaction request IDs. Please note that the length of the list should be less than or equal to 100.</ParamField>


#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
<ResponseField name="result" type="object"></ResponseField>

<RequestExample>
```python Python
request(
    "POST",
    "/v1/custody/mpc/babylon/batch_broadcast_staking_transaction/",
    {
        "request_ids": "1717059604245,1718613205287"
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('POST', '/v1/custody/mpc/babylon/batch_broadcast_staking_transaction/', {
    "request_ids": "1717059604245,1718613205287"
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("POST", "/v1/custody/mpc/babylon/batch_broadcast_staking_transaction/", map[string]string{
    "request_ids": "1717059604245,1718613205287"
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {}
}

```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>