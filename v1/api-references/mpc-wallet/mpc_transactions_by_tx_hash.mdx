---
title: Get Transactions By Tx Hash
api: GET /v1/custody/mpc/transactions_by_tx_hash/ 
description: <Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note> This endpoint returns transaction details for a given transaction hash. The response is a JSON object containing transaction details such as transaction hash, block number, sender address, recipient address, value, and gas used. The endpoint also accepts an optional parameter 'transaction_type' to filter the transaction details based on the type of transaction.
---
#### Request 

<ParamField query="tx_hash" type="String" required>Transaction hash</ParamField>
<ParamField query="transaction_type" type="int" >
**only use in non-web3 wallet:**
| Transaction Type | Code |
| ----------- | ----------- |
| TRANSACTION_FROM_WEB| 100|
| TRANSACTION_FROM_API| 102|
| TRANSACTION_RBF_API_SPEEDUP| 103|
| TRANSACTION_RBF_WEB_SPEEDUP| 104|
| TRANSACTION_RBF_API_DROP| 105|
| TRANSACTION_RBF_WEB_DROP| 106|
| TRANSACTION_FROM_EXTERNAL| 107|
| TRANSACTION_FROM_DEPOSIT| 1000|

**only use in web3 wallet:**
| Transaction Type | Code |
| ----------- | ----------- |
| TYPE_MPC_WEB3_WEB| 300|
| TYPE_MPC_WEB3_MMI_TX| 301|
| TYPE_MPC_WEB3_API_TRANSACTION| 303|
| TYPE_MPC_WEB3_TRANSACTION_FROM_EXTERNAL| 307|
| TYPE_MPC_WEB3_RBF_API_SPEEDUP| 308|
| TYPE_MPC_WEB3_RBF_WEB_SPEEDUP| 309|
| TYPE_MPC_WEB3_RBF_API_DROP| 310|
| TYPE_MPC_WEB3_RBF_WEB_DROP| 311|
| TRANSACTION_FROM_DEPOSIT| 1000|
</ParamField>

#### Response
<ResponseField name="success" type="bool">request successful or failed</ResponseField>
   <ResponseField name="result" type="object">
      <Expandable title="object">
           <Snippet file="mpc_transaction_snippet.mdx"/>
      </Expandable>
</ResponseField>

<RequestExample>
```python Python
request(
    "GET",
    "/v1/custody/mpc/transactions_by_tx_hash/",
    {
        "tx_hash": "0x224cd23f20a8dfbbe5418871f6a32ddee4463b31e77b5090e7efff1bd8bb6b74",
        "transaction_type": 303
    },
    api_key, api_secret, host
)
```
```javascript JavaScript
coboFetch('GET', '/v1/custody/mpc/transactions_by_tx_hash/', {
    "tx_hash": "0x224cd23f20a8dfbbe5418871f6a32ddee4463b31e77b5090e7efff1bd8bb6b74",
    "transaction_type": 303
}, api_key, api_secret, host)
    .then(res => {
        res.json().then((data)=>{
            console.log(JSON.stringify(data, null, 4));
        })
    }).catch(err => {
        console.log(err)
    });
```
```go Go
Request("GET", "/v1/custody/mpc/transactions_by_tx_hash/", map[string]string{
    "tx_hash": "0x224cd23f20a8dfbbe5418871f6a32ddee4463b31e77b5090e7efff1bd8bb6b74",
    "transaction_type": 303
})
```
</RequestExample>

<ResponseExample>
```json
{
  "success": true,
  "result": {
    "total": 28,
    "transactions": [
      {
        "cobo_id": "20221222170157000336673000008161",
        "request_id": "web_send_by_user_915_1671699717647",
        "status": 101,
        "coin_detail": {
          "coin": "GETH",
          "display_code": "GETH",
          "description": "Ethereum Goerli Testnet",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 32
        },
        "amount_detail": {
          "amount": "9000000000000000",
          "abs_amount": "0.009"
        },
        "fee_detail": {
          "fee_coin_detail": {
            "coin": "GETH",
            "display_code": "GETH",
            "description": "Ethereum Goerli Testnet",
            "decimal": 18,
            "can_deposit": True,
            "can_withdraw": True,
            "confirming_threshold": 32
          },
          "gas_price": 300000000,
          "gas_limit": 21000,
          "fee_used": 0
        },
        "source_addresses": "",
        "from_address": "******************************************",
        "to_address": "******************************************",
        "tx_hash": "",
        "vout_n": 0,
        "nonce": null,
        "confirmed_number": 0,
        "replace_cobo_id": "",
        "transaction_type": 303,
        "operation": 100,
        "block_detail": {
          "block_hash": "",
          "block_height": 0,
          "block_time": 0
        },
        "tx_detail": {
          "tx_hash": ""
        },
        "extra_parameters": "",
        "created_time": 1671699717956,
        "updated_time": 1671699717956,
        "failed_reason": null,
        "max_priority_fee": null,
        "max_fee": null,
        "approval_process": {"spender_result": 1, "spender_review_threshold": 1, "spender_status": [{"spender_person": "Cobo_Test_spender", "status": "approve"}], "spender_complete_time": 1681906749496, "approver_result": 1, "approver_review_threshold": 1, "approver_status": [{"approve_person": "Cobo_Test_approver", "status": "approve"}], "approver_complete_time": 1681906758619},
        "remark": "",
        "memo": "",
        "gas_station_child_id": ""
      }
    ]
  }
}



```
</ResponseExample>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>