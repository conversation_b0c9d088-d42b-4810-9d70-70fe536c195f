---
title: "Authentication"
description: "WaaS API Authentication"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

## Introduction

This article explains how Cobo's WaaS Service authenticates API clients.
If you are using one of the five WaaS SDKs provided by <PERSON><PERSON>, you can skip this
article because the SDKs already encapsulate the authentication mechanism for
you. If you don't use the SDKs, you would have to implement the authentication
by yourself, this article explains how.


Cobo API Key authentication requires each request to be signed except public API interfaces.

The data needs to be signed as the following:

```
HTTP_METHOD + |  +  HTTP_REQUEST_PATH + | + TIMESTAMP + | + PARAMS
```

The API signature should sign data with ECDSA signature after connection and sign the bytes with hex encoding.

<aside class="notice">
    Each component is defined as follows
</aside>

## HTTP HOST

- Development: https://api.dev.cobo.com
- Production: https://api.cobo.com

## HTTP_METHOD

Capitalized GET or POST. **Please note: <PERSON><PERSON> doesn’t accept JSON payloads in HTTP POST. Please use form-data.**

## HTTP_REQUEST_PATH

The PATH part of the URL request. For example: /v1/test/
in [https://api.dev.cobo.com/v1/test/](https://api.dev.cobo.com/v1/test/).

## NONCE

The UNIX EPOCH timestamp when calling the API is in milliseconds.

## PARAMS

If the parameters are:

```
{
  "username": "username",
  "password": "password"
}
```

After sorting the key with alphabet:
`password=password`
`username=username`

Because "p" is sorted before "u" in the alphabet, "password" should be placed before "username" and then connected as
follows:
`password=password&username=username`

API parameters are key-value string pairs. Parameters need to be normalized before signing. The rule is as follows:

1. Sort parameters by keys alphabetically.
2. Transform each parameter to a string in the format of “key=value”.
3. Connect the strings with &.

## Example

For the following requests:

| Method | URL                                           | Nonce         |
| ------ | --------------------------------------------- | ------------- |
| POST   | https://api.dev.cobo.com/v1/custody/test/ | 1537498830736 |

Paremeters

| Parameter | Value   |
| --------- | ------- |
| type      | limit   |
| side      | buy     |
| amount    | 100.0   |
| price     | 100.0   |
| symbol    | btcusdt |

Data to be prepared before signing are as follows:

```
POST|/v1/custody/test/|1537498830736|amount=100.0&price=100.0&side=buy&symbol=btcusdt&type=limit
```

Apply your locally generated API Secret to sign the data with ECDSA signature, and hex encode binary results to create
the final signature for API server verification. (See Cobo
examples: [https://github.com/CoboGlobal/](https://github.com/CoboGlobal/) )

HEADER FIELDS

- **BIZ-API-KEY**
  This field contains the API key.
- **BIZ-API-SIGNATURE**
  This field contains the signature.
- **BIZ-API-NONCE**
  This field contains the nonce.

Fill the Header with API Key, Nonce and signature on the right field to pass signature verification.

If you want to check Cobo pubkey to verify Cobo signature, please go to: Web management platform - Wallet - API
Callback. (NOTICE they're different in Development&Production environment)


The following content is to use the SDK to authorize.

## Create API key

To ensure secure access to your crypto assets under Cobo Custody via APIs,
Cobo mandates the use of ECDSA signatures for authentication in all API calls.
Access to the API is denied without proper authentication.

Generate and manage API keys effortlessly using the Cobo-provided SDK ([here](https://github.com/CoboGlobal)).
Here's a sample Python SDK:

```python Python
from cobo_custody.signer.local_signer import generate_new_key
api_secret, api_key = generate_new_key()
print(api_secret)
print(api_key)
```

<Warning>
api_secret is your private key and should be stored securely.

api_key is your public key and needs to be set in your custody account.
</Warning>

## Test API Key

Once you've added your API key to your Custody account (see [How to set API key on Custody in the quickstart section](/v1/get-started/overview/full-custody-quick-start#api-integration)),
we provide two methods to test whether your API key has been configured successfully.

**Test API key by SDKs:**

```python Python
signer = LocalSigner("YOUR_API_SECRET")
client = Client(signer=signer, env=DEV_ENV, debug=True)
res = client.get_account_info()
```

<Accordion title="View Response">
```json
{
  "success": true,
  "result": {
    "name": "cobo_test",
    "assets": [
      {
        "coin": "ADA",
        "display_code": "ADA",
        "description": "Cardano",
        "decimal": 6,
        "can_deposit": true,
        "can_withdraw": true,
        "require_memo": false,
        "balance": "********",
        "abs_balance": "29.880892",
        "fee_coin": "ADA",
        "abs_estimate_fee": "1",
        "confirming_threshold": 9,
        "dust_threshold": 1000000,
        "token_address": ""
      },
      {
        "coin": "ALGO",
        "display_code": "ALGO",
        "description": "Algorand",
        "decimal": 6,
        "can_deposit": true,
        "can_withdraw": true,
        "require_memo": false,
        "balance": "8359337",
        "abs_balance": "8.359337",
        "fee_coin": "ALGO",
        "abs_estimate_fee": "0.8",
        "confirming_threshold": 12,
        "dust_threshold": 1,
        "token_address": ""
      }
    ]
  }
}
```
</Accordion>

<br/>

**Test API key by Playground:**

We also provide an exciting API interaction tool called Playground,
which makes testing APIs a breeze. To learn how to use Playground, refer to the [Interactive API Playground](/v1/api-references/overview/playground) section.
<br/>

## SDKS

<CardGroup cols={3}>
  <Card title="Java" icon="java" color="#ea5a0c" href="/v1/sdks-and-tools/sdks/waas/java"></Card>
  <Card title="PHP" icon="php" color="#0285c7" href="/v1/sdks-and-tools/sdks/waas/php"></Card>
  <Card title="Python" icon="python" color="#16a34a" href="/v1/sdks-and-tools/sdks/waas/python"></Card>
  <Card title="JavaScript" icon="js" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/javascript"></Card>
  <Card title="Go" icon="golang" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/go"></Card>
</CardGroup>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>