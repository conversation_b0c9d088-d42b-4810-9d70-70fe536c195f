---
title: "Changelog"
description: "Keep track of every change to the WaaS API"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
#### October 10, 2024
   - Add new enumeration value `902` (reorg) for MPC transaction status.

#### September 13, 2024
   - Add new enumeration value `502` (reverting) for MPC transaction status.

#### August 27, 2024
  - Add New API: ***[Babylon Unbonding](/v1/api-references/mpc-wallet/babylon_unbonding)*** and ***[Babylon Withdraw](/v1/api-references/mpc-wallet/babylon_withdraw)*** to MPC Wallet.

#### August 14, 2024
  - Add New API: ***[Babylon Drop Staking](/v1/api-references/mpc-wallet/babylon_drop_staking)*** to MPC Wallet.

#### July 12, 2024
  - Add New API: ***[Babylon List Transactions By Status](/v1/api-references/mpc-wallet/babylon_list_transactions_by_status)*** to MPC Wallet.

#### June 18, 2024
  - Add New API: ***[Babylon Batch Broadcast Staking Transaction](/v1/api-references/mpc-wallet/babylon_batch_broadcast_staking_transaction)*** to MPC Wallet.

#### June 14, 2024
  - Change the data type of the `id` field in the response of ***[Get Addresses List](/v1/api-references/mpc-wallet/mpc_list_addresses)*** from `Int` to `String`.

#### June 3, 2024
   - Add APIs for the Babylon Staking of Co-managed Wallet.

#### April 2, 2024
   - Add APIs for the Gas Station of Full Custody Wallet.

#### February 28, 2024
   - Add new optional parameter ***"extra_parameters"*** for ***[Speedup Transaction](/v1/api-references/mpc-wallet/mpc_speedup_transaction)*** and ***[Drop Transaction](/v1/api-references/mpc-wallet/mpc_drop_transaction)***.

#### January 4, 2024
   - Add New API: ***[Get Coin Details](/v1/api-references/mpc-wallet/mpc_coin_info)*** to MPC Wallet.

#### December 28, 2023
   - Add APIs for the Gas Station of Co-managed Wallet.

#### December 21, 2023
   - Add new optional parameter ***"amount"*** for ***[Get Coin Details](/v1/api-references/custody-wallet/coin_info)***

#### December 7, 2023
   - Add New API: ***[Get Max Sendable Amount](/v1/api-references/mpc-wallet/mpc_get_max_send_amount)*** to MPC Wallet.

#### November 8, 2023
   - Add New API: ***[Get Transactions By time and offset](/v1/api-references/custody-wallet/transactions_by_time_ex)*** to Custodial Wallet.

#### October 26, 2023
   - Add New API: MPC Wallet add ***[Update Address Description](/v1/api-references/mpc-wallet/mpc_update_address_description)*** API.
   - Add details about ***instructions*** for Solana contract to the ***extra_parameters*** parameter
     in the MPC API's ***[Create Transaction](/v1/api-references/mpc-wallet/mpc_create_transaction)***.

#### October 20, 2023
   - Add New Params: Custodial Wallet ***[New Withdraw Request](/v1/api-references/custody-wallet/new_withdraw_request)*** and MPC Wallet  ***[Create Transaction](/v1/api-references/mpc-wallet/mpc_create_transaction)*** API add remark param.

#### October 13, 2023
   - Add new enumeration value `403` for MPC transaction status and modify the meaning of status `501`.

#### October 12, 2023
   - Modify example of response of the MPC API's ***[Get Transactions List](/v1/api-references/mpc-wallet/mpc_list_transactions)*** 

#### October 4, 2023
   - Add more details about ***calldata*** to the ***extra_parameters*** parameter
     in the MPC API's ***[Create Transaction](/v1/api-references/mpc-wallet/mpc_create_transaction)***.

#### September 7, 2023
   - Add more details to the ***extra_parameters*** parameter in the MPC API's
     ***[Create Transaction](/v1/api-references/mpc-wallet/mpc_create_transaction)***.

#### August 10, 2023
   - Merge MPC and MPC Web3 APIs.

#### July 28, 2023
   - Add New APIs: ***[Get Signed Messages By Cobo ID](/v1/api-references/mpc-wallet/mpc_sign_messages_by_cobo_ids)*** and
     ***[Get Signed Messages By Request ID](/v1/api-references/mpc-wallet/mpc_sign_messages_by_request_ids)*** to MPC Web3 wallet.

#### July 25, 2023
   - Add EIP-712 example to the ***extra_parameters*** parameter in the MPC API's ***[Sign Message](/v1/api-references/mpc-wallet/mpc_sign_message)***.

#### July 24, 2023
   - Add New APIs: ***[Get Tss Node List](/v1/api-references/mpc-wallet/mpc_list_tss_node)*** to MPC wallet.

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>