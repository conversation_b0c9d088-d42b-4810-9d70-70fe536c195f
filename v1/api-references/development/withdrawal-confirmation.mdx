---
title: "Withdrawal Confirmation"
description: ""
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
Upon receiving a withdraw request, <PERSON><PERSON> will initiate the verification process by sending a push message to a client-specified URL via HTTP(S) using the POST request method. The push message contains withdraw data in JSON format within the HTTP message body.

Upon completing the verification of the withdraw request and associated data, the client is required to respond promptly with an HTTP status of 200. The response should include an HTTP body containing either 'ok' to indicate approval or 'deny' to signify rejection. Failure to provide this response will result in continued pushes of the same message to the designated URL. Kindly note that your timely response is crucial for the efficient processing of a withdraw request.

When a withdrawal request for 1 BTC_USDT is submitted to a Custodial Wallet, <PERSON><PERSON> will initiate an HTTP POST request to the client-designated URL. The request body will be structured as follows:

```json json
{
    "id": "",
    "coin": "BTC_USDT",
    "display_code": "USDT",
    "description": "Tether",
    "decimal": 8,
    "address": "**********************************",
    "memo": "",
    "source_address": "**********************************",
    "source_address_detail": "**********************************",
    "side": "withdraw",
    "amount": "100000000",
    "abs_amount": "1",
    "txid": "73ea2bcb20b36115bc50588a8bcdbfbaaeb3d75656c2b104ef4a1234e5dbe550",
    "vout_n": 0,
    "request_id": "test_request_id",
    "status": "pending",
    "created_time": 1542165707058,
    "last_time": 1542165707058,
    "confirming_threshold": 3,
    "remark": "hello test",
    "confirmed_num": 0,
    "type": "external",
	"fee_amount": 0,
   "fee_coin": "GETH",
	"fee_decimal": 18,
}
```
For more information, please refer to the response of [transactions_by_id](/v1/api-references/custody-wallet/transactions_by_id).

When a withdrawal request for 0.009 GETH is submitted to a MPC Wallet, the request body will be structured as follows:

```json json
{
        "cobo_id": "20221222170157000336673000008161",
        "request_id": "web_send_by_user_915_1671699717647",
        "status": 101,
        "coin_detail": {
          "coin": "GETH",
          "display_code": "GETH",
          "description": "Ethereum Goerli Testnet",
          "decimal": 18,
          "can_deposit": True,
          "can_withdraw": True,
          "confirming_threshold": 32
        },
        "amount_detail": {
          "amount": "9000000000000000",
          "abs_amount": "0.009"
        },
        "fee_detail": {
          "fee_coin_detail": {
            "coin": "GETH",
            "display_code": "GETH",
            "description": "Ethereum Goerli Testnet",
            "decimal": 18,
            "can_deposit": True,
            "can_withdraw": True,
            "confirming_threshold": 32
          },
          "gas_price": 300000000,
          "gas_limit": 21000,
          "fee_used": 0
        },
        "source_addresses": "",
        "from_address": "******************************************",
        "to_address": "******************************************",
        "tx_hash": "",
        "vout_n": 0,
        "nonce": null,
        "confirmed_number": 0,
        "replace_cobo_id": "",
        "transaction_type": 303,
        "operation": 100,
        "block_detail": {
          "block_hash": "",
          "block_height": 0,
          "block_time": 0
        },
        "tx_detail": {
          "tx_hash": ""
        },
        "extra_parameters": "",
}
```

For more information, please refer to the response of [transactions_by_cobo_ids](/v1/api-references/mpc-wallet/mpc_transactions_by_cobo_ids).

If you approve this withdrawal request, please respond with an HTTP status of 200 and set the message body to "ok".

### Things to note:

1. To ensure system resilience against a single point of failure (SPOF), we strongly recommend using a dedicated server, such as a risk monitor, for processing withdraw confirmations.

2. Please validate the legitimacy of each withdraw request by carefully reviewing key parameters, including request_id, to_address, and amount.

3. For security reasons, we strongly recommend using HTTPS and Cobo's public key for signature verification. Detailed instructions can be found in our [guide](/v1/api-references/development/callback-signature). Please ensure that you are operating in the correct environment during the verification process.

For information on how to configure API callbacks, refer to this [guide](/v1/guides/howtos/configure-api-callback).

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>