---
title: "Error Codes"
description: ""
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

Cobo servers will return the following error data when encountering an error:

```json
{
  "success": false,
  "error_code": 1000,
  "error_description": "Unknown internal error"
}
```
## <font color="blue"> HTTP Status Code </font>
| **Status Code** | **Description** |
|:----|:----|
| 200 | OK
| 400 | Bad request
| 401 | Unauthorized – API key, signature, or timestamp is incorrect
| 403 | Forbidden – No access allowed
| 404 | Not Found – Requested resources not found
| 405 | Method Not Allowed – HTTP methods used not applicable to the requested resources
| 406 | Not Acceptable – Requested content format is not JSON
| 429 | Too Many Requests – Requests are limited, please reduce the request frequency
| 500 | Internal Server Error – Internal server error, please try again later
| 502 | Bad Gateway
| 503 | Service Unavailable – Service unavailable, please try again later

## <font color="blue"> Error codes starting with 1 </font>
| **Error Code** | **Description** |
|:----|:----|
| 1000 | Unknown internal error – Please contact Cobo
| 1001 | Signature missing, format error, or mismatch
| 1002 | Unsupported 'method' parameter
| 1003 | API params is missing or null
| 1004 | Unexpected parameters were provided
| 1005 | The nonce value is invalid
| 1006 | The parameter format or value is invalid
| 1007 | The current user lacks the necessary permissions
| 1010 | The request is too frequent
| 1011 | Invalid page_index value
| 1012 | Error request in the production environment
| 1013 | The hash passed by the API is inconsistent with the one calculated locally
| 1030 | Blacklisted user
| 1031 | Unsupported region
| 1040 | Errors related to access tokens
| 1050 | Deprecated API
| 1060 | No enough balance
| 1070 | cobo card kyc status


## <font color="blue"> Error codes starting with 12 </font>
| **Error Code** | **Description** |
|:----|:----|
| 12000 | Signature headers missing – API signature header is missing
| 12001 | Signature verification failed – API signature verification fail
| 12002 | Coin not supported
| 12003 | Permission denied
| 12004 | Transaction does not exist
| 12005 | Signature permission denied -- API key does not have access
| 12006 | IP not in whitelist
| 12007 | Insufficient balance
| 12008 | Coin is suspended temporarily
| 12009 | Duplicate withdraw request id
| 12010 | Account has been frozen
| 12011 | Amount below coin dust
| 12012 | Invalid address
| 12013 | Address not in whitelist
| 12014 | Transaction fee invalid
| 12015 | Address does not exist
| 12016 | Insufficient Credit
| 12021 | duplicated signature
| 12022 | blockchain node API call error
| 12200 | staking product not exist
| 12201 | invalid staking amount
| 12202 | invalid staking status
| 12203 | Unsupported transfer from staking wallet



<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>