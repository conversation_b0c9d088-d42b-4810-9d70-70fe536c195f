---
title: "Address Encodings"
description: ""
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

| **Enum** | **Encodings** |
|:----|:----|
| 0 | ENCODING_P2PKH |
| 1 | ENCODING_P2SH_P2WPKH |
| 2 | ENCODING_BECH32 |
| 3 | ENCODING_P2PKH_UNCOMPRESSED |
| 4 | ENCODING_P2SH_P2MS |
| 5 | ENCODING_P2SH_P2WSH_P2MS |
| 6 | ENCODING_P2TR |
| 9 | ENCODING_ADA_BYRON |
| 10 | ENCODING_ADA_SHELLEY |

## BTC
- Legacy: ENCODING_P2PKH
- Nested SegWit (P2SH): ENCODING_P2SH_P2WPKH
- Native SegWit (Bech32): ENCODING_BECH32
- Taproot: ENCODING_P2TR

## BTC_USDT
- ENCODING_P2PKH
- ENCODING_P2SH_P2WPKH (default)

## BTC_BRC20_ORDI
- ENCODING_P2TR

## SIGNET_BTC
- ENCODING_P2SH_P2WPKH
- ENCODING_BECH32 (default)
- ENCODING_P2TR

## ADA
- ENCODING_P2PKH
- ENCODING_ADA_BYRON
- ENCODING_ADA_SHELLEY

## CFZ
- old hex address: ENCODING_P2PKH
- new base32 address: ENCODING_P2SH_P2WPKH (default)

## DASH
- ENCODING_P2PKH (default)
- ENCODING_P2PKH_UNCOMPRESSED

Cryptocurrencies that do not support specifying the main chain address format, the default address format is ENCODING_P2PKH (0).



<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>