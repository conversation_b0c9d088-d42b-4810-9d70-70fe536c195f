---
title: "Cobo Callback Signature"
description: ""
"og:title": "Verifying API Callback Signatures"
"og:description": "Explore the robust security measures of Cobo, where the Elliptic Curve Digital Signature Algorithm (ECDSA) is employed to secure every HTTP response and API callback."
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>


To ensure that the response messages originate from Cobo, it is essential to verify signatures for confirmation.
<PERSON><PERSON> utilizes the Elliptic Curve Digital Signature Algorithm (ECDSA) to sign each HTTP response and API callback response.
To authenticate <PERSON><PERSON>'s signature, you will first need to retrieve a public key.
To do so, kindly navigate to the "API Callback" section on the Cobo Custody Web.
**Note that the public key differs between the development and production environments.**

<img src="/v1/images/callback.png" />

The API signature is generated using ECDSA and represented in hex through hexadecimal encoding.
The data will be signed according to the following format:

```java
HTTP_METHOD + |  +  HTTP_REQUEST_PATH + | + TIMESTAMP + | + PARAMS
```

HTTP_METHOD
GET or POST must be capitalized. Note that <PERSON><PERSON> will utilize form-data instead of JSON payloads in HTTP POST requests.
The snippet provided includes code samples focused on signature verification using Cobo SDKs.
To view the complete code samples, kindly refer to the ["How to Configure API Callback"](/v1/guides/howtos/configure-api-callback) guide.

```java
verifyResult = LocalSigner.verifyEcdsaSignature(content, signature, coboPubKey);
```

To successfully verify a signature, you will need to complete the following steps:

1. Retrieve Biz-Timestamp (timestamp) and Biz-Resp-Signature (signature) from the callback's HTTP header.
2. Concatenate the original data from the HTTP body with the timestamp from the header to create the content to be signed.
3. Retrieve Cobo’s public key from the Cobo Custody Web or through the SDKs.
4. Utilize the secp256k1 elliptic curve algorithm or the verifyEcdsaSignature function from Cobo SDKs to verify whether the public key, the content to be signed, and the signature match.
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>