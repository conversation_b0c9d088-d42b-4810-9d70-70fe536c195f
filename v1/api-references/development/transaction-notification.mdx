---
title: "Transaction Notification"
description: ""
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
Upon receiving a deposit or withdraw transaction, <PERSON><PERSON> will send a push message to a client-specified URL via HTTP(S) using the POST request method. The push message contains transaction data in JSON format within the HTTP message body.

Status = “Success”

When <PERSON><PERSON> receives a deposit of 0.000001 TRX to a recipient address associated with a Cobo client, it will send an HTTP POST request to the client's designated URL. The request body will be structured as follows:

#### Custodial Wallet
```json
{
  "id": "20201207150724000192033000003701",
  "coin": "TRON",
  "display_code": "TRX",
  "description": "TRON",
  "decimal": 6,
  "address": "TZAt997umXasPUfxUZYKQKcGwjmYpJzX6V",
  "source_address": "TWDchZBmYvTQBeXD4w8rRUowDv5ka8kiFU",
  "side": "deposit",
  "amount": "1",
  "abs_amount": "0.000001",
  "txid": "6c2df6f6cf7fe8fe1e8559c11679a5a5b90768d1c128ffb3dd66d6f3cb910775",
  "vout_n": 0,
  "request_id": null,
  "status": "success",
  "abs_cobo_fee": "0",
  "created_time": 1607324928585,
  "last_time": 1607324928585,
  "request_created_time": 1607324928492,
  "confirmed_num": 27,
  "tx_detail": {
    "txid": "6c2df6f6cf7fe8fe1e8559c11679a5a5b90768d1c128ffb3dd66d6f3cb910775",
    "blocknum": 25660684,
    "blockhash": "0000000001878d0c83fb9640712d3354a0a1912dfd5884959d3c1a16b6f62e1c",
    "hexstr": ""
  },
  "source_address_detail": "TWDchZBmYvTQBeXD4w8rRUowDv5ka8kiFU",
  "confirming_threshold": 27,
  "type": "external"
}
```

#### MPC Wallet
```json
{
    "cobo_id": "20240117111135000139405000001665",
    "request_id": "",
    "status": 900,
    "coin_detail":
    {
        "coin": "XTN",
        "chain_code": "XTN",
        "display_code": "XTN",
        "description": "Bitcoin Testnet3",
        "decimal": 8,
        "can_deposit": true,
        "can_withdraw": true,
        "confirming_threshold": 3
    },
    "nft_detail":
    {
        "nft_code": "",
        "token_id": null,
        "chain_code": "",
        "contract_address": ""
    },
    "amount_detail":
    {
        "amount": "100000",
        "abs_amount": "0.001",
        "nft_amount": 0
    },
    "fee_detail":
    {
        "fee_coin_detail":
        {
            "coin": "XTN",
            "chain_code": "XTN",
            "display_code": "XTN",
            "description": "Bitcoin Testnet3",
            "decimal": 8,
            "can_deposit": true,
            "can_withdraw": true,
            "confirming_threshold": 3
        },
        "gas_price": 0,
        "gas_limit": 0,
        "fee_used": 845,
        "fee": 5.0
    },
    "source_addresses": null,
    "from_address": "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE",
    "to_address": "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE",
    "memo": "",
    "tx_hash": "b8805bdde2e05c7392ed33069842b22317fd97882e62863756f8f84ec6bde75b",
    "vout_n": 0,
    "nonce": 0,
    "confirmed_number": 3,
    "replace_cobo_id": "",
    "transaction_type": 1000,
    "block_detail":
    {
        "block_hash": "000000000000002731780a7ec34736a63056eef5256cced316eba74df5c2a5dd",
        "block_height": 2573189,
        "block_time": 1705461073000
    },
    "tx_detail":
    {
        "tx_hash": "b8805bdde2e05c7392ed33069842b22317fd97882e62863756f8f84ec6bde75b"
    },
    "extra_parameters": "",
    "created_time": 1705461095138,
    "updated_time": 1705462579114,
    "failed_reason": "",
    "to_address_details": ""
}
```

For the definition of parameters, please refer to this [guide](/v1/api-references/custody-wallet/transaction#response) if you are using Cobo's Custodial Wallets and [here](/v1/api-references/mpc-wallet/mpc_transactions_by_request_ids#response) if you are using MPC Wallets.

Status = “Failed”

By default, Cobo does not send push messages for failed transactions. If you have a specific business requirement to receive push messages for transactions marked as 'failed,' please contact Cobo's customer <NAME_EMAIL>.  We will configure the corresponding settings tailored to meet your development needs.

Status = “Pending”

If you have ticked “Push Pending Transaction” under Transaction Notification on Cobo Custody Web, you will receive a push message whenever there is a change in the number of block confirmations for a specific transaction. The message will indicate the transaction status as ‘pending’ and include essential details such as the number of confirmed blocks and the threshold.

The status will change from "pending" to "success" when the 'confirmed_num' reaches the pre-defined 'confirmed_threshold' in the message. Additionally, pending transactions include an extra field called "waiting_audit," signifying whether an approval is required.

Each push message for a specific block number change will be sent only once. Kindly note that certain blockchains may not provide block-based notifications due to their internal mechanisms.

<img src="/v1/images/pendingpushEN.png" />

### Things to note:

1. Upon receiving a push notification from Cobo, it is strongly recommended to call Cobo's transaction querying endpoint with the provided ID to validate the transaction. Please refrain from relying solely on push messages for the validation of deposit or withdraw transactions.

2. Upon receiving a "success" transaction push message from Cobo, clients are required to respond with an HTTP status code of 200 and an HTTP body containing 'ok' to confirm receipt. If no response is received by Cobo after 14 push attempts, the message will be marked as failed.

3. For transactions marked as "pending," Cobo will send a push message whenever there is a change in the block confirmation number. No response is required from the client.

4. We recommend using the provided IDs as unique keys for each transaction to enhance database security and prevent any instances of duplicate accounting.

5. For security reasons, we strongly recommend using HTTPS and Cobo's public key for signature verification. Detailed instructions can be found in our [guide](/v1/api-references/development/callback-signature). Please ensure that you are operating in the correct environment during the verification process.

For information on how to configure API callbacks, refer to this [guide](/v1/guides/howtos/configure-api-callback).
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>