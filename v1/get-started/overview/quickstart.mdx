---
title: "Quickstart"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>
This guide demonstrates a quick start with Cobo WaaS and its accompanying SDKs. To get started, please select your Cobo Custody product from the options below:
<CardGroup cols={3}>
  <Card title="Full Custody" icon="link" href="/v1/get-started/overview/full-custody-quick-start">HSM-based custody</Card>
  <Card title="MPC Co-managed Custody" icon="link" href="/v1/get-started/overview/mpc-quick-start">MPC-based co-managed custody</Card>
  <Card title="MPC Lite Custody" icon="link" href="/v1/get-started/overview/mpc-lite-quick-start">MPC-based self custody</Card>
</CardGroup>
This guide serves as your onboarding tutorial for the Cobo WaaS [Development Environment](https://home.dev.cobo.com/). While the majority of experiences may be familiar, it is crucial to note that the associated accounts and API credentials in this environment differ from those employed in the [Production Environment](https://home.custody.cobo.com/).

To understand the differences among the custody products mentioned above, [click here](https://www.cobo.com/post/how-to-choose-cobo-custody-solutions). 
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>