---
title: "MPC Co-managed Custody"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

![](/v1/images/quickstart/mpc_001.png)


## Account Opening 
To start using Cobo MPC Co-Managed Custody, you will need to open an account in the development environment by heading to https://home.dev.cobo.com/#/login. Alternatively, you may [contact <PERSON><PERSON>](https://www.cobo.com/) to request an invitation email.

To improve security, you will need to download Google Authenticator (GA) on your mobile device. To complete the setup, please use your GA to scan the QR code displayed on the Cobo Custody Web.  

<div className="w-full flex items-center justify-center">
    <img src="/v1/images/quickstart/full_custody_002.png" width="400"/>
</div>

You are also required to install Cobo Guard, which is a dedicated mobile application that leverages cutting-edge Trusted Execution Environment (TEE) technologies for secure authentication, risk control verification, and MPC-TSS key share management. It provides robust protection for your account, particularly during critical actions such as withdrawals and risk policy adjustments. Currently, Cobo Guard only supports the iOS system. The app is compatible with iPhone 5s and newer models, and the operating system must be iOS 13.0 or later. Prior to installation, kindly make sure that your mobile device meets the aforementioned requirements.

To download Cobo Guard (development environment) from TestFlight, [click here](https://testflight.apple.com/join/pcSF46JK?ref=cobo-institutional-digital-asset-custody-provider). Please be advised that Cobo Guard downloaded from the Apple Store will only be compatible with the production environment.

After downloading Cobo Guard, you need to click on the scan icon on the top-right corner of your Cobo Guard to scan the QR code displayed on the Cobo Custody Web.

<div className="w-full flex items-center justify-center">
    <img src="/v1/images/quickstart/full_custody_003.png" width="400"/>
</div>


On the Cobo Custody Web, you can invite more team members to your org. To do so, head to Settings > Teams > Invite Users. Note that your invitees will also need to follow the aforementioned steps for setup and authentication.

<img src="/v1/images/quickstart/full_custody_004.png" />

## MPC Setup

If you want to set up your own TSS Node, please prepare the [testing environment](https://docs.cobo.com/cobo-mpc-waas/cobo-mpc-waas/tss-node-user-guide/server-environment-preparation) for TSS Node deployment. Alternatively, you can contact Cobo to acquire Cobo-managed testing nodes, which will enable you to bypass the MPC Setup process for your development environment.

Currently, you can deploy TSS Nodes on an off-the-shelf server, a server that supports Intel® Software Guard Extensions (Intel® SGX), or an Apple MacBook. 

Please head to Cobo's [TSS Node](https://download.tss.cobo.com/) Library to download a version of the TSS Node package that fits your server model and deployment method. For user guides on the deployment process, please [click here](https://docs.cobo.com/cobo-mpc-waas/cobo-mpc-waas/tss-node-user-guide/tss-node-deployment). The following initialization output uses the off-the-shelf server as an example.

<img src="/v1/images/quickstart/mpc_002.png" />

Following the initialization of each TSS Node, a TSS Node ID will be automatically generated. Please manually enter the TSS Node IDs on the Cobo Custody web. 

<img src="/v1/images/quickstart/mpc_003.png" />

Once all TSS Node IDs have been added on the Cobo Custody Web, the key generation ceremony will automatically start. This process requires all TSS Nodes to be online in order to generate a set of MPC key-shares in an independent manner. The MPC root extended public key will also be created. 

<img src="/v1/images/quickstart/mpc_004.png" />

## MPC Wallet Setup
Once the MPC root extended public key is successfully generated, you can proceed to create one or multiple types of MPC wallets on the Cobo Custody Web. 

<img src="/v1/images/quickstart/mpc_005.png" />

To add coins to MPC wallet, simply click "+Add Coins'' and then search for your preferred coin types. Currently, Cobo Custody Web supports four mainnets (BTC, ETH, TRON, XRP) and two testnets (GETH, XTN). 

<img src="/v1/images/quickstart/mpc_006.png" />

<Warning>
Please make sure that you have added coins to your MPC Custody wallets first before making any API calls.
</Warning>

## API Integration
If you are using APIs, please first choose an appropriate SDK from Cobo’s GitHub repository. Cobo Custody provides SDKs in five programming languages - Java, PHP, Python, JavaScript and Go. 

<CardGroup cols={3}>
  <Card title="Java" icon="java" color="#ea5a0c" href="/v1/sdks-and-tools/sdks/waas/java"></Card>
  <Card title="PHP" icon="php" color="#0285c7" href="/v1/sdks-and-tools/sdks/waas/php"></Card>
  <Card title="Python" icon="python" color="#16a34a" href="/v1/sdks-and-tools/sdks/waas/python"></Card>
  <Card title="JavaScript" icon="js" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/javascript"></Card>
  <Card title="Go" icon="golang" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/go"></Card>
</CardGroup>

Next, you will need to generate an API key and secret. The following code snippet provides an example using the Python SDK. You can locate instructions for generating API keys in the GitHub SDK README for your chosen programming language.

```python Python
from cobo_custody.signer.local_signer import generate_new_key
api_secret, api_key = generate_new_key()
print(api_secret)
print(api_key)
```
<Warning>
api_secret is your private key and should be stored securely.

api_key is your public key and needs to be set in your WaaS account via Cobo Custody web interface.
</Warning>


After successfully generating an API key and opening an account, you can head to the API dashboard on the Cobo Custody Web.

<img src="/v1/images/quickstart/mpc_007.png" />

Please manually enter your key into the "API Key" field and ensure that the status of your API key is displayed as “Active”. You can now proceed to use the relevant SDKs to make API calls. 

<img src="/v1/images/quickstart/mpc_008.png" />


## Test API by SDKs

```python Python
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

# input your API SECRET
signer = LocalSigner("YOUR_API_SECRET")
mpc_client = MPCClient(signer=signer, env=DEV_ENV, debug=True)
res = mpc_client.get_supported_chains()
```

<Accordion title="View Response">
```json
{
  "success": true,
  "result": {'chain_codes': ['DOT', 'BTC', 'CHZ2', 'XTN', 'BSC_BNB', 'VET', 'ETHW', 'SOL', 'ETH', 'GETH', 'TRON', 'MNT']}
}
```
</Accordion>

<br/>

## Test API by Playground

To learn what is Playground, please refer to the ([Interactive API Playground](/v1/api-references/overview/playground)) section.


<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>