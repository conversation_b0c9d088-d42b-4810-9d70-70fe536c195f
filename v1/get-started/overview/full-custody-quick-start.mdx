---
title: "Full Custody"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

![](/v1/images/quickstart/full_custody_001.png)

## Account Opening 
To use Cobo Full Custody, you will need to create an account with Co<PERSON> first. Please [contact <PERSON><PERSON>](mailto:<EMAIL>) to request an invitation email if you do not have an account yet.

To improve security, you will need to download Google Authenticator (GA) on your mobile device. To complete the setup, please use your GA to scan the QR code displayed on the Cobo Custody Web.  

<div className="w-full flex items-center justify-center">
    <img width="400" src="/v1/images/quickstart/full_custody_002.png"/>
</div>


You are also required to install Cobo Guard, which is a dedicated mobile application that leverages cutting-edge Trusted Execution Environment (TEE) technologies for secure authentication, risk control verification, and MPC-TSS key share management. It provides robust protection for your account, particularly during critical actions such as withdrawals and risk policy adjustments. Currently, Cobo Guard only supports the iOS system. The app is compatible with iPhone 5s and newer models, and the operating system must be iOS 13.0 or later. Prior to installation, kindly make sure that your mobile device meets the aforementioned requirements.

To download Cobo Guard (development environment) from TestFlight, [click here](https://testflight.apple.com/join/pcSF46JK?ref=cobo-institutional-digital-asset-custody-provider). Please be advised that Cobo Guard downloaded from the Apple Store will only be compatible with the production environment.

After downloading Cobo Guard, you need to click on the scan icon on the top-right corner of your Cobo Guard to scan the QR code displayed on the Cobo Custody Web. 

<div className="w-full flex items-center justify-center">
    <img src="/v1/images/quickstart/full_custody_003.png" width="400"/>
</div>

On the Cobo Custody Web, you can invite more team members to your org. To do so, head to Settings > Teams > Invite Users. Note that your invitees will also need to follow the aforementioned steps for setup and authentication.

<img src="/v1/images/quickstart/full_custody_004.png" />

## Wallet Setup
You can now proceed to create a Full Custody wallet on the Cobo Custody Web. To add coins to your wallet, simply click "+Add Coins'' and then search for your preferred coin types. Currently, Cobo Custody Web supports four mainnets (BTC, ETH, TRON, XRP) and two testnets (GETH, XTN). 

<img src="/v1/images/quickstart/full_custody_005.png" />

<Warning>
Please make sure that you have added coins to your Full Custody wallets first before making any API calls.
</Warning>

## API Integration
If you are using APIs, please first choose an appropriate SDK from Cobo’s GitHub repository. Cobo Custody provides SDKs in five programming languages - Java, PHP, Python, JavaScript and Go. 

<CardGroup cols={3}>
  <Card title="Java" icon="java" color="#ea5a0c" href="/v1/sdks-and-tools/sdks/waas/java"></Card>
  <Card title="PHP" icon="php" color="#0285c7" href="/v1/sdks-and-tools/sdks/waas/php"></Card>
  <Card title="Python" icon="python" color="#16a34a" href="/v1/sdks-and-tools/sdks/waas/python"></Card>
  <Card title="JavaScript" icon="js" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/javascript"></Card>
  <Card title="Go" icon="golang" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/go"></Card>
</CardGroup>

Next, you will need to generate an API key and secret. The following code snippet provides an example using the Python SDK. You can locate instructions for generating API keys in the GitHub SDK README for your chosen programming language.

```python Python
from cobo_custody.signer.local_signer import generate_new_key
api_secret, api_key = generate_new_key()
print(api_secret)
print(api_key)
```
<Warning>
api_secret is your private key and should be stored securely.

api_key is your public key and needs to be set in your WaaS account via Cobo Custody web interface.
</Warning>

After successfully generating an API key and opening an account, you can head to the API dashboard on the Cobo Custody Web.

Please manually enter your key into the "API Key" field and ensure that the status of your API key is displayed as “Active”. You can now proceed to use the relevant SDKs to make API calls. 

<img src="/v1/images/quickstart/full_custody_007.png" />


## Test API by SDKs

```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

# input your API SECRET
signer = LocalSigner("YOUR_API_SECRET")
client = Client(signer=signer, env=DEV_ENV, debug=True)
res = client.get_account_info()
```

<Accordion title="View Response">
```json
{
  "success": true,
  "result": {
    "name": "cobo_test",
    "assets": [
      {
        "coin": "ADA",
        "display_code": "ADA",
        "description": "Cardano",
        "decimal": 6,
        "can_deposit": true,
        "can_withdraw": true,
        "require_memo": false,
        "balance": "********",
        "abs_balance": "29.880892",
        "fee_coin": "ADA",
        "abs_estimate_fee": "1",
        "confirming_threshold": 9,
        "dust_threshold": 1000000,
        "token_address": ""
      },
      {
        "coin": "ALGO",
        "display_code": "ALGO",
        "description": "Algorand",
        "decimal": 6,
        "can_deposit": true,
        "can_withdraw": true,
        "require_memo": false,
        "balance": "8359337",
        "abs_balance": "8.359337",
        "fee_coin": "ALGO",
        "abs_estimate_fee": "0.8",
        "confirming_threshold": 12,
        "dust_threshold": 1,
        "token_address": ""
      }
    ]
  }
}
```
</Accordion>

<br/>

## Test API by Playground

To learn what is Playground, please refer to the ([Interactive API Playground](/v1/api-references/overview/playground)) section.
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>