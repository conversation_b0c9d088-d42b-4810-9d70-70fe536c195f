---
title: "MPC Lite"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

![](/v1/images/quickstart/mpc_lite_001.png)

## Account Opening
To create an account in the development environment, please head to https://home.develop.cobo.com/#/cobo-lite/register. For a seamless account creation process, please refer to this [user guide](https://www.cobo.com/post/open-cobo-mpc-lite-account).
<Warning>
Note that you only need to create an account in the development environment if you fall under the following categories. Otherwise, please use the production environment instead.
1. You require a testing environment that is entirely independent of the production environment
2. You need to use the API Playground feature on Developer Hub
</Warning>
To improve security, you will need to download Google Authenticator (GA) on your mobile device. To complete the setup, please use your GA to scan the QR code displayed on the web interface.
You are also required to install Cobo Guard, which is a dedicated mobile application that leverages cutting-edge Trusted Execution Environment (TEE) technologies for secure authentication, risk control verification, and MPC-TSS key share management. It provides robust protection for your account, particularly during critical actions such as withdrawals and risk policy adjustments. Currently, Cobo Guard only supports the iOS system. The app is compatible with iPhone 5s and newer models, and the operating system must be iOS 13.0 or later. Prior to installation, kindly make sure that your mobile device meets the aforementioned requirements.

<Tip>
To download Cobo Guard, whether for a production or development environment, please visit the [Apple Store](https://apps.apple.com/us/app/cobo-guard/id6450997458?ref=cobo-institutional-digital-asset-custody-provider).
</Tip>

For more information on how to set up Cobo Guard for your MPC Lite account, please refer to this [user guide](https://www.cobo.com/post/how-to-use-cobo-guard).
## MPC Setup

After successfully opening an account, you can navigate to Settings > MPC Settings > Generate Kay to complete your MPC setup. After you click on the “Generate Key" button, a message will be pushed to your Cobo Guard.
<img src="/v1/images/quickstart/mpc_lite_002.png"/>

You can approve the key generation request on your Cobo Guard. Then, a 2-2 key generation ceremony will be automatically triggered. One MPC key-share will be generated and stored on your Cobo Guard, while the other MPC key-share will be managed by Cobo. 

<div className="w-full flex items-center justify-center">
    <img src="/v1/images/quickstart/mpc_lite_003.jpg" width="400"/>
</div>

Following the key generation ceremony, you are required to back up your MPC key-share. This precautionary measure ensures that in the event of a lost mobile device or accidental uninstallation of the Cobo Guard app, you can use the backup to reconstruct your private key.

<div className="w-full flex items-center justify-center">
    <img src="/v1/images/quickstart/mpc_lite_004.jpg" width="400"/>
</div>

Upon completion of the above processes, the status of your MPC key-share will be updated to “Generated” on the web interface.

<img src="/v1/images/quickstart/mpc_lite_005.png"/>

## MPC Wallet Setup
Once the MPC key-shares are successfully generated, you can proceed to create one or multiple types of MPC wallets on the web interface. 

<img src="/v1/images/quickstart/mpc_lite_006.png"/>

To add coins to MPC wallet, simply click "+Add Coins'' and then search for your preferred coin types. Currently, Cobo Custody Web supports four mainnets (BTC, ETH, TRON, XRP) and two testnets (GETH, XTN). 

<img src="/v1/images/quickstart/mpc_lite_007.png"/>

<Warning>
Please make sure that you have added coins to your MPC Custody wallets first before making any API calls.
</Warning>

## API Integration
If you are using APIs, please first choose an appropriate SDK from Cobo’s GitHub repository. Cobo Custody provides SDKs in five programming languages - Java, PHP, Python, JavaScript and Go. 

<CardGroup cols={3}>
  <Card title="Java" icon="java" color="#ea5a0c" href="/v1/sdks-and-tools/sdks/waas/java"></Card>
  <Card title="PHP" icon="php" color="#0285c7" href="/v1/sdks-and-tools/sdks/waas/php"></Card>
  <Card title="Python" icon="python" color="#16a34a" href="/v1/sdks-and-tools/sdks/waas/python"></Card>
  <Card title="JavaScript" icon="js" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/javascript"></Card>
  <Card title="Go" icon="golang" color="#dc2626" href="/v1/sdks-and-tools/sdks/waas/go"></Card>
</CardGroup>

Next, you will need to generate an API key and secret. The following code snippet provides an example using the Python SDK. You can locate instructions for generating API keys in the GitHub SDK README for your chosen programming language.

```python Python
from cobo_custody.signer.local_signer import generate_new_key
api_secret, api_key = generate_new_key()
print(api_secret)
print(api_key)
```
<Warning>
api_secret is your private key and should be stored securely.

api_key is your public key and needs to be set in your WaaS account via Cobo Custody web interface.
</Warning>


After successfully generating an API key and opening an account, you can head to the API dashboard on the Cobo Custody Web.

Please manually enter your key into the "API Key" field and ensure that the status of your API key is displayed as “Active”. You can now proceed to use the relevant SDKs to make API calls. 

<img src="/v1/images/quickstart/mpc_lite_008.png"/>



## Test API by SDKs

```python Python
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

# input your API SECRET
signer = LocalSigner("YOUR_API_SECRET")
mpc_client = MPCClient(signer=signer, env=DEV_ENV, debug=True)
res = mpc_client.get_supported_chains()
```

<Accordion title="View Response">
```json
{
  "success": true,
  "result": {'chain_codes': ['DOT', 'BTC', 'CHZ2', 'XTN', 'BSC_BNB', 'VET', 'ETHW', 'SOL', 'ETH', 'GETH', 'TRON', 'MNT']}
}
```
</Accordion>

<br/>

## Test API by Playground

To learn what is Playground, please refer to the ([Interactive API Playground](/v1/api-references/overview/playground)) section.


<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>