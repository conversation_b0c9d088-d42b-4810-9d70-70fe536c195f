---
title: "Build your first wallet application with Cobo in 10 minutes"
sidebarTitle: "First Wallet Tutorial"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

This article provides an end-to-end demonstration of creating a crypto wallet application for your platform or exchange. Throughout the article, you will learn::
1. How to initialize RestClients with Cobo SDKs.
2. How to create your first deposit address.
3. How to retrieve transaction information.
4. How to make withdraw transactions.

#### Before you start:
Make sure you have finished your wallet setup based on [Quickstart](/v1/get-started/overview/quickstart). Before writing your first demo, make sure that the following actions have been completed:
1. You have pulled your preferred [SDK](/v1/sdks-and-tools/sdks/waas/python) for development.
2. You have completed [key pair generation](/v1/sdks-and-tools/sdks/waas/python#generate-key-pair) for API Authentication.
3. You have added your API Key to your [Custodial Wallet](/v1/get-started/overview/full-custody-quick-start#api-integration),
[MPC Co-managed Wallet](/v1/get-started/overview/mpc-quick-start#api-integration),
or [MPC Lite Wallet](/v1/get-started/overview/mpc-lite-quick-start#api-integration).

### Initialize
Cobo SDKs have packaged RestClients to interact with Cobo Wallet-as-a-Service APIs. To start, you need to initialize these Clients
as well as the ApiSigner for API Authentication.

**Custodial Wallet**
<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

# input your API SECRET
signer = LocalSigner("YOUR_API_SECRET")
client = Client(signer=signer, env=DEV_ENV, debug=True)
```
```java Java
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.CoboApiConfig;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.impl.LocalSigner;

CoboApiRestClient client = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newRestClient();

```
</CodeGroup>
<br />

**MPC Wallet**
<CodeGroup>
```python Python
from cobo_custody.client import MPCClient
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner

# input your API SECRET
signer = LocalSigner("YOUR_API_SECRET")
mpc_client = MPCClient(signer=signer, env=DEV_ENV, debug=True)
```
```java Java
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.CoboApiConfig;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.impl.LocalSigner;

CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newMPCRestClient();

```
</CodeGroup>
<br />

<Card title="Use the correct Client and Environment" icon="lightbulb" iconType="duotone" color="#ca8b04">
  `Client` is for custodial wallet, `MPCClient` is for MPC Co-managed and MPC Lite wallet.<br/>
  `DEV_ENV` is development environment, `PROD_ENV` is for production environment.
</Card>


### Create your first deposit address
Now you are able to create your first deposit address. Let us take Goerli ETH as an example. Please make sure that you or your admin
have added `GETH` in the wallet. To verify, you can use [Get Account Details](/v1/api-references/custody-wallet/org_info)
for Custodial Wallet or [Get Wallet Supported Coins](/v1/api-references/mpc-wallet/mpc_get_wallet_supported_coins) for MPC Wallets.

**Cusotidal Wallet**
<CodeGroup>
```python Python
response = client.get_account_info()
```
```java Java
ApiResponse<OrgInfo> orgInfo = client.getOrgInfo();
```
</CodeGroup>

**MPC Wallet**
<CodeGroup>
```python Python
response = mpc_client.get_wallet_supported_coins()
```
```java Java
ApiResponse<MPCCoins> getSupportedCoinsResponse = mpcClient.getWalletSupportedCoins();
```
</CodeGroup>

<br />

<Accordion title="View Response">


```json
{
  "success": true,
  "result": {
    "name": "Your Account",
    "assets": [
      {
        "coin": "GETH",
        "display_code": "GETH",
        "description": "Ethereum Goerli Testnet",
        "decimal": 18,
        "can_deposit": true,
        "can_withdraw": true,
        "require_memo": false,
        "minimum_deposit_threshold": "0",
        "balance": "****************",
        "abs_balance": "0.***************",
        "fee_coin": "GETH",
        "abs_estimate_fee": "0.***************",
        "abs_estimate_fee_usd": "0.00",
        "confirming_threshold": 64,
        "dust_threshold": 1,
        "token_address": ""
      },
    ]
  }
}
```
</Accordion>

If `GETH` is already available, you can create your first deposit address:
**Cusotidal Wallet**
<CodeGroup>
```python Python
response = client.new_deposit_address("GETH")
```
```java Java
ApiResponse<NewAddresses> newAddresses = client.newAddresses("GETH", 1, false);
```
</CodeGroup><br />

**MPC Wallet**
<CodeGroup>
```python Python
response = mpc_client.generate_addresses("GETH",1)
```
```java Java
ApiResponse<MPCAddressList> generateAddressResponse = mpcClient.generateAddresses("GETH", 1);
```
</CodeGroup><br />

<Accordion title="View Response">
```json
{
  "success": true,
  "result": {
    "coin": "GETH",
    "address": "******************************************"
  }
}
```
</Accordion>

### Check your first deposit transaction
You can make a deposit transaction to your newly created `GETH` address from an external address.
After that, you can query the transaction by latest transaction time.

**Cusotidal Wallet**
<CodeGroup>
```python Python
response = client.get_transactions_by_time(side="deposit", limit="1")
```
```java Java
ApiResponse<List<Transaction>> getTransactionsByTime = client.getTransactionsByTime(null, Side.Deposit, null, 0, 0, 1, null);
```
</CodeGroup><br />
**MPC Wallet**
<CodeGroup>
```python Python
response = mpc_client.list_transactions(transaction_type=1000, order_by="created_time", order="DESC", limit=1)
```
```java Java
ApiResponse<MPCTransactions> listTransactionsResponse = mpcClient.listTransactions(null, null, null, "created_time", "DESC", 1000, null, null, null, 1);
```
</CodeGroup><br />

<Accordion title="View Response">
```json
{
  "success": true,
  "result": [
    {
      "id": "20230720143713000129950000008444",
      "coin": "GETH",
      "display_code": "GETH",
      "description": "Ethereum Goerli Testnet",
      "decimal": 18,
      "address": "******************************************",
      "source_address": "******************************************",
      "side": "deposit",
      "amount": "*****************",
      "abs_amount": "0.01",
      "txid": "0x27cdd4750fc9a873ae62a7d0466ceaf40946d1e1bedd7dbe521a9a72a5851020",
      "vout_n": 0,
      "request_id": null,
      "status": "success",
      "abs_cobo_fee": "0",
      "created_time": 1689835033529,
      "last_time": 1689835033529,
      "confirmed_num": 34,
      "tx_detail": {
        "txid": "0x27cdd4750fc9a873ae62a7d0466ceaf40946d1e1bedd7dbe521a9a72a5851020",
        "blocknum": 9375296,
        "blockhash": "0xd7c3562bf373d126959adb4fa376b10eb9104c1e47807d8df1ab53d34008d045",
        "hexstr": ""
      },
      "source_address_detail": "******************************************",
      "confirming_threshold": 64,
      "type": "external"
    }
  ]
}
```
</Accordion>

You may find other different ways of transaction query in API References: [Custodial Wallet](/v1/api-references/custody-wallet/transaction). [MPC Wallet](/v1/api-references/mpc-wallet/mpc_list_transactions).

### Make your first withdraw transaction
BBefore making the withdraw, you may want to find out the transaction fees. In Custodial Wallet, `abs_estimate_fee` in [Get Coin Details](/v1/api-references/custody-wallet/coin_info)
indicates the withdraw fees in `fee_coin`. In MPC Wallets, `gas_price` in [Get Estimate Fee](/v1/api-references/mpc-wallet/mpc_estimate_fee)
indicates the on-chain real-time transaction fees.

**Cusotidal Wallet**
<CodeGroup>
```python Python
response = client.get_coin_info("GETH")
print(f"Get Estimated Fee: {response.result['abs_estimate_fee']}")
```
```java Java
ApiResponse<CoinInfo> getCoinInfo =client.getCoinInfo("GETH");
System.out.println("getAbsEstimateFee:" + getCoinInfo.getResult().getAbsEstimateFee());
```
</CodeGroup><br />

<Accordion title="View Response - Custodial Wallet">
```json
{
  "success": true,
  "result": {
    "coin": "GETH",
    "display_code": "GETH",
    "description": "Ethereum Goerli Testnet",
    "decimal": 18,
    "can_deposit": true,
    "can_withdraw": true,
    "require_memo": false,
    "minimum_deposit_threshold": "0",
    "balance": "****************",
    "abs_balance": "0.***************",
    "fee_coin": "GETH",
    "abs_estimate_fee": "0.***************",
    "abs_estimate_fee_usd": "0.00",
    "confirming_threshold": 64,
    "dust_threshold": 1,
    "token_address": ""
  }
}
```
</Accordion>

**MPC Wallet**
<CodeGroup>
```python Python
response = mpc_client.estimate_fee("GETH",1,"To_Address")
```
```java Java
ApiResponse<EstimateFeeDetails> estimateFeeResponse = mpcClient.estimateFee(coin, withdraw_amount, fromAddr, null,
                null, null, null, null, null, null);
```
</CodeGroup><br />

<Accordion title="View Response - MPC Wallets">
```json
{
  "success": true,
  "result": {
    "fee_coin": "GETH",
    "fee_decimal": 18,
    "slow": {
      "fee_per_byte": 0,
      "fee_amount": 0,
      "gas_price": 18354,
      "gas_limit": 21000
    },
    "average": {
      "fee_per_byte": 0,
      "fee_amount": 0,
      "gas_price": 18354,
      "gas_limit": 21000
    },
    "fast": {
      "fee_per_byte": 0,
      "fee_amount": 0,
      "gas_price": 36708,
      "gas_limit": 21000
    }
  }
}
```
</Accordion>

Now you can create your first withdraw transaction, please use UUID for `request_id` to prevent any confusion in future reconcilation. From address is required in MPC wallets whereas exampted in Custodial wallet. Meanwhile, pay attention to `amount` value and `decimal` format of each coin (amount = abs_amount*10^decimal).
<br /> Here is an example of withdrawing 0.01 GETH to an external account:

**Cusotidal Wallet**
<CodeGroup>
```python Python
request_id = f"request_id_{sha256(address.encode()).digest().hex()[:8]}_{str(int(time.time() * 1000))}"
response = client.withdraw("GETH","To_addres","*****************",request_id)
```
```java Java
String requestId = String.valueOf(System.currentTimeMillis());
ApiResponse<String> withdraw = client.withdraw("GETH",requestId,"toAddr","*****************", null, null, null);
```
</CodeGroup><br />

**MPC Wallet**
<CodeGroup>
```python Python
request_id = f"request_id_{sha256(address.encode()).digest().hex()[:8]}_{str(int(time.time() * 1000))}"
response = mpc_client.create_transaction("GETH",request_id,"From_address","To_Address","*****************")
```
```java Java
String requestId = String.valueOf(System.currentTimeMillis());
ApiResponse<MPCPostTransaction> createTransactionResponse = mpcClient.createTransaction("GETH", requestId, "*****************", "fromAddr", "toAddr",
                toAddressDetails, fee, gasPrice, gasLimit, operation, extraParameters, null, null, null);
```
</CodeGroup><br />

Then you may use the `request_id` to query the transaction status. Please note that the transaction will only be confirmed once the on-chain confirmation blocks reach `confirming_threshold`.
Before that, you may use the [GET /v1/custody/pending_transactions/](/v1/api-references/custody-wallet/pending_transactions)
        endpoint to query the details of a pending transaction under a Custodial Wallet. For an MPC Wallet, you may refer to the `status` field with code 501 PENDING_CONFIRMATION in [any transactional APIs](/v1/api-references/mpc-wallet/mpc_list_transactions) to retrieve the details of a pending transaction.
        Prior to using any endpoints, however, you need to first head to Cobo Custody Web and enable the ["Transaction Notification - Includes Block Confirmation Number"](/v1/api-references/development/transaction-notification) Status feature.
        Failure to enable this feature will result in the inability to fetch transaction information. Do note that some transactions cannot be retrieved due to fast on-chain confirmations (e.g., TRON).

**Cusotidal Wallet**
<CodeGroup>
```python Python
response = client.get_transactions_by_request_ids(request_ids="your request_id")
```
```java Java
ApiResponse<List<Transaction>> getTransactionsByRequestIds = client.getTransactionsByRequestIds("your requestId");
```
</CodeGroup><br />

**MPC Wallet**
<CodeGroup>
```python Python
response = mpc_client.transactions_by_request_ids("your request_id")
```
```java Java
ApiResponse<MPCTransactionInfos> transactionsByRequestIdsResponse = mpcClient.transactionsByRequestIds("your requestId", null);
```
</CodeGroup><br />

Congratulations! You have successfully created your first wallet application with Cobo Wallet-as-a-Service.

### Code Samples

The sample codes are for reference only. Please use [Cobo SDKs](https://github.com/CoboGlobal/) for your development.

<Tip> Note: The code samples for Custodial Wallet and MPC Wallet are different. </Tip>

**Custodial Wallet**
<CodeGroup>
```python Python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
import time

api_secret = "your_api_secret"  # Your wallet api secret
coin_code = "GETH"  # Your testing coin
amount = *****************  # Withdraw amount：0.01GETH
to_address = "your address"  # Your external address

# Initialize Cobo Client
client = Client(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# Check if GETH has been added in your wallet
response = client.get_coin_info(coin=coin_code)
print(f"Get Coin Info: {response.result}")

# Create GETH address
response = client.new_deposit_address(coin=coin_code)
print(f"New Deposit Address: {response.result}")

# Get deposit transaction
response = client.get_transactions_by_time(side="deposit", limit="1")
print(f"Get Transactions By Time: {response.result}")

# Get estimated withdraw fee
response = client.get_coin_info(coin=coin_code)
print(f"Get Estimated Fee: {response.result['abs_estimate_fee']}")

# Withdraw 0.01GETH
request_id = f"ApiTransaction-{int(time.time() * 1000)}"    # Your custom request_id
response = client.withdraw(
    coin=coin_code,
    request_id=request_id,
    amount=amount,
    address=to_address,
)
print(f"Withdraw: {response.result}")

# Get transaction by request_id
response = client.get_transactions_by_request_ids(request_ids=request_id)
print(f"Get Transactions By Request Ids： {response.result}")

```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigInteger;
import java.util.List;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.CoinInfo;
import com.cobo.custody.api.client.domain.account.NewAddresses;
import com.cobo.custody.api.client.domain.transaction.Side;
import com.cobo.custody.api.client.domain.transaction.Transaction;


public class CoboApiExample {
    public static void main(String[] args) {
        String apiSecret = "your_api_secret";  // Your wallet api secret
        String coin = "GETH";  // Your testing coin
        String requestId = String.valueOf(System.currentTimeMillis());  // Your custom request_id
        String toAddr = "your_address";  // Your external address
        BigInteger withdraw_amount = new BigInteger("*****************");  // Withdraw amount：0.01GETH

        // Initialize Cobo Client
        CoboApiRestClient client = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newRestClient();

        // Check if GETH has been added in your wallet
        ApiResponse<CoinInfo> response = client.getCoinInfo(coin);
        System.out.println("getCoinInfo:" + response.getResult());

        // Create GETH address
        ApiResponse<NewAddresses> newAddresses = client.newAddresses(coin, 1, false);
        System.out.println("generateAddresses:" + newAddresses.getResult());

        // Get deposit transaction
        ApiResponse<List<Transaction>> getTransactionsByTime = client.getTransactionsByTime(null, Side.Deposit, null, 0, 0, 1, null);
        System.out.println("getTransactionsByTime: " + getTransactionsByTime.getResult());

        // Get estimated withdraw fee
        ApiResponse<CoinInfo> getCoinInfo = client.getCoinInfo(coin);
        System.out.println("getAbsEstimateFee:" + getCoinInfo.getResult().getAbsEstimateFee());

        // Withdraw 0.01GETH
        ApiResponse<String> withdraw = client.withdraw(
            coin,
            requestId,
            toAddr,
            withdraw_amount,
            null,
            null,
            null
        );
        System.out.println("withdraw：" + withdraw.getResult());

        // Get transaction by request_id
        ApiResponse<List<Transaction>> getTransactionsByRequestIds = client.getTransactionsByRequestIds(requestId);
        System.out.println(getTransactionsByRequestIds.getResult());
    }
}

```

</CodeGroup><br />

**MPC Wallet**
<CodeGroup>
```python Python
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
import time


api_secret = "your api secret"  # Your wallet api secret
chain_code = "GETH"  # Your testing chain
coin_code = "GETH"  # Your testing coin
amount = "*****************"  # Withdraw amount：0.01GETH
from_address = "your mpc wallet address"  # Your MPC wallet address
to_address = "your address"  # Your external address

# Initialize Cobo Client
mpc_client = MPCClient(signer=LocalSigner(api_secret), env=DEV_ENV, debug=False)

# Check if GETH has been added in your wallet
response = mpc_client.get_wallet_supported_coins()
geth_coin_found = any(coin['coin'] == coin_code for coin in response.result['coins'])
is_coin_added = f"{coin_code} supports deposits and withdrawals." if geth_coin_found \
    else f"Your wallet does not have the {coin_code} coin. Please configure it on Cobo Custody Web."
print(is_coin_added)

# Create GETH address
response = mpc_client.generate_addresses(chain_code=chain_code, count=1)
print(f"New Deposit Address: {response.result}")

# Get deposit transaction
response = mpc_client.list_transactions(transaction_type=1000, order_by="created_time", order="DESC", limit=1)
print(f"Get Transactions By Time: {response.result}")

# Get estimated gas fee
response = mpc_client.estimate_fee(coin=coin_code, amount=1, address=to_address)
print(f"Get Estimated Gas Fee: {response.result}")

# Withdraw 0.01GETH
request_id = f"MPCTransaction-{int(time.time() * 1000)}"  # Your custom request_id
response = mpc_client.create_transaction(
    coin=coin_code,
    request_id=request_id,
    amount=amount,
    from_addr=from_address,
    to_addr=to_address,
)
print(f"Withdraw: {response.result}")

# Get transaction by request_id
response = mpc_client.transactions_by_request_ids(request_id)
print(f"Get Transactions By Request Ids： {response.result}")
```
```java Java
package com.cobo.custody.api.client.impl;

import java.math.BigDecimal;
import java.math.BigInteger;

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.MPCAddressList;

import com.cobo.custody.api.client.domain.account.MPCCoins;
import com.cobo.custody.api.client.domain.transaction.EstimateFeeDetails;
import com.cobo.custody.api.client.domain.transaction.MPCPostTransaction;

import com.cobo.custody.api.client.domain.transaction.MPCTransactionInfos;
import com.cobo.custody.api.client.domain.transaction.MPCTransactions;


public class CobоCustodyApiClientExample {
    public static void main(String[] args) {
        String apiSecret = "your_api_secret";  // Your wallet api secret
        String coin = "GETH";  // Your testing coin
        String chain_code = "GETH";  // Your testing chain
        String requestId = String.valueOf(System.currentTimeMillis());  // Your custom request_id
        String fromAddr = "your_mpc_wallet_address";  // Your MPC wallet address
        String toAddr = "your address";  // Your external address
        BigInteger withdraw_amount = new BigInteger("*****************");  // Withdraw amount：0.01GETH
        String toAddressDetails = null;
        BigDecimal fee = null;
        BigInteger gasPrice = null;
        BigInteger gasLimit = null;
        Integer operation = null;
        String extraParameters = null;

        // Initialize Cobo Client
        CoboMPCApiRestClient mpcClient = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newMPCRestClient();

        // Check if GETH has been added to your wallet
        ApiResponse<MPCCoins> getSupportedCoinsResponse = mpcClient.getSupportedCoins(coin);
        System.out.println("GetSupportedCoins: " + getSupportedCoinsResponse.getResult());

        // Create GETH address
        ApiResponse<MPCAddressList> generateAddressResponse = mpcClient.generateAddresses(chain_code, 1);
        System.out.println("Generated Address: " + generateAddressResponse.getResult());

        // Get deposit transaction
        ApiResponse<MPCTransactions> listTransactionsResponse = mpcClient.listTransactions(null, null, null, "created_time", "DESC", 1000, null, null, null, 1);
        System.out.println("Deposit Transactions: " + listTransactionsResponse.getResult());

        // Get estimated gas fee
        ApiResponse<EstimateFeeDetails> estimateFeeResponse = mpcClient.estimateFee(coin, withdraw_amount, fromAddr, null,
                null, null, null, null, null, null);
        System.out.println("Estimated Gas Fee: " + estimateFeeResponse.getResult());

        // Withdraw 0.01GETH
        ApiResponse<MPCPostTransaction> createTransactionResponse = mpcClient.createTransaction(coin, requestId, withdraw_amount, fromAddr, toAddr,
                toAddressDetails, fee, gasPrice, gasLimit, operation, extraParameters, null, null, null);
        System.out.println("Withdraw: " + createTransactionResponse.getResult());

        // Get transaction by request_id
        ApiResponse<MPCTransactionInfos> transactionsByRequestIdsResponse = mpcClient.transactionsByRequestIds(requestId, null);
        System.out.println("Transactions by Request ID: " + transactionsByRequestIdsResponse.getResult());
    }
}
```
</CodeGroup>

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>