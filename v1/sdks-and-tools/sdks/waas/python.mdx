---
title: "Python"
icon: "python"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>


***
# Overview
This guide introduces Cobo WaaS API in Python SDK, enabling developers to integrate with Cobo's Custodial/MPC services seamlessly using the Python programming language.


# Before You Begin
- Ensure that you have created an account and configured Cobo's Custodial/MPC services.
For detailed instructions, please refer to the [Quickstart](https://www.cobo.com/developers/get-started/overview/quickstart) guide.

- To access the API documentation, navigate to the [API references](https://www.cobo.com/developers/api-references/overview/) section.



# Using the Cobo Python SDK


## GitHub
[The Official Python SDK for Cobo WaaS API](https://github.com/CoboGlobal/cobo-python-api).




## Requirements
Python 3.7 or newer.


## Installation
Please run the first command for installation. Note that the source code below is only required for those looking to modify the package.
:


```sh
pip install --upgrade cobo-custody
```


Install from source with:


```sh
python setup.py install
```

## Code Sample

### Generate Key Pair
For more information on the API Key, please [click here](/v1/api-references/overview/authentication).


```python
from cobo_custody.signer.local_signer import generate_new_key,LocalSigner
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
api_secret, api_key = generate_new_key()
```

### Initialize ApiSigner

`ApiSigner` can be instantiated through

```python
from cobo_custody.signer.local_signer import LocalSigner
LocalSigner("API_SECRET")
```

In certain scenarios, the private key may be restricted from export, such as when it is stored in AWS Key Management Service (KMS).
In such cases, please pass in a custom implementation using the ApiSigner interface:

### Initialize RestClient

```python
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
from cobo_custody.signer.local_signer import LocalSigner
client = Client(signer=signer, env=DEV_ENV, debug=True)
```


### Custodial Wallet Sample
```python
from cobo_custody.signer.local_signer import generate_new_key,LocalSigner
from cobo_custody.client import Client
from cobo_custody.config import DEV_ENV
signer = LocalSigner("api_secret")
client = Client(signer=signer, env=DEV_ENV, debug=True)
client.get_account_info()
```


### MPC Wallet Sample
```python
from cobo_custody.signer.local_signer import generate_new_key,LocalSigner
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEV_ENV
signer = LocalSigner("api_secret")
mpc_client = MPCClient(signer=signer, env=DEV_ENV, debug=True)
mpc_client.get_supported_coins()
```


### Handling Response


The response is an ApiResponse object.
```python
from cobo_custody.signer.local_signer import generate_new_key,LocalSigner
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEV_ENV
signer = LocalSigner("api_secret")
client = Client(signer=signer, env=DEV_ENV, debug=True)
res = client.get_supported_coins()

''' Response
ApiResponse(success=True,
 result= {
   'name': 'DEV_AT_Auth',
   'assets': [
     {
       'coin': 'COBO_ETH',
       'display_code': 'COBO_ETH',
       'description': 'Cobo Ethereum Testnet',
       'decimal': 18,
       'can_deposit': True,
       'can_withdraw': True,
       'require_memo': False,
       'minimum_deposit_threshold': '0',
       'balance': '90681999999999969',
       'abs_balance': '0.090681999999999969',
       'fee_coin': 'COBO_ETH',
       'abs_estimate_fee': '0.000021',
       'abs_estimate_fee_usd': '0.04',
       'confirming_threshold': 2,
       'dust_threshold': 1,
       'token_address': ''
     }
   ]
 }
, exception=None)'''

# You can handle the response object as follows:
#value is True
print(res.success)
print(res.result)
#value is DEV_AT_Auth
print(res.result["name"])
print(res.exception)
```


### Handling API Errors
```python
from cobo_custody.signer.local_signer import generate_new_key,LocalSigner
from cobo_custody.client.mpc_client import MPCClient
from cobo_custody.config import DEV_ENV
signer = LocalSigner("api_secret")
client = Client(signer=signer, env=DEV_ENV, debug=True)
res = client.get_supported_coins()


'''Reponse
ApiResponse(success=False,
           result=None,
           exception=ApiError(
                errorCode=12001,
                errorMessage='Signature verify fail',
                errorId='a0ca65be81f94a9e8046f214e0480b3a'
                )
           )
'''

# You can handle the response object as follows
#value is False
print(res.success)

print(res.result)

#value is ApiError object
print(res.exception)

print(res.exception["errorCode"])
print(res.exception["errorMessage"])
print(res.exception["errorId"])
```
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>