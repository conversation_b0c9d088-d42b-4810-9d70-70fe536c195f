---
title: "PHP"
icon: "php"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

***
# Overview
This guide introduces Cobo WaaS API in PHP SDK, enabling developers to integrate with Cobo's Custodial/MPC services seamlessly using the PHP programming language.

# Before You Begin
- Ensure that you have created an account and configured Cobo's Custodial/MPC services.
For detailed instructions, please refer to the [Quickstart](https://www.cobo.com/developers/get-started/overview/quickstart) guide.

- To access the API documentation, navigate to the [API references](https://www.cobo.com/developers/api-references/overview/) section.

# Using the Cobo PHP SDK

## GitHub
[The Official PHP SDK for Cobo WaaS API](https://github.com/CoboGlobal/cobo-php-api).

## Requirements
PHP 7.0 or newer.


## Installation
The cobo_custody library can be conveniently installed using Composer.

- first you need to install [Composer](https://getcomposer.org/)

- then add dependency in composer.json

```json
{
  "require": {
    "cobo/cobo_custody": "0.2.17"
  }
}
```

## Code Sample

### Generate Key Pair

```php
<?php
require 'vendor/autoload.php';

use Cobo\Custody\Config;
use Cobo\Custody\LocalSigner;
use Cobo\Custody\Client;
$key = LocalSigner::generateKeyPair();
echo "apiSecret:", $key['apiSecret'],"\n";
echo "apiKey:", $key['apiKey'];
?>
```
For more information on the API key, please [click here](https://www.cobo.com/developers/api-references/overview/authentication).


### Initialize ApiSigner
`ApiSigner` can be instantiated through `$signer = new LocalSigner($key['apiSecret']);`

```php
$signer = new LocalSigner($key['apiSecret']);
```

In certain scenarios, the private key may be restricted from export, such as when it is stored in AWS Key Management Service (KMS).
In such cases, please pass in a custom implementation using the ApiSigner interface:


### Initialize RestClient
```php
<?php
require 'vendor/autoload.php';

use Cobo\Custody\Config;
use Cobo\Custody\LocalSigner;
use Cobo\Custody\Client;

$client = new Client($signer, Config::DEV, false);
?>
```

### Custodial Wallet Sample
```php
<?php
require 'vendor/autoload.php';

use Cobo\Custody\Config;
use Cobo\Custody\LocalSigner;
use Cobo\Custody\Client;

$key = LocalSigner::generateKeyPair();
echo "apiSecret:", $key['apiSecret'],"\n";
echo "apiKey:", $key['apiKey'];

$signer = new LocalSigner($key['apiSecret']);
$client = new Client($signer, Config::DEV, false);
$res = $client->getAccountInfo();

?>
```


### MPC Wallet Sample
```php
<?php
require 'vendor/autoload.php';
use Cobo\Custody\Config;
use Cobo\Custody\LocalSigner;
use Cobo\Custody\MPCClient;
$key = LocalSigner::generateKeyPair();
echo "apiSecret:", $key['apiSecret'],"\n";
echo "apiKey:", $key['apiKey'];

$signer = new LocalSigner($apiKey);
$mpc_client = new MPCClient($signer, Config::DEV, false);
$res = $mpc_client->getSupportedChains();
var_dump($res->success);
var_dump(json_encode($res));
?>
```


### Handling Response

```php
<?php
require 'vendor/autoload.php';
use Cobo\Custody\Config;
use Cobo\Custody\LocalSigner;
use Cobo\Custody\Client;
$key = LocalSigner::generateKeyPair();
echo "apiSecret:", $key['apiSecret'],"\n";
echo "apiKey:", $key['apiKey'];

$signer = new LocalSigner($apiKey);
$client = new Client($signer, Config::DEV, false);
$res = $client->getCoinDetails();

/* response as follows:
object(stdClass)#5 (2) {
  ["success"]=>
  bool(true)
  ["result"]=>
  object(stdClass)#1750 (16) {
    ["coin"]=>
    string(3) "BTC"
    ["display_code"]=>
    string(3) "BTC"
    ["description"]=>
    string(7) "Bitcoin"
    ["decimal"]=>
    int(8)
    ["can_deposit"]=>
    bool(true)
    ["can_withdraw"]=>
    bool(true)
    ["require_memo"]=>
    bool(false)
    ["minimum_deposit_threshold"]=>
    string(5) "10000"
    ["balance"]=>
    string(5) "10000"
    ["abs_balance"]=>
    string(6) "0.0001"
    ["fee_coin"]=>
    string(3) "BTC"
    ["abs_estimate_fee"]=>
    string(8) "0.000948"
    ["abs_estimate_fee_usd"]=>
    string(5) "35.64"
    ["confirming_threshold"]=>
    int(4)
    ["dust_threshold"]=>
    int(546)
    ["token_address"]=>
    string(0) ""
  }
}
*/

// You can handle the response object as follows:
var_dump($res->success);
var_dump($res->result->coin);
var_dump($res->result->abs_estimate_fee_usd);
var_dump(json_encode($res));
?>
```

### Handling API Errors
```php
<?php
require 'vendor/autoload.php';
use Cobo\Custody\Config;
use Cobo\Custody\LocalSigner;
use Cobo\Custody\Client;

$signer = new LocalSigner("wrong secret key");
$client = new Client($signer, Config::DEV, false);
$res = $client->getCoinDetails();

/* response
"
object(stdClass)#5 (5) {
  ["success"]=>bool(false)
  ["error_code"]=>int(1006)
  ["error_message"]=>string(51) "Invalid api key, please use standard wallet api key"
  ["error_id"]=>string(32) "110e3bd3e8064f99ae0c0fea9e91bdb9"
  ["error_description"]=>string(51) "Invalid api key, please use standard wallet api key"
}
*/

// You can handle the error response  as follows:
var_dump($res->error_code);
var_dump($res->error_message);
var_dump($res->error_id);
var_dump($res->error_description);
?>
```



<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>