---
title: "Go"
icon: "golang"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

***
# Overview
This guide introduces Cobo WaaS API in Go SDK, enabling developers to integrate with Cobo's Custodial/MPC services seamlessly using the Go programming language.

# Before You Begin
- Ensure that you have created an account and configured Cobo's Custodial/MPC services.
For detailed instructions, please refer to the [Quickstart](https://www.cobo.com/developers/get-started/overview/quickstart) guide.

- To access the API documentation, navigate to the [API references](https://www.cobo.com/developers/api-references/overview/) section.

# Using the Cobo Go SDK

## GitHub
[The Official Go SDK for Cobo WaaS API](https://github.com/CoboGlobal/cobo-go-api).



## Requirements
Go 1.18 or newer.


## Installation

add dependency

```sh
go get github.com/CoboGlobal/cobo-go-api@v0.46.0
```

## Code Sample

### Generate Key Pair

```go
import (
	"fmt"
	"github.com/CoboGlobal/cobo-go-api/cobo_custody"
)

apiSecret, apiKey := cobo_custody.GenerateKeyPair()
fmt.Println("API_SECRET:", apiSecret)
fmt.Println("API_KEY:", apiKey)
```
For more information on the API key, please [click here](https://www.cobo.com/developers/api-references/overview/authentication).

### Initialize ApiSigner

`ApiSigner` can be instantiated through

```go
import "github.com/CoboGlobal/cobo-go-api/cobo_custody"

var localSigner = cobo_custody.LocalSigner{
		PrivateKey: "apiSecret",
	}
```

In some cases, your private key cannot be exported, for example, your private key is in aws kms, you should pass in your own implementation by implements `ApiSigner` interface

### Initialize RestClient

```go
import "github.com/CoboGlobal/cobo-go-api/cobo_custody"
var client = cobo_custody.Client{
  Signer:  localSigner,
  Env:     cobo_custody.Dev(),
}
```

### Custodial Wallet Sample
```Go
import (
	"fmt"
	"github.com/CoboGlobal/cobo-go-api/cobo_custody"
)
apiSecret, apiKey := cobo_custody.GenerateKeyPair()
fmt.Println("API_SECRET:", apiSecret)
fmt.Println("API_KEY:", apiKey)

var localSigner = cobo_custody.LocalSigner{
  PrivateKey: apiSecret,
}

var client = cobo_custody.Client{
  Signer:  localSigner,
  Env:     cobo_custody.Dev(),
}

var res, error_msg = client.GetAccountInfo()
fmt.Println(res)
fmt.Println(error_msg)

```

### MPC Wallet Sample
```go
import (
	"fmt"
	"github.com/CoboGlobal/cobo-go-api/cobo_custody"
)
apiSecret, apiKey := cobo_custody.GenerateKeyPair()
fmt.Println("API_SECRET:", apiSecret)
fmt.Println("API_KEY:", apiKey)

var localSigner = cobo_custody.LocalSigner{
  PrivateKey: apiSecret,
}

var mpc_client = cobo_custody.MPCClient{
  Signer:  localSigner,
  Env:     cobo_custody.Dev(),
}

var res, error_msg = mpc_client.GetSupportedChains()
fmt.Println(res)
fmt.Println(error_msg)
```

### Handling Response
```Go
import (
	"fmt"

	"github.com/CoboGlobal/cobo-go-api/cobo_custody"
)

var localSigner = cobo_custody.LocalSigner{
    PrivateKey: "your secret key",
}

var client = cobo_custody.Client{
    Signer: localSigner,
    Env:    cobo_custody.Dev(),
}

var res, error_msg = client.GetCoinInfo("BTC")
fmt.Println(res)

/* The response is a simplejson.Json object
   {map[abs_balance:0.0001 abs_estimate_fee:0.000948
        abs_estimate_fee_usd:35.28 balance:10000
        can_deposit:true
        can_withdraw:true
        coin:BTC confirming_threshold:4 decimal:8
        description:Bitcoin display_code:BTC
        dust_threshold:546 fee_coin:BTC
        minimum_deposit_threshold:10000
        require_memo:false token_address:]}
*/

// You can handle the response object as follows:
fmt.Println(res.Get("abs_balance"))
fmt.Println(res.Get("abs_estimate_fee"))

```

### Handling API Errors

```Go
var client = cobo_custody.Client{
    Signer: localSigner,
    Env:    cobo_custody.Dev(),
}
var res, error_msg = client.GetCoinInfo("BTC")
fmt.Println(error_msg)

/* The error response is a cobo_custody.ApiError object.
" { 4b9be35a1c5d44f38ddf2b7e58fd3897 1006 Invalid api key,
  please use standard wallet api key }"
*/

// You can handle the response object as follows:
fmt.Println(error_msg.ErrorCode)
fmt.Println(error_msg.ErrorId)
fmt.Println(error_msg.ErrorMessage)
```


<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>