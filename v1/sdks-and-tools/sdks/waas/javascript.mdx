---
title: "JavaScript"
icon: "js"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

***
# Overview
This guide introduces Cobo WaaS API in JavaScript SDK, enabling developers to integrate with Cobo's Custodial/MPC services seamlessly using the JavaScript programming language.

# Before You Begin
- Ensure that you have created an account and configured Cobo's Custodial/MPC services.
For detailed instructions, please refer to the [Quickstart](https://www.cobo.com/developers/get-started/overview/quickstart) guide.

- To access the API documentation, navigate to the [API references](https://www.cobo.com/developers/api-references/overview/) section.

# Using the Cobo JavaScript SDK

## GitHub
[The Official JavaScript SDK for Cobo WaaS API](https://github.com/CoboGlobal/cobo-js-api).

## Requirements
Node.js v10.18.0 or newer.


## Installation
add dependency in package.json

```json
{
  "dependencies": {
    "cobo-custody": "https://github.com/CoboGlobal/cobo-js-api/releases/download/v0.39.0/release.tgz"
  }
}
```

## Code Sample

### Generate Key Pair

```JavaScript
const { LocalSigner } = require('cobo-custody');

const keyPair = LocalSigner.newKeyPair();
console.log(keyPair["privKey"]);
console.log(keyPair["pubKey"]);
```
For more information on the API key, please [click here](https://www.cobo.com/developers/api-references/overview/authentication).


### Initialize ApiSigner
`ApiSigner` can be instantiated through

```javascript
const { LocalSigner } = require("cobo-custody");
const signer = new LocalSigner(keyPair["privKey"]);
```
In certain scenarios, the private key may be restricted from export, such as when it is stored in AWS Key Management Service (KMS).
In such cases, please pass in a custom implementation using the ApiSigner interface:


### Initialize RestClient
```JavaScript
const { Client } = require('cobo-custody');
const { LocalSigner } = require('cobo-custody');
const {DEV,PROD} = require('cobo-custody');

const client = new Client(API_SIGNER, DEV, true);
```

### Custodial Wallet Sample
```JavaScript
const { Client } = require('cobo-custody');
const { LocalSigner } = require('cobo-custody');
const {DEV,PROD} = require('cobo-custody');

async function cutodian_wallet_sample() {
    const keyPair = LocalSigner.newKeyPair();
    console.log(keyPair["privKey"]);
    console.log(keyPair["pubKey"]);
    const signer = new LocalSigner(keyPair["privKey"]);
    const client = new Client(signer, DEV, false);
    const res =  await client.getCoinInfo("BTC");
    console.log(res);
}
cutodian_wallet_sample();
```

### MPC Wallet Sample
```JavaScript
async function mpc_wallet_sample() {
    const keyPair = LocalSigner.newKeyPair();
    console.log(keyPair["privKey"]);
    console.log(keyPair["pubKey"]);
    const signer = new LocalSigner(keyPair["privKey"]);
    const mpc_client = new MPCClient(signer, DEV, false);
    const res =  await mpc_client.GetSupportedChains();
    console.log(res);
}
mpc_wallet_sample();
```

### Handling Response

```JavaScript
const { Client } = require('cobo-custody');
const { LocalSigner } = require('cobo-custody');
const {DEV,PROD} = require('cobo-custody');

async function cutodian_wallet_sample() {
    const keyPair = LocalSigner.newKeyPair();
    console.log(keyPair["privKey"]);
    console.log(keyPair["pubKey"]);
    const signer = new LocalSigner(keyPair["privKey"]);
    const client = new Client(signer, DEV, false);
    const res =  await client.getCoinInfo("BTC");
    console.log(res);

    /*response is a json object
    "{
      success: true,
      result: {
        coin: 'BTC',
        display_code: 'BTC',
        description: 'Bitcoin',
        decimal: 8,
        can_deposit: true,
        can_withdraw: true,
        require_memo: false,
        minimum_deposit_threshold: '10000',
        balance: '10000',
        abs_balance: '0.0001',
        fee_coin: 'BTC',
        abs_estimate_fee: '0.000948',
        abs_estimate_fee_usd: '35.30',
        confirming_threshold: 4,
        dust_threshold: 546,
        token_address: ''
      }
    }*/

    // You can handle the response object as follows:
    console.log(res.success);
    console.log(res.result.coin)
    console.log(res.result.abs_estimate_fee)

}
cutodian_wallet_sample();
```

### Handling API Errors
```JavaScript
const { Client } = require('cobo-custody');
const { LocalSigner } = require('cobo-custody');
const {DEV,PROD} = require('cobo-custody');

async function cutodian_wallet_sample() {
    const keyPair = LocalSigner.newKeyPair();
    // console.log(keyPair["privKey"]);
    // console.log(keyPair["pubKey"]);
    const signer = new LocalSigner("wrong secret key");
    const client = new Client(signer, DEV, false);
    const res =  await client.getCoinInfo("BTC");
    console.log(res);

    /*response is a json object
   {
      success: false,
      error_code: 1006,
      error_message: 'Invalid api key, please use standard wallet api key',
      error_id: '16060797d8834044a4fdb0f7d01c6627',
      error_description: 'Invalid api key, please use standard wallet api key'
    }*/

    // You can handle the error response object as follows:
    console.log(res.success);
    console.log(res.error_code);
    console.log(res.error_message);
    console.log(res.error_id);
    console.log(res.error_description);
  }

cutodian_wallet_sample();

```



<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>