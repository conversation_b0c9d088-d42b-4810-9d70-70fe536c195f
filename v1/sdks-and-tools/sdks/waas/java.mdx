---
title: "Java"
icon: "java"
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

***
# Overview
This guide introduces Cobo WaaS API in Java SDK, enabling developers to integrate with Cobo's Custodial/MPC services seamlessly using the Java programming language.

# Before You Begin
- Ensure that you have created an account and configured Cobo's Custodial/MPC services.
For detailed instructions, please refer to the [Quickstart](https://www.cobo.com/developers/get-started/overview/quickstart) guide.

- To access the API documentation, navigate to the [API references](https://www.cobo.com/developers/api-references/overview/) section.

# Using the Cobo Java SDK

## GitHub
[The Official Java SDK for Cobo WaaS API](https://github.com/CoboGlobal/cobo-java-api).

## Requirements
- JDK8
- JDK17 or newer


## Installation
Step 1. Add the JitPack repository to your build file

gradle:

```
allprojects {
    repositories {
        ...
        maven { url 'https://jitpack.io' }
    }
}
```

maven:

```
<repositories>
    <repository>
        <id>jitpack.io</id>
        <url>https://jitpack.io</url>
    </repository>
</repositories>
```

Step 2. Add the dependency

gradle:

```
dependencies {
    implementation 'com.github.CoboGlobal:cobo-java-api:v0.64'
}
```

maven:

```
<dependency>
    <groupId>com.github.CoboGlobal</groupId>
    <artifactId>cobo-java-api</artifactId>
    <version>v0.62</version>
</dependency>
```

## Code Sample

### Generate Key Pair

```java
import com.cobo.custody.api.client.impl.LocalSigner;

String[] key = LocalSigner.generateKeyPair();
String secretKey = key[0];
String apiKey = key[1];

```
For more information on the API key, please [click here](https://www.cobo.com/developers/api-references/overview/authentication).


### Initialize ApiSigner
`ApiSigner` can be instantiated through `new LocalSigner("secretkey" )`

In certain scenarios, the private key may be restricted from export, such as when it is stored in AWS Key Management Service (KMS).
In such cases, please pass in a custom implementation using the ApiSigner interface:
```java

import com.cobo.custody.api.client.ApiSigner;
new ApiSigner() {
    @Override
    public String sign(byte[] message) {
        return null;
    }

    @Override
    public String getPublicKey() {
        return null;
    }
}
```

### Initialize RestClient
These can be instantiated using the corresponding factory method provided by `CoboApiClientFactory`.

```java
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.CoboApiConfig;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.impl.LocalSigner;

CoboApiRestClient client = CoboApiClientFactory.newInstance(
                new LocalSigner(apiSecret),
                Env.DEV,
                false).newRestClient();
```

### Custodial Wallet Sample
```java

import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.CoinInfo;
import com.cobo.custody.api.client.impl.LocalSigner;

String[] key = LocalSigner.generateKeyPair();
String secretKey = key[0];
String apiKey = key[1];

CoboApiRestClient client = CoboApiClientFactory.newInstance(
        new LocalSigner(secretKey),
        Env.DEV,
        false).newRestClient();

ApiResponse<CoinInfo> coinInfo = client.getCoinInfo("BTC");
System.out.println(coinInfo);

```


### MPC Wallet Sample
```java
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.MPCChains;
import com.cobo.custody.api.client.impl.LocalSigner;

String[] key = LocalSigner.generateKeyPair();
String secretKey = key[0];
String apiKey = key[1];

CoboMPCApiRestClient mpc_client = CoboApiClientFactory.newInstance(
        new LocalSigner(secretKey),
        Env.DEV,
        false).newMPCRestClient();

ApiResponse<MPCChains> chains = mpc_client.getSupportedChains();
System.out.println(chains);
```


### Handling Response

```java
import com.cobo.custody.api.client.CoboApiClientFactory;
import com.cobo.custody.api.client.CoboMPCApiRestClient;
import com.cobo.custody.api.client.config.Env;
import com.cobo.custody.api.client.domain.ApiResponse;
import com.cobo.custody.api.client.domain.account.MPCChains;
import com.cobo.custody.api.client.impl.LocalSigner;

CoboApiRestClient client = CoboApiClientFactory.newInstance(
        new LocalSigner(secretKey),
        Env.DEV,
        false).newRestClient();

ApiResponse<CoinInfo> coinInfo = client.getCoinInfo("BTC");
System.out.println(coinInfo);

/* The response like this
"Response{success=true,
          errorCode=0, errorMessage='null',
          errorId='null', errorDescription='null',
          result=CoinInfo{
              coin='BTC', displayCode='BTC',
              description='Bitcoin', decimal=8, canDeposit=true,
              canWithdraw=true, requireMemo=false,
              minimumDepositThreshold='10000',
              balance='10000', absBalance='0.0001',
              feeCoin='BTC', absEstimateFee='0.000948',
              confirmingThreshold=4, dustThreshold=546',
              tokenAddress='',
              absEstimateFeeUsd='35.28'
           }
         }"
*/

 // You can handle the response object as follows:
System.out.println(coinInfo.isSuccess());
System.out.println(coinInfo.getResult().getCoin());
System.out.println(coinInfo.getResult().getBalance());
```

### Handling API Errors
```java
/* The error response like this
Response{ success=false, errorCode=1006,
          errorMessage='Invalid api key, please use standard wallet api key',
          errorId='fbee1201d4ee4a67aae91f05f57e0679',
          errorDescription='Invalid api key, please use standard wallet api key',
          result=null
         }
*/

// You can handle the response object as follows:
System.out.println(coinInfo.isSuccess());
System.out.println(coinInfo.getErrorCode());
System.out.println(coinInfo.getErrorDescription());
System.out.println(coinInfo.getErrorId());
System.out.println(coinInfo.getErrorMessage());
```



<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>