---
title: "Python"
icon: "python"
description:
---
<Note>This content applies to WaaS 1.0 only. We highly recommend that you upgrade to [WaaS 2.0](https://www.cobo.com/developers/v2/guides/overview/introduction).</Note>

# Overview

`pycobosafe` is a Python SDK for sending transactions via `Cobo Safe` in the same way as [brownie](https://github.com/eth-brownie/brownie).

Head to https://github.com/coboglobal/pycobosafe for the source code.

`pycobosafe` also works as an interactive console to interact with `Cobo Safe` contracts. Read more [here](/v1/sdks-and-tools/tools/pycobosafe).

# Installation

```sh
pip install git+https://github.com/coboglobal/pycobosafe
```

# Usage

Your `.env` configuration
```
DELEGATE = <Your brownie account id>
COBOSAFE = <Address of CoboSafeAccount>
```

The code performs a transfer of WMATIC ERC20 token on Polygon network.
```py
import json
import os

import dotenv
from brownie import Contract, accounts, network
from web3 import Web3

from pycobosafe.account import CoboSafeAccount

# Connect to the network. A fork chain is used for test.
network.connect("polygon-main-fork")

# Load delegate's private key / keystore file and CoboSafe address.
dotenv.load_dotenv()
DELEGATE = accounts.load((os.getenv("DELEGATE"))) # This is safer than plain private key.
COBOSAFE = os.getenv("COBOSAFE")
assert DELEGATE and COBOSAFE, "delegate or cobosafe not set"

cobosafe = CoboSafeAccount(COBOSAFE, DELEGATE)

WMATIC = Web3.toChecksumAddress("0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270")

ERC20_ABI = json.load(open(os.path.join(os.path.dirname(__file__), "ERC20.json")))
erc20 = Contract.from_abi("WMATIC", WMATIC, ERC20_ABI)

print(erc20.balanceOf(cobosafe.safe.address))

# Send transaction as other brownie accounts do.
erc20.transfer(DELEGATE, 1, {"from": cobosafe})

print(erc20.balanceOf(cobosafe.safe.address))
```

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>