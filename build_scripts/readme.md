## 生成新的 MDX 文件和 Code Samples（每次全量构建）

### **步骤**
1. **安装依赖**
   - **Homebrew 安装工具**：
     ```sh
     brew install prettier golines
     ```
   - **pip 安装 Python 依赖**：
     ```sh
     pip install black openapi yaml git openai
     ```

2. **配置环境变量**
   - 如果不使用 AI 优化示例代码，可以跳过这一步。
   - 需要使用 AI 优化时，配置 `OPENAI_API_KEY`：
     ```sh
     export OPENAI_API_KEY="your-api-key"
     ```
   - 需要使用 AI 优化时，配置 `ASSISTANT_ID`：
     ```sh
     export ASSISTANT_ID="assistant-id"
     ```

3. **执行构建脚本**
   - **默认模式**（不使用 AI 优化，执行速度较快）：
     ```sh
     bash build_mdx_and_code.sh
     ```
   - **使用 AI 优化代码示例**（执行时间较长，约 10+ 分钟）：
     - 需要修改 `build_mdx_and_code.sh` 以启用 AI 处理。

---

## **搬运文件到 `prod` 环境的 `developer-hub` 仓库**

### **步骤**
1. **配置 `copy_to_prod.py` 的路径**
   在 `copy_to_prod.py` 里设置本地 `sandbox` 和 `prodrepo` 路径：
   ```python
   SOURCE_BASE = PROJECT_ROOT / "developer-site-waas2"
   TARGET_BASE = PROJECT_ROOT / "developer-site"
