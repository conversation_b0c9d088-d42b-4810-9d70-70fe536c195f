import openai
import os
import concurrent.futures
import yaml
import json
import re

from logger_config import logger


class EnhanceCodeSamplesUsingAI:
    def __init__(self, max_workers, build_script_dir, project_dir):
        self.client = openai.Client()
        self.assistant_id = os.environ.get("ASSISTANT_ID")
        self.build_script_dir = build_script_dir
        self.project_dir = project_dir
        self.openapi_file_id = ""
        self.openapi_yaml_path = os.path.join(project_dir, "v2/cobo_waas2_openapi_spec/dev_openapi.yaml")
        self.openapi_json_path = os.path.join(build_script_dir, "dev_openapi.json")
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.codes = {}

    def upload_files(self):
        """将 OpenAPI 规范上传至 OpenAI"""
        for file in self.client.files.list().data:
            self.client.files.delete(file.id)
            logger.info(f"已删除旧文件: {file.filename} (ID: {file.id})")

        with open(self.openapi_yaml_path, "r", encoding="utf-8") as f:
            yaml_data = yaml.safe_load(f)
        with open(self.openapi_json_path, "w", encoding="utf-8") as f:
            json.dump(yaml_data, f, indent=4, ensure_ascii=False)
        logger.info(f"已将 YAML 转换为 JSON: {self.openapi_json_path}")

        with open(self.openapi_json_path, "rb") as f:
            file_obj = self.client.files.create(file=f, purpose="assistants")
            self.openapi_file_id = file_obj.id

    def ai_task(self, unique_id, prompt_message):
        thread = self.client.beta.threads.create()
        self.codes[unique_id] = {}
        self.codes[unique_id]["java"] = ""
        self.codes[unique_id]["js"] = ""
        self.codes[unique_id]["python"] = ""
        self.codes[unique_id]["go"] = ""

        self.client.beta.threads.messages.create(
            thread_id=thread.id,
            role="user",
            content=prompt_message,
            attachments=[
                {"file_id": self.openapi_file_id,
                 "tools": [{"type": "code_interpreter"}]
                 }
            ]
        )

        run = self.client.beta.threads.runs.create_and_poll(
            thread_id=thread.id,
            assistant_id=self.assistant_id
        )
        logger.info(f"✅ Assistant 运行完成，状态: {run.status}")

        messages = self.client.beta.threads.messages.list(thread_id=thread.id)

        pattern_js = r"```javascript\n([\s\S]*?)\n```"
        pattern_java = r"```java\n([\s\S]*?)\n```"
        pattern_python = r"```python\n([\s\S]*?)\n```"
        pattern_go = r"```go\n([\s\S]*?)\n```"

        output_text = []
        for message in messages:
            output_text.append(message.content[0].text.value)
            if re.findall(pattern_java, message.content[0].text.value):
                self.codes[unique_id]["java"] = re.findall(pattern_java, message.content[0].text.value)[0]
            if re.findall(pattern_js, message.content[0].text.value):
                self.codes[unique_id]["js"] = re.findall(pattern_js, message.content[0].text.value)[0]
            if re.findall(pattern_python, message.content[0].text.value):
                self.codes[unique_id]["python"] = re.findall(pattern_python, message.content[0].text.value)[0]
            if re.findall(pattern_go, message.content[0].text.value):
                self.codes[unique_id]["go"] = re.findall(pattern_go, message.content[0].text.value)[0]
        for text in reversed(output_text):
            logger.info(text)

    def generate_code_samples_by_ai(self, codes):
        prompt_messages = {}
        self.codes = {}
        for unique_id, code in codes.items():
            http_method, url, file_name = unique_id.split("#")
            prompt_message = f"""{self.openapi_file_id}是基于OpenAPI规范定义的API协议，请帮我分析该JSON文件里Paths的Key为 {url} 同时HTTP METHOD是 {http_method} 的完整参数定义。
                你需要基于上边已经分析的完整参数定义，并根据我提供的初版示例代码，最后给出该接口详细的 Python、Go、Java、JS 代码示例。
                要求示例完整包括所有参数，如果参数是模型，不要使用内联定义，结构性和可读性强，下边是我提供的初版代码示例：
                Java 初版示例代码如下:
                {code['java']}
                Python 初版示例代码如下:
                {code['python']}
                JS 初版示例代码如下:
                {code['js']}
                Go 初版示例代码如下:
                {code['go']}
                请严格按照下列格式返回数据，不要添加额外的文本，格式如下：
                ```javascript
                ```
                ```java

                ```
                ```python

                ```

                ```go

                ```

                """
            prompt_messages[unique_id] = prompt_message

        futures = {self.executor.submit(self.ai_task, unique_id, task): task for unique_id, task in
                   prompt_messages.items()}

        results = []
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())

        return self.codes

    def shutdown(self):
        """关闭线程池"""
        self.executor.shutdown()
