from logger_config import logger
import re
import os


class WriteCodeSamplesToMDXFiles:

    def __init__(self, build_script_dir, project_dir):
        self.build_script_dir = build_script_dir
        self.project_dir = project_dir

    def update_sample_code_to_mdx(self, sdk_code: dict):
        for unique_id, codes in sdk_code.items():
            http_method, url, file_name = unique_id.split("#")
            find_file_name = f"{file_name}.mdx"
            results = []
            api_mdx_path = os.path.join(self.project_dir, "v2/api-references/")
            for dirpath, dirnames, filenames in os.walk(api_mdx_path):
                if find_file_name in filenames:
                    results.append(os.path.join(dirpath, find_file_name))
            if len(results) == 0:
                logger.info(f"没有找到文件 {find_file_name}")
            for result in results:
                logger.info(f"更新代码文件路径:{result}")
                if self._check_found_file(result, http_method, url):
                    self._write_code_to_mdx_file(result, codes)

    def _check_found_file(self, file_path, http_method, url):
        with open(file_path, "r") as file:
            lines = file.readlines()
        matches = re.findall(r"openapi:\s*(\S+)\s*(\S+)", lines[1])
        if matches[0][0] == http_method.lower() and matches[0][1] == url:
            return True
        else:
            return False

    def _write_code_to_mdx_file(self, file_path, codes):
        write_content = (f"\n\n<RequestExample>\n"
                         f"```python Python\n{codes['python']}\n```\n"
                         f"```java Java\n{codes['java']}\n```\n"
                         f"```go Go\n{codes['go']}\n```\n"
                         f"```javascript JavaScript\n{codes['js']}\n```\n"
                         "</RequestExample>")
        with open(file_path, "a") as file:
            file.writelines(write_content)
