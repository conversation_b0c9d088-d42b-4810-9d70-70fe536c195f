import os
import git
import shutil
import subprocess
import glob
from logger_config import logger


class GenerateLatestOpenApiDocs:

    def __init__(self, build_script_dir, project_dir):
        self.build_script_dir = build_script_dir
        self.project_dir = project_dir

    @classmethod
    def pull_and_update_devapi_template_repo(cls):
        repo_path = "./cobo-waas2-sdk-template"
        repo_url = "**************:CoboGlobal/cobo-waas2-sdk-template.git"
        if not os.path.exists(repo_path):
            logger.info("克隆仓库...")
            git.Repo.clone_from(repo_url, repo_path, branch="master")
        else:
            logger.info("更新仓库...")
            repo = git.Repo(repo_path)
            origin = repo.remotes.origin
            origin.pull("master")
        logger.info("仓库已更新到最新的 master 分支")

    def build_latest_docs(self):
        old_dev_openapi_file_path = os.path.join(self.build_script_dir, "cobo-waas2-sdk-template/openapi.yaml")
        if os.path.exists(old_dev_openapi_file_path):
            os.remove(old_dev_openapi_file_path)
            logger.info(f"文件 {old_dev_openapi_file_path} 已删除")
        else:
            logger.info(f"文件 {old_dev_openapi_file_path} 不存在")

        source_file = os.path.join(self.project_dir, "v2/cobo_waas2_openapi_spec/dev_openapi.yaml")
        destination_file = os.path.join(self.build_script_dir, "cobo-waas2-sdk-template/openapi.yaml")
        shutil.copy2(source_file, destination_file)

        logger.info(f"文件已成功复制并覆盖: {destination_file}")

        build_docs_sh_file = "./build_scripts/build_all.sh"
        target_dir = os.path.join(self.build_script_dir, "cobo-waas2-sdk-template")

        result = subprocess.run(["bash", build_docs_sh_file], cwd=target_dir, capture_output=True, text=True)

        logger.info("标准输出:", result.stdout)

    def move_docs_to_temp_dir(self):
        for language in ("go", "java", "js", "php", "python"):
            if language == "php":
                target_dir = os.path.join(self.build_script_dir,
                                          f"cobo-waas2-sdk-template/build/cobo-waas2-{language}-sdk/docs/Api")
            else:
                target_dir = os.path.join(self.build_script_dir,
                                          f"cobo-waas2-sdk-template/build/cobo-waas2-{language}-sdk/docs")
            matching_files = glob.glob(os.path.join(target_dir, "*[aA][pP][iI].md"))

            dst_dir = os.path.join(self.build_script_dir, f"docs/{language}/")
            os.makedirs(dst_dir, exist_ok=True)

            for file in matching_files:
                shutil.copy(file, dst_dir)
