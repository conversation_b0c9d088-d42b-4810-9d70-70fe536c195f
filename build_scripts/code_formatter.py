import os
import black
import tempfile
import re
import subprocess


class CodeFormatter:

    def __init__(self, build_script_dir, project_dir):
        self.build_script_dir = build_script_dir
        self.project_dir = project_dir

    def format_all_codes(self, codes):
        formatted_codes = {}
        if not os.path.exists(f"{self.build_script_dir}/temps"):
            os.mkdir(f"{self.build_script_dir}/temps")
        for method, code in codes["java"].items():
            formatted_codes[method] = {}

            code = self._format_java(code)
            formatted_codes[method]["java"] = code

            code = self._format_go(codes["go"][method])
            formatted_codes[method]["go"] = code

            code = self._format_python(codes["python"][method])
            formatted_codes[method]["python"] = code

            code = self._format_js(codes["js"][method])
            formatted_codes[method]["js"] = code

        return formatted_codes

    def _format_python(self, code):
        formatted_code = black.format_str(code, mode=black.FileMode())
        return formatted_code

    def _format_java(self, code):
        with tempfile.NamedTemporaryFile(prefix='java_temp_',
                                         dir=f"{self.build_script_dir}/temps/",
                                         delete=True,
                                         mode="w+",
                                         suffix=".java") as temp_file:
            temp_file.write(code)
            temp_file.flush()
            subprocess.run(
                ["java", "-jar",
                 f"{self.build_script_dir}/google-java-format.jar",
                 "--replace",
                 temp_file.name])
            temp_file.seek(0)
            formatted_code = temp_file.read()
        return formatted_code

    def _format_go(self, code):
        code = re.sub(r"string\(\[B@[a-f0-9]+\)",
                      r"0x6057361d00000000000000000000000000000000000000000000000000000000000000fa",
                      code)
        with tempfile.NamedTemporaryFile(prefix='go_temp_',
                                         dir=f"{self.build_script_dir}/temps/",
                                         delete=True,
                                         mode="w+",
                                         suffix=".go") as temp_file:
            temp_file.write(code)
            temp_file.flush()
            subprocess.run(['golines', '-w', temp_file.name])

            temp_file.seek(0)
            formatted_code = temp_file.read()
        return formatted_code

    def _format_js(self, code):
        with tempfile.NamedTemporaryFile(prefix='js_temp_',
                                         dir=f"{self.build_script_dir}/temps/",
                                         delete=True,
                                         mode="w+",
                                         suffix=".js") as temp_file:
            temp_file.write(code)
            temp_file.flush()
            subprocess.run(
                ["prettier", "--write", temp_file.name, "--print-width", "80"]
            )
            temp_file.seek(0)
            formatted_code = temp_file.read()
        return formatted_code
