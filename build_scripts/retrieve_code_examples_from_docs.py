import glob
import re


class RetrieveCodeExamplesFromDocs:

    def __init__(self, build_script_dir, project_dir):
        self.build_script_dir = build_script_dir
        self.project_dir = project_dir

    def retrieve_examples_from_docs(self):
        api_docs_code_examples = {}
        for language in ("go", "java", "js", "python"):
            api_docs_code_examples[language] = self.process_api_docs(language)
        return api_docs_code_examples

    def process_api_docs(self, language: str):
        method_to_code_map = {}
        api_docs_path = glob.glob(f"{self.build_script_dir}/docs/{language}/*.md")

        pattern_to_get_code = {
            "java": r"#\s*\*\*(\S+)\*\*.*?```java(.*?)```",
            "python": r"#\s*\*\*(\S+)\*\*.*?```python(.*?)```",
            "go": r"##\s*(\S+)\n+>\s+.*?```go(.*?)```",
            "js": r"##\s*(\S+)\n+>\s+.*?```javascript(.*?)```",
        }

        pattern_to_get_dec = {
            "java": r'\[\*\*(\S+)\*\*]\(\S+.md#\S+\s*\|\s*\*\*(\S+)\*\*\s*(.*?)\|(.*?)\|\n+',
            "python": r'\[\*\*(\S+)\*\*]\(\S+.md#\S+\s*\|\s*\*\*(\S+)\*\*\s*(.*?)\|(.*?)\n+',
            "go": r'\[\*\*(\S+)\*\*]\(\S+.md#\S+\s*\|\s*\*\*(\S+)\*\*\s*(.*?)\|(.*?)\n+',
            "js": r'\[\*\*(\S+)\*\*]\(\S+.md#\S+\s*\|\s*\*\*(\S+)\*\*\s*(.*?)\|(.*?)\n+',
        }
        for file_path in api_docs_path:
            with open(file_path, 'r', encoding='utf-8') as file:
                markdown_text = file.read()
            method_to_des = re.findall(pattern_to_get_dec[language], markdown_text, re.DOTALL)
            method_to_code = re.findall(pattern_to_get_code[language], markdown_text, re.DOTALL)
            method_to_code_dict = {i[0]: i[1] for i in method_to_code}

            for group in method_to_des:
                mdx_file_name = self.translate_des_to_mdx_name(group[3].strip())
                unique_id = f"{group[1].strip().lower()}#{group[2].strip()}#{mdx_file_name}"
                method_to_code_map[unique_id] = method_to_code_dict[group[0]].strip()
        return method_to_code_map

    def translate_des_to_mdx_name(self, des: str):
        return des.replace(" ", "-").lower()
