import os
import subprocess
import yaml
import re
import shutil
from logger_config import logger


class GenerateNewMDXByOpenAPI:

    def __init__(self, build_script_dir, project_dir):
        self.build_script_dir = build_script_dir
        self.project_dir = project_dir

    def get_all_folders_to_be_deleted(self):
        folders_to_be_deleted = []
        source_file = os.path.join(self.project_dir, "v2/cobo_waas2_openapi_spec/dev_openapi.yaml")
        with open(source_file, "r", encoding="utf-8") as file:
            data = yaml.safe_load(file)
        for tag in data["tags"]:
            converted_name = re.sub(r"\s*-\s*", "--", tag["name"].lower())
            converted_name = re.sub(r"\s+", "-", converted_name)
            folders_to_be_deleted.append(os.path.join(self.project_dir, f"v2/api-references/{converted_name}"))
        return folders_to_be_deleted

    def delete_api_mdx_files(self):
        folders_to_be_deleted = self.get_all_folders_to_be_deleted()
        for folder in folders_to_be_deleted:
            if os.path.exists(folder):
                shutil.rmtree(folder)
                logger.info(f'已删除: {folder}')
            else:
                logger.info(f'文件夹不存在: {folder}')

    def generate_api_mdx_files(self):
        try:
            source_file = os.path.join(self.project_dir, "v2/cobo_waas2_openapi_spec/dev_openapi.yaml")
            destination_dir = os.path.join(self.project_dir, "v2/api-references/")
            command = [
                "npx", "@mintlify/scraping@3.0.141", "openapi-file", source_file, "-o", destination_dir
            ]
            original_spec_data = subprocess.run(command, capture_output=True, text=True).stdout.split('\n', 1)[1]
        except Exception as e:
            m_logger.error("程序运行出错: %s", str(e))
            m_logger.debug(traceback.format_exc())
            sys.exit(1)
