import sys
import traceback
import os
import argparse
from logger_config import logger
from generate_latest_devapi_template_repo import GenerateLatestOpenApiDocs
from retrieve_code_examples_from_docs import RetrieveCodeExamplesFromDocs
from code_formatter import CodeFormatter
from generate_new_mdx_by_open_api import GenerateNewMDXByOpenAPI
from enhance_code_samples_using_ai import EnhanceCodeSamplesUsingAI
from write_code_samples_to_mdx_files import WriteCodeSamplesToMDXFiles

build_script_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(build_script_dir)


def chunk_dict(data, chunk_size=10):
    """使用列表切片，每次取 chunk_size 个数据"""
    items = list(data.items())
    for i in range(0, len(items), chunk_size):
        yield dict(items[i:i + chunk_size])


def main():
    try:
        parser = argparse.ArgumentParser(description="OpenAPI 文档和代码示例处理")
        parser.add_argument("--ai", action="store_true", help="是否使用AI优化Code Samples")
        parser.add_argument("--no-ai", dest="ai", action="store_false", help="禁用AI优化")

        args = parser.parse_args()
        ai_client = None
        write_code_samples_to_mdx_files = WriteCodeSamplesToMDXFiles(build_script_dir, project_dir)
        logger.info(f"使用AI优化:{args.ai}")

        logger.info("开始拉取并更新 OpenAPI 文档...")
        generate_latest_open_api_docs = GenerateLatestOpenApiDocs(build_script_dir, project_dir)
        generate_latest_open_api_docs.pull_and_update_devapi_template_repo()
        generate_latest_open_api_docs.build_latest_docs()
        generate_latest_open_api_docs.move_docs_to_temp_dir()

        logger.info("获取代码示例...")
        retrieve_code_examples_from_docs = RetrieveCodeExamplesFromDocs(build_script_dir, project_dir)
        code_examples = retrieve_code_examples_from_docs.retrieve_examples_from_docs()

        logger.info("格式化所有代码示例...")
        code_formatter = CodeFormatter(build_script_dir, project_dir)
        formatted_codes = code_formatter.format_all_codes(code_examples)

        logger.info("生成新的MDX文件...")
        generate_new_mdx = GenerateNewMDXByOpenAPI(build_script_dir, project_dir)
        generate_new_mdx.delete_api_mdx_files()
        generate_new_mdx.generate_api_mdx_files()

        if args.ai:
            ai_client = EnhanceCodeSamplesUsingAI(max_workers=10, build_script_dir=build_script_dir,
                                                  project_dir=project_dir)
            ai_client.upload_files()

        for i, chunk in enumerate(chunk_dict(formatted_codes, 10)):
            if args.ai:
                processed_chunk = ai_client.generate_code_samples_by_ai(chunk)
                logger.info("把code samples写入MDX文件")
                write_code_samples_to_mdx_files.update_sample_code_to_mdx(processed_chunk)
            else:
                logger.info("把code samples写入MDX文件")
                write_code_samples_to_mdx_files.update_sample_code_to_mdx(chunk)

    except Exception as e:
        logger.error("程序运行出错: %s", str(e))
        logger.debug(traceback.format_exc())


if __name__ == "__main__":
    main()
