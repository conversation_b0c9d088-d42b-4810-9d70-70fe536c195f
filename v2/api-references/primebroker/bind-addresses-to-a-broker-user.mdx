---
openapi: post /prime_broker/user/{user_id}/addresses
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_prime_broker_address201_response import CreatePrimeBrokerAddress201Response
from cobo_waas2.models.create_prime_broker_address_request import CreatePrimeBrokerAddressRequest
from cobo_waas2.models.address import Address
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.PrimeBrokerApi(api_client)
    user_id = "168108513539918"
    create_prime_broker_address_request = cobo_waas2.CreatePrimeBrokerAddressRequest(
        addresses=[
            Address(
                chain_id="ETH",
                address="******************************************"
            )
        ]
    )

    try:
        api_response = api_instance.create_prime_broker_address(
            user_id,
            create_prime_broker_address_request=create_prime_broker_address_request,
        )
        print("The response of PrimeBrokerApi->create_prime_broker_address:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling PrimeBrokerApi->create_prime_broker_address: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.PrimeBrokerApi;
import com.cobo.waas2.model.CreatePrimeBrokerAddressRequest;
import com.cobo.waas2.model.Address;
import com.cobo.waas2.model.CreatePrimeBrokerAddress201Response;

import java.util.ArrayList;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    PrimeBrokerApi apiInstance = new PrimeBrokerApi();

    String userId = "168108513539918";
    CreatePrimeBrokerAddressRequest createPrimeBrokerAddressRequest = new CreatePrimeBrokerAddressRequest();

    // Create and add address
    Address address = new Address();
    address.setAddress("******************************************");
    address.setChainId("ETH");

    ArrayList<Address> addresses = new ArrayList<Address>();
    addresses.add(address);
    createPrimeBrokerAddressRequest.setAddresses(addresses);

    try {
      CreatePrimeBrokerAddress201Response result = apiInstance.createPrimeBrokerAddress(userId, createPrimeBrokerAddressRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling PrimeBrokerApi#createPrimeBrokerAddress");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	userId := "168108513539918"
	createPrimeBrokerAddressRequest := *coboWaas2.NewCreatePrimeBrokerAddressRequest()
	
	// Add Address
	address := coboWaas2.Address{
		ChainId: "ETH",
		Address: "******************************************",
	}
	createPrimeBrokerAddressRequest.Addresses = []coboWaas2.Address{address}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.PrimeBrokerAPI.CreatePrimeBrokerAddress(ctx, userId).
		CreatePrimeBrokerAddressRequest(createPrimeBrokerAddressRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `PrimeBrokerAPI.CreatePrimeBrokerAddress``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `PrimeBrokerAPI.CreatePrimeBrokerAddress`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.PrimeBrokerApi();
const user_id = "168108513539918";
const opts = {
  CreatePrimeBrokerAddressRequest: new CoboWaas2.CreatePrimeBrokerAddressRequest({
    addresses: [
      {
        chain_id: "ETH",
        address: "******************************************"
      }
    ]
  }),
};

apiInstance.createPrimeBrokerAddress(user_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>