---
openapi: put /prime_broker/user/{user_id}/guard_pubkey
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.change_guard_pubkey200_response import ChangeGuardPubkey200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.PrimeBrokerApi(api_client)
    user_id = "168108513539918"

    try:
        # Change Guard pubkey binding
        api_response = api_instance.change_guard_pubkey(user_id)
        print("The response of PrimeBrokerApi->change_guard_pubkey:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling PrimeBrokerApi->change_guard_pubkey:")
        print("Status code: ", e.status)
        print("Reason: ", e.reason)
        print("Response headers: ", e.headers)
        print(e.body)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.PrimeBrokerApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    PrimeBrokerApi apiInstance = new PrimeBrokerApi();
    String userId = "168108513539918";
    try {
      ChangeGuardPubkey200Response result = apiInstance.changeGuardPubkey(userId);
      System.out.println("API called successfully. Response data: " + result);
    } catch (ApiException e) {
      System.err.println("Exception when calling PrimeBrokerApi#changeGuardPubkey");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	userId := "168108513539918"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.PrimeBrokerAPI.ChangeGuardPubkey(ctx, userId).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `PrimeBrokerAPI.ChangeGuardPubkey``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ChangeGuardPubkey`: ChangeGuardPubkey200Response
	fmt.Fprintf(os.Stdout, "Response from `PrimeBrokerAPI.ChangeGuardPubkey`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.PrimeBrokerApi();
const user_id = "168108513539918";
apiInstance.changeGuardPubkey(user_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + JSON.stringify(data, null, 2));
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>