---
openapi: get /prime_broker/user/{user_id}/guard_pubkey
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models import QueryGuardPubkey200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# API client
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.PrimeBrokerApi(api_client)
    user_id = "168108513539918" 

    try:
        # Query Guard public key
        api_response = api_instance.query_guard_pubkey(user_id)
        print("The response of PrimeBrokerApi->query_guard_pubkey:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling PrimeBrokerApi->query_guard_pubkey: %s\n" % e)
```
```java Java
// Import necessary classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.PrimeBrokerApi;
import com.cobo.waas2.model.QueryGuardPubkey200Response;

public class Example {
  public static void main(String[] args) {
    // API client configuration
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    // PrimeBrokerApi instance
    PrimeBrokerApi apiInstance = new PrimeBrokerApi();
    String userId = "168108513539918";
    try {
      // Retrieve Guard public key
      QueryGuardPubkey200Response result = apiInstance.queryGuardPubkey(userId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling PrimeBrokerApi#queryGuardPubkey");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	userId := "168108513539918"

	// Configuration and API client setup
	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	// Query Guard public key
	resp, r, err := apiClient.PrimeBrokerAPI.QueryGuardPubkey(ctx, userId).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `PrimeBrokerAPI.QueryGuardPubkey``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `PrimeBrokerAPI.QueryGuardPubkey`: %v\n", resp)
}
```
```javascript JavaScript
// Import the library
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Invoke API
const apiInstance = new CoboWaas2.PrimeBrokerApi();
const user_id = "168108513539918";

apiInstance.queryGuardPubkey(user_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>