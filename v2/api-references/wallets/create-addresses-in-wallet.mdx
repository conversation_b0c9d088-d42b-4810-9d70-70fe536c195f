---
openapi: post /wallets/{wallet_id}/addresses
---

<RequestExample>
```python Python
# Import necessary components
import cobo_waas2
from cobo_waas2.models import CreateAddressRequest, AddressEncoding
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure API client
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Your private key
    host="https://api.dev.cobo.com/v2",  # Development environment
)

# Use the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # API instance
    api_instance = cobo_waas2.WalletsApi(api_client)
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"  # Example wallet ID

    # Create request
    create_address_request = CreateAddressRequest(
        chain_id="ETH",  # Chain ID example
        count=1,  # Number of addresses to create
        # Uncomment and specify if creating tweaked Taproot addresses
        # taproot_script_tree_hashes=["0x138fdd0f6c3803d45553e730c25924baf7be741b8a72a4e6fdbd9d44cb19f85b"],
        # taproot_internal_address="**********************************",
        # encoding=AddressEncoding(...)
    )

    try:
        # Create addresses in wallet
        api_response = api_instance.create_address(wallet_id, create_address_request=create_address_request)
        print("The response of WalletsApi->create_address:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->create_address:", e)
```
```java Java
// Import classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    // Configure API client
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);  // Dev environment
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>"); // Your private key

    WalletsApi apiInstance = new WalletsApi();
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");

    // Create request
    CreateAddressRequest createAddressRequest = new CreateAddressRequest();
    createAddressRequest.setChainId("ETH");  // Chain ID
    createAddressRequest.setCount(1);  // Number of addresses to create
    // Uncomment and specify if needed
    // createAddressRequest.setTaprootScriptTreeHashes(Arrays.asList("0x138fdd0f6c3803d45553e730c25924baf7be741b8a72a4e6fdbd9d44cb19f85b"));
    // createAddressRequest.setTaprootInternalAddress("**********************************");
    // createAddressRequest.setEncoding(...)

    try {
      List<AddressInfo> result = apiInstance.createAddress(walletId, createAddressRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#createAddress");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Configuration and client
	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Dev environment setup
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>", // Your private key
	})

	// Wallet and Request
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	createAddressRequest := coboWaas2.CreateAddressRequest{
		ChainId: "ETH", // Chain ID
		Count:   1,     // Number of addresses to create
		// Uncomment and specify if needed
		// TaprootScriptTreeHashes: []string{"0x138fdd0f6c3803d45553e730c25924baf7be741b8a72a4e6fdbd9d44cb19f85b"},
		// TaprootInternalAddress:  "**********************************",
		// Encoding:                coboWaas2.AddressEncoding{},
	}

	// API Call
	resp, r, err := apiClient.WalletsAPI.CreateAddress(ctx, walletId).
		CreateAddressRequest(createAddressRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.CreateAddress`: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.CreateAddress`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV); // Dev environment
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Your private key

// Call the API
const apiInstance = new CoboWaas2.WalletsApi();
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

// Create request
const opts = {
  CreateAddressRequest: new CoboWaas2.CreateAddressRequest({
    chain_id: "ETH", // Chain ID
    count: 1, // Number of addresses to create
    // Uncomment and specify if needed
    // taproot_script_tree_hashes: ["0x138fdd0f6c3803d45553e730c25924baf7be741b8a72a4e6fdbd9d44cb19f85b"],
    // taproot_internal_address: "**********************************",
    // encoding: new CoboWaas2.AddressEncoding(...)
  }),
};

apiInstance.createAddress(wallet_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: ", data);
  },
  (error) => {
    console.error("Error calling API:", error);
  }
);
```
</RequestExample>