---
openapi: post /wallets/{wallet_id}/utxos/lock
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.lock_utxos_request import LockUtxosRequest
from cobo_waas2.models.lock_utxos_request_utxos_inner import LockUtxosRequestUtxosInner
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)

    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    utxos = [
        LockUtxosRequestUtxosInner(
            token_id="BTC",
            tx_hash="9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb",
            vout_n=0
        )
    ]

    lock_utxos_request = LockUtxosRequest(utxos=utxos)

    try:
        api_response = api_instance.lock_utxos(wallet_id, lock_utxos_request=lock_utxos_request)
        print("The response of WalletsApi->lock_utxos:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->lock_utxos: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.LockUtxosRequest;
import com.cobo.waas2.model.LockUtxosRequestUtxosInner;
import com.cobo.waas2.model.LockUtxos201Response;

import java.util.Arrays;
import java.util.UUID;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    WalletsApi apiInstance = new WalletsApi();
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");

    LockUtxosRequest.UtxosInner utxos = new LockUtxosRequestUtxosInner();
    utxos.setTokenId("BTC");
    utxos.setTxHash("9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb");
    utxos.setVoutN(0);

    LockUtxosRequest lockUtxosRequest = new LockUtxosRequest();
    lockUtxosRequest.setUtxos(Arrays.asList(utxos));

    try {
      LockUtxos201Response result = apiInstance.lockUtxos(walletId, lockUtxosRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#lockUtxos");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	utxos := coboWaas2.NewLockUtxosRequestUtxosInner("BTC", "9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb", int32(0))
	lockUtxosRequest := *coboWaas2.NewLockUtxosRequest([]coboWaas2.LockUtxosRequestUtxosInner{*utxos})

	configuration := coboWaas2.NewConfiguration()

	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.WalletsAPI.LockUtxos(ctx, walletId).
		LockUtxosRequest(lockUtxosRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.LockUtxos``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.LockUtxos`: %v\n", resp)
}
```
```javascript JavaScript
import CoboWaas2 from "@cobo/cobo-waas2";

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

const apiInstance = new CoboWaas2.WalletsApi();
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const lockUtxosRequest = {
  utxos: [
    {
      token_id: "BTC",
      tx_hash: "9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb",
      vout_n: 0,
    },
  ],
};

apiInstance.lockUtxos(wallet_id, { LockUtxosRequest: lockUtxosRequest }).then(
  (data) => {
    console.log("API successfully called. Data returned: " + data);
  },
  (error) => {
    console.error("Error: ", error);
  }
);
```
</RequestExample>