---
openapi: post /wallets
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_wallet_params import CreateCustodialWalletParams, CreateMpcWalletParams, CreateExchangeWalletParams
from cobo_waas2.models.created_wallet_info import CreatedWalletInfo
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)

    # Setup CreateCustodialWalletParams
    custodial_wallet_params = CreateCustodialWalletParams(
        name="My WaaS 2.0 Wallet",
        wallet_type="Custodial",
        wallet_subtype="Asset"
    )

    # Setup CreateMpcWalletParams
    mpc_wallet_params = CreateMpcWalletParams(
        name="My WaaS 2.0 Wallet",
        wallet_type="MPC",
        wallet_subtype="Asset",
        vault_id="<VAULT_ID>"
    )

    # Setup CreateExchangeWalletParams
    exchange_wallet_params = CreateExchangeWalletParams(
        name="My WaaS 2.0 Wallet",
        wallet_type="Exchange",
        wallet_subtype="Asset",
        exchange_id="Binance",
        apikey="d8f062da-39f4-4a11-8b9d-12595854237f",
        secret="75B4F636193162488A3728B4A5797708",
        passphrase="sXASDKWKLLsWWEE",
        account_identify="<EMAIL>",
        memo="<EMAIL>",
        ga_code="sXASDKWKLLsWWEE75B4F636193162488A3728B4A5797708",
        main_wallet_id="f47ac10b-58cc-4372-a567-0e02b2c3d479"
    )

    try:
        # Choose the appropriate wallet params when creating a wallet
        api_response = api_instance.create_wallet(create_wallet_params=custodial_wallet_params) # For Custodial
        # api_response = api_instance.create_wallet(create_wallet_params=mpc_wallet_params) # For MPC
        # api_response = api_instance.create_wallet(create_wallet_params=exchange_wallet_params) # For Exchange
        print("The response of WalletsApi->create_wallet:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling WalletsApi->create_wallet: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        defaultClient.setEnv(Env.DEV);
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        WalletsApi apiInstance = new WalletsApi();

        // Setup CreateWalletParams - Custodial Example
        CreateCustodialWalletParams custodialWalletParams = new CreateCustodialWalletParams()
            .name("My WaaS 2.0 Wallet")
            .walletType(WalletType.CUSTODIAL)
            .walletSubtype(WalletSubtype.ASSET);

        // Setup CreateWalletParams - MPC Example
        CreateMpcWalletParams mpcWalletParams = new CreateMpcWalletParams()
            .name("My WaaS 2.0 Wallet")
            .walletType(WalletType.MPC)
            .walletSubtype(WalletSubtype.ASSET)
            .vaultId("<VAULT_ID>");

        // Setup CreateWalletParams - Exchange Example
        CreateExchangeWalletParams exchangeWalletParams = new CreateExchangeWalletParams()
            .name("My WaaS 2.0 Wallet")
            .walletType(WalletType.EXCHANGE)
            .walletSubtype(WalletSubtype.ASSET)
            .exchangeId(ExchangeId.BINANCE)
            .apikey("d8f062da-39f4-4a11-8b9d-12595854237f")
            .secret("75B4F636193162488A3728B4A5797708")
            .passphrase("sXASDKWKLLsWWEE")
            .accountIdentify("<EMAIL>")
            .memo("<EMAIL>")
            .gaCode("sXASDKWKLLsWWEE75B4F636193162488A3728B4A5797708")
            .mainWalletId("f47ac10b-58cc-4372-a567-0e02b2c3d479");

        try {
            // Choose the appropriate wallet params when creating a wallet
            CreatedWalletInfo result = apiInstance.createWallet(custodialWalletParams); // For Custodial
            // CreatedWalletInfo result = apiInstance.createWallet(mpcWalletParams); // For MPC
            // CreatedWalletInfo result = apiInstance.createWallet(exchangeWalletParams); // For Exchange
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling WalletsApi#createWallet");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Custodial Wallet Params
	createCustodialWalletParams := coboWaas2.CreateCustodialWalletParams{
		Name:         "My WaaS 2.0 Wallet",
		WalletType:   coboWaas2.WalletType("Custodial"),
		WalletSubtype: coboWaas2.WalletSubtype("Asset"),
	}

	// MPC Wallet Params
	createMpcWalletParams := coboWaas2.CreateMpcWalletParams{
		CreateCustodialWalletParams: createCustodialWalletParams,
		VaultId:                    "<VAULT_ID>",
	}

	// Exchange Wallet Params
	createExchangeWalletParams := coboWaas2.CreateExchangeWalletParams{
		CreateCustodialWalletParams: createCustodialWalletParams,
		ExchangeId:                  coboWaas2.ExchangeId("Binance"),
		Apikey:                      "d8f062da-39f4-4a11-8b9d-12595854237f",
		Secret:                      "75B4F636193162488A3728B4A5797708",
		Passphrase:                  "sXASDKWKLLsWWEE",
		AccountIdentify:             "<EMAIL>",
		Memo:                        "<EMAIL>",
		GaCode:                      "sXASDKWKLLsWWEE75B4F636193162488A3728B4A5797708",
		MainWalletId:                "f47ac10b-58cc-4372-a567-0e02b2c3d479",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	// Choose the appropriate wallet params when creating a wallet
	resp, r, err := apiClient.WalletsAPI.CreateWallet(ctx).
		CreateWalletParams(createCustodialWalletParams). // For Custodial
		// CreateWalletParams(createMpcWalletParams). // For MPC
		// CreateWalletParams(createExchangeWalletParams). // For Exchange
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.CreateWallet``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.CreateWallet`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.WalletsApi();

const custodialWalletParams = new CoboWaas2.CreateCustodialWalletParams(
    "My WaaS 2.0 Wallet", CoboWaas2.WalletType.CUSTODIAL, CoboWaas2.WalletSubtype.ASSET
);

const mpcWalletParams = new CoboWaas2.CreateMpcWalletParams(
    "My WaaS 2.0 Wallet", CoboWaas2.WalletType.MPC, CoboWaas2.WalletSubtype.ASSET, "<VAULT_ID>"
);

const exchangeWalletParams = new CoboWaas2.CreateExchangeWalletParams(
    "My WaaS 2.0 Wallet", CoboWaas2.WalletType.EXCHANGE, CoboWaas2.WalletSubtype.ASSET,
    "Binance", "d8f062da-39f4-4a11-8b9d-12595854237f", "75B4F636193162488A3728B4A5797708",
    "sXASDKWKLLsWWEE", "<EMAIL>", "<EMAIL>", "sXASDKWKLLsWWEE75B4F636193162488A3728B4A5797708",
    "f47ac10b-58cc-4372-a567-0e02b2c3d479"
);

const opts = { CreateWalletParams: custodialWalletParams }; // For Custodial
// const opts = { CreateWalletParams: mpcWalletParams }; // For MPC
// const opts = { CreateWalletParams: exchangeWalletParams }; // For Exchange

apiInstance.createWallet(opts).then(
    (data) => {
        console.log("API called successfully. Returned data: " + data);
    },
    (error) => {
        console.error(error);
    }
);
```
</RequestExample>