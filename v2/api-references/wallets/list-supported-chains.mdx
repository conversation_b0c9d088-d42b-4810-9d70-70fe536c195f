---
openapi: get /wallets/chains
---

<RequestExample>
```python Python
# Import necessary modules and packages
import cobo_waas2
from cobo_waas2.models.wallet_subtype import WalletSubtype
from cobo_waas2.models.wallet_type import WalletType
from pprint import pprint
from cobo_waas2.rest import ApiException

# Set the configuration for the API
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)

    # Set parameters
    wallet_type = WalletType.Custodial
    wallet_subtype = WalletSubtype.Asset
    chain_ids = "BTC,ETH"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        # Call the API
        api_response = api_instance.list_supported_chains(
            wallet_type=wallet_type,
            wallet_subtype=wallet_subtype,
            chain_ids=chain_ids,
            limit=limit,
            before=before,
            after=after,
        )
        print("The response of WalletsApi->list_supported_chains:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->list_supported_chains: %s\n" % e)
```
```java Java
// Import classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment
    defaultClient.setEnv(Env.DEV);

    // Replace '<YOUR_PRIVATE_KEY>' with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    WalletsApi apiInstance = new WalletsApi();
    WalletType walletType = WalletType.fromValue("Custodial");
    WalletSubtype walletSubtype = WalletSubtype.fromValue("Asset");
    String chainIds = "BTC,ETH";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";

    try {
      ListSupportedChains200Response result = apiInstance.listSupportedChains(walletType, walletSubtype, chainIds, limit, before, after);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#listSupportedChains");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletType := coboWaas2.WalletType("Custodial")
	walletSubtype := coboWaas2.WalletSubtype("Asset")
	chainIds := "BTC,ETH"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()

	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)

	// Replace '<YOUR_PRIVATE_KEY>' with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	// Call the API
	resp, r, err := apiClient.WalletsAPI.ListSupportedChains(ctx).
		WalletType(walletType).
		WalletSubtype(walletSubtype).
		ChainIds(chainIds).
		Limit(limit).
		Before(before).
		After(after).
		Execute()

	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.ListSupportedChains``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// Response from `ListSupportedChains`: ListSupportedChains200Response
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.ListSupportedChains`: %v\n", resp)
}
```
```javascript JavaScript
// Import necessary classes and configurations
const CoboWaas2 = require('@cobo/cobo-waas2');

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;

// Select the development environment
apiClient.setEnv(CoboWaas2.Env.DEV);

// Replace '<YOUR_PRIVATE_KEY>' with your private key
apiClient.setPrivateKey('<YOUR_PRIVATE_KEY>');

// Create an instance of the API class
const apiInstance = new CoboWaas2.WalletsApi();

const opts = {
  wallet_type: new CoboWaas2.WalletType('Custodial'),
  wallet_subtype: new CoboWaas2.WalletSubtype('Asset'),
  chain_ids: 'BTC,ETH',
  limit: 10,
  before: 'RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1',
  after: 'RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk',
};

// Call the API
apiInstance.listSupportedChains(opts)
  .then((data) => {
    console.log('API called successfully. Returned data: ' + JSON.stringify(data, null, 2));
  })
  .catch((error) => {
    console.error('Error when calling WalletsApi->listSupportedChains: ' + error.response.text);
  });
```
</RequestExample>