---
openapi: get /wallets/{wallet_id}/max_transferable_value
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.max_transferable_value import MaxTransferableValue
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    token_id = "ETH_USDT"
    fee_rate = "10"
    to_address = "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE"
    from_address = "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE"

    try:
        api_response = api_instance.get_max_transferable_value(
            wallet_id, token_id, fee_rate, to_address, from_address=from_address
        )
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->get_max_transferable_value: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    WalletsApi apiInstance = new WalletsApi();
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    String tokenId = "ETH_USDT";
    String feeRate = "10";
    String toAddress = "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE";
    String fromAddress = "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE";
    
    try {
      MaxTransferableValue result = apiInstance.getMaxTransferableValue(walletId, tokenId, feeRate, toAddress, fromAddress);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#getMaxTransferableValue");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	tokenId := "ETH_USDT"
	feeRate := "10"
	toAddress := "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE"
	fromAddress := "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.WalletsAPI.GetMaxTransferableValue(ctx, walletId).
		TokenId(tokenId).
		FeeRate(feeRate).
		ToAddress(toAddress).
		FromAddress(fromAddress).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.GetMaxTransferableValue``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.GetMaxTransferableValue`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.WalletsApi();
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const token_id = "ETH_USDT";
const fee_rate = "10";
const to_address = "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE";
const opts = {
  from_address: "2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE",
};

apiInstance
  .getMaxTransferableValue(wallet_id, token_id, fee_rate, to_address, opts)
  .then(
    (data) => {
      console.log("API called successfully. Returned data: " + data);
    },
    (error) => {
      console.error(error);
    },
  );
```
</RequestExample>