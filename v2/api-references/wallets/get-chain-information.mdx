---
openapi: get /wallets/chains/{chain_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.chain_info import ChainInfo
from cobo_waas2.models.error_response import ErrorResponse
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Replace with your private key
    host="https://api.dev.cobo.com/v2",  # Use production environment URL as needed
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)
    chain_id = "ETH"  # specify the chain_id

    try:
        # Get chain information
        api_response: ChainInfo = api_instance.get_chain_by_id(chain_id)
        print("The response of WalletsApi->get_chain_by_id:\n")
        pprint(api_response)
    except ApiException as e:
        if e.status == 400:
            error: ErrorResponse = e.body
            print("Bad request: ", error.error_message)
        else:
            print(f"Exception when calling WalletsApi->get_chain_by_id: {e}\n")
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.ChainInfo;
import com.cobo.waas2.model.ErrorResponse;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        
        // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
        defaultClient.setEnv(Env.DEV);

        // Replace `<YOUR_PRIVATE_KEY>` with your private key
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        WalletsApi apiInstance = new WalletsApi();
        String chainId = "ETH"; // specify the chain_id
        try {
            ChainInfo result = apiInstance.getChainById(chainId);
            System.out.println(result);
        } catch (ApiException e) {
            if (e.getCode() == 400) {
                ErrorResponse error = e.getErrorObject(ErrorResponse.class);
                System.err.println("Bad request: " + error.getErrorMessage());
            } else {
                System.err.println("Exception when calling WalletsApi#getChainById");
                System.err.println("Status code: " + e.getCode());
                System.err.println("Reason: " + e.getResponseBody());
                System.err.println("Response headers: " + e.getResponseHeaders());
            }
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	chainId := "ETH"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsAPI.GetChainById(ctx, chainId).Execute()
	if err != nil {
		var errorResponse map[string]interface{}
		if r != nil && r.StatusCode == 400 {
			fmt.Fprintf(os.Stderr, "Bad request: %v\n", err)
		} else {
			fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.GetChainById``: %v\n", err)
		}
	} else {
		// response from `GetChainById`: ChainInfo
		fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.GetChainById`: %v\n", resp)
	}
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.WalletsApi();
const chain_id = "ETH";

apiInstance.getChainById(chain_id).then(
  (data) => {
    console.log("API called successfully. Returned data: ", data);
  },
  (error) => {
    if (error.status === 400) {
      console.error("Bad request: ", error.response.data.error_message);
    } else {
      console.error(error);
    }
  }
);
```
</RequestExample>