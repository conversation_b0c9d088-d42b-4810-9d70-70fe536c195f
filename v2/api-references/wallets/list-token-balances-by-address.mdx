---
openapi: get /wallets/{wallet_id}/addresses/{address}/tokens
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_token_balances_for_address200_response import ListTokenBalancesForAddress200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    address = "******************************************"
    token_ids = "ETH_USDT,ETH_USDC"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        api_response = api_instance.list_token_balances_for_address(
            wallet_id, address, token_ids=token_ids, limit=limit, before=before, after=after
        )
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->list_token_balances_for_address: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    WalletsApi apiInstance = new WalletsApi();
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    String address = "******************************************";
    String tokenIds = "ETH_USDT,ETH_USDC";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    
    try {
      ListTokenBalancesForAddress200Response response = apiInstance.listTokenBalancesForAddress(walletId, address, tokenIds, limit, before, after);
      System.out.println(response);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#listTokenBalancesForAddress");
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	address := "******************************************"
	tokenIds := "ETH_USDT,ETH_USDC"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsAPI.ListTokenBalancesForAddress(ctx, walletId, address).
		TokenIds(tokenIds).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.ListTokenBalancesForAddress``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
		return
	}

	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.ListTokenBalancesForAddress`: %v\n", resp)
}
```
```javascript JavaScript
const fetch = require('node-fetch');

const YOUR_PRIVATE_KEY = '<YOUR_PRIVATE_KEY>';
const BASE_URL = 'https://api.dev.cobo.com/v2';
// Specify the wallet ID and address
const walletId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
const address = '******************************************';

// Set query parameters
const queryParams = new URLSearchParams({
  token_ids: 'ETH_USDT,ETH_USDC',
  limit: '10',
  before: 'RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1',
  after: 'RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk'
}).toString();

// Make the request
fetch(`${BASE_URL}/wallets/${walletId}/addresses/${address}/tokens?${queryParams}`, {
  method: 'GET',
  headers: {
    Authorization: `Bearer ${YOUR_PRIVATE_KEY}`,
    Accept: 'application/json'
  }
})
  .then(response => {
    if (!response.ok) {
      throw new Error(`Error: ${response.statusText}`);
    }
    return response.json();
  })
  .then(data => console.log('Token balances:', data))
  .catch(error => console.error('Fetch error:', error));
```
</RequestExample>