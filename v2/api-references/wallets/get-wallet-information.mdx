---
openapi: get /wallets/{wallet_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.wallet_info import WalletInfo
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure API client
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment.
    # To use the production environment, change to 'https://api.cobo.com/v2'.
    host="https://api.dev.cobo.com/v2",
)

# Use context manager for API client to ensure resources are released properly
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.WalletsApi(api_client)
  
    # UUID of the wallet to retrieve
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"

    try:
        # Get wallet information
        api_response = api_instance.get_wallet_by_id(wallet_id)
        print("The response of WalletsApi->get_wallet_by_id:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->get_wallet_by_id: %s\n" % e)
```
```java Java
// Import necessary classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

// Define the Example class
public class Example {
  // Main method where the execution starts
  public static void main(String[] args) {
    // Initialize the API client
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Set the environment to development. For production use, set Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key for authentication
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    // Initialize the Wallets API instance
    WalletsApi apiInstance = new WalletsApi();

    // Prepare the wallet ID parameter
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");

    // Try to get the wallet information using the API
    try {
      WalletInfo result = apiInstance.getWalletById(walletId);
      System.out.println(result);
    } catch (ApiException e) {
      // Handle exceptions that occur during the API call
      System.err.println("Exception when calling WalletsApi#getWalletById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Wallet ID to be queried
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"

	// Create a configuration for the API client
	configuration := coboWaas2.NewConfiguration()

	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Set environment to development. Change to coboWaas2.ProdEnv for production use.
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)

	// Set private key for authentication. Replace `<YOUR_PRIVATE_KEY>` with your actual private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	// Call the API to get wallet information
	resp, r, err := apiClient.WalletsAPI.GetWalletById(ctx, walletId).Execute()
	if err != nil {
		// Handle errors during the API call
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.GetWalletById``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}

	// Output the wallet information
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.GetWalletById`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;

// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);

// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Prepare the parameters
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

// Call the API
const apiInstance = new CoboWaas2.WalletsApi();

apiInstance.getWalletById(wallet_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + JSON.stringify(data));
  },
  (error) => {
    console.error("Error calling API:", error);
  }
);
```
</RequestExample>