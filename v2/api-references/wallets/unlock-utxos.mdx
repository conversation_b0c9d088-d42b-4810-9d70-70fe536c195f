---
openapi: post /wallets/{wallet_id}/utxos/unlock
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.lock_utxos201_response import LockUtxos201Response
from cobo_waas2.models.lock_utxos_request import LockUtxosRequest
from cobo_waas2.models.lock_utxos_request_utxos_inner import LockUtxosRequestUtxosInner
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.WalletsApi(api_client)
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"

    # Prepare UTXO data
    utxo = LockUtxosRequestUtxosInner(
        token_id="BTC", 
        tx_hash="9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb", 
        vout_n=0
    )

    lock_utxos_request = LockUtxosRequest(
        utxos=[utxo]
    )

    try:
        # Unlock UTXOs
        api_response = api_instance.unlock_utxos(
            wallet_id, lock_utxos_request=lock_utxos_request
        )
        print("The response of WalletsApi->unlock_utxos:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling WalletsApi->unlock_utxos: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsApi apiInstance = new WalletsApi();
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");

    // Create UTXO objects
    LockUtxosRequestUtxos utxo = new LockUtxosRequestUtxos();
    utxo.setTokenId("BTC");
    utxo.setTxHash("9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb");
    utxo.setVoutN(0);

    List<LockUtxosRequestUtxos> utxoList = new ArrayList<>();
    utxoList.add(utxo);

    LockUtxosRequest lockUtxosRequest = new LockUtxosRequest();
    lockUtxosRequest.setUtxos(utxoList);

    try {
      LockUtxos201Response result = apiInstance.unlockUtxos(walletId, lockUtxosRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#unlockUtxos");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	utxos := coboWaas2.NewLockUtxosRequestUtxosInner("BTC", "9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb", int32(0))

	lockUtxosRequest := *coboWaas2.NewLockUtxosRequest([]coboWaas2.LockUtxosRequestUtxosInner{*utxos})

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsAPI.UnlockUtxos(ctx, walletId).
		LockUtxosRequest(lockUtxosRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.UnlockUtxos``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `UnlockUtxos`: LockUtxos201Response
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.UnlockUtxos`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Call the API
const apiInstance = new CoboWaas2.WalletsApi();
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  LockUtxosRequest: new CoboWaas2.LockUtxosRequest({
    utxos: [
      {
        token_id: 'BTC',
        tx_hash: '9bdf8e7ae03c237e115f09543fbdb40f8efa600106e78b67ce4d5adfadda2dbb',
        vout_n: 0
      }
    ]
  })
};

apiInstance.unlockUtxos(wallet_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>