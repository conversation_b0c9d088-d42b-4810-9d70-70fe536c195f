---
openapi: get /wallets
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models import WalletType, WalletSubtype
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Use a context to ensure the API client is cleaned up.
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)

    # Parameters
    wallet_type = WalletType.CUSTODIAL  # WalletType enums
    wallet_subtype = WalletSubtype.ASSET # WalletSubtype enums
    project_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        # Call the API and print the response
        api_response = api_instance.list_wallets(wallet_type, wallet_subtype, project_id, vault_id, limit, before, after)
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->list_wallets: %s\n" % e)
```
```java Java
// Import necessary classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV); // Set the environment
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>"); // Set your private key

    WalletsApi apiInstance = new WalletsApi();
    WalletType walletType = WalletType.fromValue("Custodial");
    WalletSubtype walletSubtype = WalletSubtype.fromValue("Asset");
    UUID projectId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    
    try {
      ListWallets200Response result = apiInstance.listWallets(walletType, walletSubtype, projectId, vaultId, limit, before, after);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#listWallets");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletType := coboWaas2.WalletType("Custodial")
	walletSubtype := coboWaas2.WalletSubtype("Asset")
	projectId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsAPI.ListWallets(ctx).
		WalletType(walletType).
		WalletSubtype(walletSubtype).
		ProjectId(projectId).
		VaultId(vaultId).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.ListWallets``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.ListWallets`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV); // Set environment
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Set your private key

const apiInstance = new CoboWaas2.WalletsApi();
const opts = {
  wallet_type: CoboWaas2.WalletType.fromValue("Custodial"),
  wallet_subtype: CoboWaas2.WalletSubtype.fromValue("Asset"),
  project_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  vault_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
};

apiInstance.listWallets(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>