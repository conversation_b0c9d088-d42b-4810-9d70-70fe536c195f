---
openapi: get /wallets/check_address_chains_validity
---

<RequestExample>
```python Python

import cobo_waas2
from cobo_waas2.models.check_address_chains_validity200_response_inner import (
    CheckAddressChainsValidity200ResponseInner,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsApi(api_client)
    address = "******************************************"
    chain_ids = "BTC,ETH"

    try:
        api_response = api_instance.check_address_chains_validity(address, chain_ids)
        print("The response of WalletsApi->check_address_chains_validity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling WalletsApi->check_address_chains_validity: %s\n" % e)

```
```java Java

import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.*;
import java.util.List;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);

    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsApi apiInstance = new WalletsApi();
    String address = "******************************************";
    String chainIds = "BTC,ETH";
    try {
      List<CheckAddressChainsValidity200ResponseInner> result =
          apiInstance.checkAddressChainsValidity(address, chainIds);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsApi#checkAddressChainsValidity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go

package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	address := "******************************************"
	chainIds := "BTC,ETH"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsAPI.CheckAddressChainsValidity(ctx).
		Address(address).
		ChainIds(chainIds).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.CheckAddressChainsValidity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.CheckAddressChainsValidity`: %v\n", resp)
}

```
```javascript JavaScript

const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.WalletsApi();
const address = "******************************************";
const chain_ids = "BTC,ETH";
apiInstance.checkAddressChainsValidity(address, chain_ids).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>