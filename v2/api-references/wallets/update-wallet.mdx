---
openapi: put /wallets/{wallet_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.update_wallet_params import UpdateCustodialWalletParams, UpdateWalletParams
from cobo_waas2.models.wallet_info import CustodialWalletInfo
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.WalletsApi(api_client)
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"

    # Define the parameters for updating a custodial wallet
    update_wallet_params = UpdateCustodialWalletParams(
        wallet_type="Custodial",
        # Add other necessary parameters here
    )

    try:
        # Update wallet
        api_response = api_instance.update_wallet_by_id(
            wallet_id, update_wallet_params=update_wallet_params
        )
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsApi->update_wallet_by_id: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsApi;
import com.cobo.waas2.model.UpdateCustodialWalletParams;
import com.cobo.waas2.model.WalletInfo;

import java.util.UUID;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        defaultClient.setEnv(Env.DEV);
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        WalletsApi apiInstance = new WalletsApi();
        UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
        UpdateCustodialWalletParams updateWalletParams = new UpdateCustodialWalletParams()
                .walletType("Custodial");
                // Add other necessary parameters here
        try {
            WalletInfo result = apiInstance.updateWalletById(walletId, updateWalletParams);
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling WalletsApi#updateWalletById");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
    "context"
    "fmt"
    coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
    "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
    "os"
)

func main() {
    walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    updateWalletParams := coboWaas2.UpdateCustodialWalletParams{
        WalletType: "Custodial",
        // Add other necessary parameters here
    }

    configuration := coboWaas2.NewConfiguration()
    apiClient := coboWaas2.NewAPIClient(configuration)
    ctx := context.Background()

    ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
    ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
        Secret: "<YOUR_PRIVATE_KEY>",
    })
    resp, r, err := apiClient.WalletsAPI.UpdateWalletById(ctx, walletId).
        UpdateWalletParams(updateWalletParams).
        Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `WalletsAPI.UpdateWalletById``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    fmt.Fprintf(os.Stdout, "Response from `WalletsAPI.UpdateWalletById`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.WalletsApi();
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

const opts = {
  UpdateWalletParams: new CoboWaas2.UpdateCustodialWalletParams({
    wallet_type: "Custodial",
    // Add other necessary parameters here
  }),
};

apiInstance.updateWalletById(wallet_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: ", data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>