---
openapi: get /address_books
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2"
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.AddressBooksApi(api_client)
    
    chain_id = "ETH"
    address = "******************************************"
    label = "test"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        api_response = api_instance.list_address_books(
            chain_id,
            address=address,
            label=label,
            limit=limit,
            before=before,
            after=after
        )
        pprint(api_response)
    except Exception as e:
        print("Exception when calling AddressBooksApi->list_address_books: %s\n" % e)

```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.AddressBooksApi;
import com.cobo.waas2.ApiException;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        defaultClient.setEnv(Env.DEV);
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        
        AddressBooksApi apiInstance = new AddressBooksApi();
        
        String chainId = "ETH";
        String address = "******************************************";
        String label = "test";
        Integer limit = 10;
        String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
        String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
        
        try {
            ListAddressBooks200Response result = apiInstance.listAddressBooks(chainId, address, label, limit, before, after);
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling AddressBooksApi#listAddressBooks");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	chainId := "ETH"
	address := "******************************************"
	label := "test"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.AddressBooksAPI.ListAddressBooks(ctx).
		ChainId(chainId).
		Address(address).
		Label(label).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `AddressBooksAPI.ListAddressBooks``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `AddressBooksAPI.ListAddressBooks`: %v\n", resp)
}

```
```javascript JavaScript
import CoboWaas2 from "@cobo/cobo-waas2";

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Create API instance
const apiInstance = new CoboWaas2.AddressBooksApi();

// Define parameters
const chainId = "ETH";
const opts = {
  address: "******************************************",
  label: "test",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
};

// Call the API
apiInstance.listAddressBooks(chainId, opts)
  .then(data => {
    console.log("API called successfully. Returned data: ", data);
  })
  .catch(error => {
    console.error("Error calling API: ", error);
  });

```
</RequestExample>