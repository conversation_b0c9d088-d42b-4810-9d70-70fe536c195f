---
openapi: post /travel_rule/transaction/deposit/travel_rule_info
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.submit_deposit_travel_rule_info201_response import SubmitDepositTravelRuleInfo201Response
from cobo_waas2.models.travel_rule_deposit_request import TravelRuleDepositRequest
from cobo_waas2.models.self_custody_wallet import SelfCustodyWallet
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure the client
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>", # Replace with your private key
    host="https://api.dev.cobo.com/v2"
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.TravelRuleApi(api_client)
    travel_rule_deposit_request = TravelRuleDepositRequest(
        transaction_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        # Choose either TravelRuleDepositExchangesOrVASP or SelfCustodyWallet
        # Example with SelfCustodyWallet
        travel_rule_info=SelfCustodyWallet(
            destination_wallet_type="SELF_CUSTODY_WALLET",
            self_custody_wallet_challenge="challenge_token_abc123",
            self_custody_wallet_address="******************************************",
            self_custody_wallet_sign="0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c"
        )
    )

    try:
        api_response = api_instance.submit_deposit_travel_rule_info(travel_rule_deposit_request=travel_rule_deposit_request)
        print("The response of TravelRuleApi->submit_deposit_travel_rule_info:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling TravelRuleApi->submit_deposit_travel_rule_info: %s\n" % e)
```
```java Java
// Import necessary classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TravelRuleApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV); // Use the development environment
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

    TravelRuleApi apiInstance = new TravelRuleApi();
    TravelRuleDepositRequest travelRuleDepositRequest = new TravelRuleDepositRequest();
    travelRuleDepositRequest.setTransactionId("f47ac10b-58cc-4372-a567-0e02b2c3d479");

    // Choose either TravelRuleDepositExchangesOrVASP or SelfCustodyWallet
    // Example with SelfCustodyWallet
    SelfCustodyWallet selfCustodyWallet = new SelfCustodyWallet()
      .destinationWalletType("SELF_CUSTODY_WALLET")
      .selfCustodyWalletChallenge("challenge_token_abc123")
      .selfCustodyWalletAddress("******************************************")
      .selfCustodyWalletSign("0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c");
    travelRuleDepositRequest.setTravelRuleInfo(selfCustodyWallet);

    try {
      SubmitDepositTravelRuleInfo201Response result = apiInstance.submitDepositTravelRuleInfo(travelRuleDepositRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TravelRuleApi#submitDepositTravelRuleInfo");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Initialize the request model
	travelRuleDepositRequest := coboWaas2.TravelRuleDepositRequest{
		TransactionId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		// Choose either TravelRuleDepositExchangesOrVASP or SelfCustodyWallet
		// Example with SelfCustodyWallet
		TravelRuleInfo: coboWaas2.SelfCustodyWallet{
			DestinationWalletType:       coboWaas2.DestinationWalletType("SELF_CUSTODY_WALLET"),
			SelfCustodyWalletChallenge:  "challenge_token_abc123",
			SelfCustodyWalletAddress:    "******************************************",
			SelfCustodyWalletSign:       "0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c",
		},
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Use the development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TravelRuleAPI.SubmitDepositTravelRuleInfo(ctx).
		TravelRuleDepositRequest(travelRuleDepositRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TravelRuleAPI.SubmitDepositTravelRuleInfo``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `TravelRuleAPI.SubmitDepositTravelRuleInfo`: %v\n", resp)
}
```
```javascript JavaScript
// Import necessary classes
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV); // Use the development environment
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

// Create an instance of the API class
const apiInstance = new CoboWaas2.TravelRuleApi();

// Initialize the request body
const travelRuleDepositRequest = new CoboWaas2.TravelRuleDepositRequest();
travelRuleDepositRequest.transaction_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

// Choose either TravelRuleDepositExchangesOrVASP or SelfCustodyWallet
// For example, using SelfCustodyWallet
const selfCustodyWallet = new CoboWaas2.SelfCustodyWallet();
selfCustodyWallet.destination_wallet_type = "SELF_CUSTODY_WALLET";
selfCustodyWallet.self_custody_wallet_challenge = "challenge_token_abc123";
selfCustodyWallet.self_custody_wallet_address = "******************************************";
selfCustodyWallet.self_custody_wallet_sign = "0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c";
travelRuleDepositRequest.travel_rule_info = selfCustodyWallet;

// Call the API
apiInstance.submitDepositTravelRuleInfo({TravelRuleDepositRequest: travelRuleDepositRequest}).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>