---
openapi: get /travel_rule/transaction/countries
---

<RequestExample>
```python Python
import example_api_client
from example_api_client.rest import ApiException
from pprint import pprint

# Configuration
configuration = example_api_client.Configuration(
    host="https://api.dev.cobo.com/v2",
    api_key={'YOUR_API_KEY': 'Bearer YOUR_ACCESS_TOKEN'}
)

# Enter a context with an instance of the API client
with example_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = example_api_client.TravelRuleApi(api_client)

    try:
        # List supported countries
        api_response = api_instance.list_supported_countries()
        print("The response of TravelRuleApi->list_supported_countries:\n")
        pprint(api_response)
    except ApiException as e:
        print(
            "Exception when calling TravelRuleApi->list_supported_countries: %s\n" % e
        )
```
```java Java
// Import classes:
import com.example.api.client.ApiClient;
import com.example.api.client.ApiException;
import com.example.api.client.Configuration;
import com.example.api.client.auth.OAuth;
import com.example.api.api.TravelRuleApi;
import com.example.api.model.*;

import java.util.List;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. Replace `Env.DEV` with the production environment if needed
    defaultClient.setBasePath("https://api.dev.cobo.com/v2");

    // Set authentication
    OAuth oauth2 = (OAuth) defaultClient.getAuthentication("OAuth2");
    oauth2.setAccessToken("YOUR_ACCESS_TOKEN");

    TravelRuleApi apiInstance = new TravelRuleApi(defaultClient);
    try {
      List<SupportedCountry> result = apiInstance.listSupportedCountries();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TravelRuleApi#listSupportedCountries");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	example_api_client "github.com/example/api-client-go"
)

func main() {
	configuration := example_api_client.NewConfiguration()
	configuration.BasePath = "https://api.dev.cobo.com/v2"

	// API client initialization
	apiClient := example_api_client.NewAPIClient(configuration)
	ctx := context.WithValue(context.Background(), example_api_client.ContextAPIKey, example_api_client.APIKey{
		Key:    "Bearer YOUR_ACCESS_TOKEN",
		Prefix: "Bearer",
	})

	// Call the API
	resp, r, err := apiClient.TravelRuleAPI.ListSupportedCountries(ctx).Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TravelRuleAPI.ListSupportedCountries`: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
		return
	}
	fmt.Printf("Response from `TravelRuleAPI.ListSupportedCountries`: %v\n", resp)
}
```
```javascript JavaScript
const ExampleApiClient = require('example_api_client');

const apiClient = ExampleApiClient.ApiClient.instance;
// Set the base path and authentication
apiClient.basePath = "https://api.dev.cobo.com/v2";
apiClient.authentications['OAuth2'].accessToken = "YOUR_ACCESS_TOKEN";

const apiInstance = new ExampleApiClient.TravelRuleApi();

apiInstance.listSupportedCountries().then(
  (data) => {
    console.log("API called successfully. Returned data: ", data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>