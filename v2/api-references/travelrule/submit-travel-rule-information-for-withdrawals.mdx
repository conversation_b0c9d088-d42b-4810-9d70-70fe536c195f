---
openapi: post /travel_rule/transaction/withdraw/travel_rule_info
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.travel_rule_withdraw_request import TravelRuleWithdrawRequest
from cobo_waas2.models.self_custody_wallet import SelfCustodyWallet
from cobo_waas2.models.destination_wallet_type import DestinationWalletType
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.TravelRuleApi(api_client)
    travel_rule_withdraw_request = TravelRuleWithdrawRequest(
        transaction_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        travel_rule_info={
            "self_custody_wallet": SelfCustodyWallet(
                wallet_type=DestinationWalletType.EXCHANGES_OR_VASP,
                challenge_token="challenge_token_abc123",
                address="******************************************",
                signature="0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c"
            )
        }
    )

    try:
        # Submit Travel Rule information for withdrawals
        api_response = api_instance.submit_withdraw_travel_rule_info(
            travel_rule_withdraw_request=travel_rule_withdraw_request
        )
        print("The response of TravelRuleApi->submit_withdraw_travel_rule_info:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling TravelRuleApi->submit_withdraw_travel_rule_info: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TravelRuleApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    TravelRuleApi apiInstance = new TravelRuleApi();
    TravelRuleWithdrawRequest travelRuleWithdrawRequest = new TravelRuleWithdrawRequest();
    SelfCustodyWallet selfCustodyWallet = new SelfCustodyWallet()
      .walletType(DestinationWalletType.EXCHANGES_OR_VASP)
      .challengeToken("challenge_token_abc123")
      .address("******************************************")
      .signature("0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c");
    TravelRuleInfo travelRuleInfo = new TravelRuleInfo()
      .selfCustodyWallet(selfCustodyWallet);
    travelRuleWithdrawRequest.setTransactionId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    travelRuleWithdrawRequest.setTravelRuleInfo(travelRuleInfo);

    try {
      SubmitDepositTravelRuleInfo201Response result =
          apiInstance.submitWithdrawTravelRuleInfo(travelRuleWithdrawRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TravelRuleApi#submitWithdrawTravelRuleInfo");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	travelRuleWithdrawRequest := coboWaas2.TravelRuleWithdrawRequest{
		TransactionId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		TravelRuleInfo: coboWaas2.TravelRuleWithdrawRequest_travel_rule_info{
			SelfCustodyWallet: &coboWaas2.SelfCustodyWallet{
				WalletType:     coboWaas2.DestinationWalletType_EXCHANGES_OR_VASP,
				ChallengeToken: "challenge_token_abc123",
				Address:        "******************************************",
				Signature:      "0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c",
			},
		},
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TravelRuleAPI.SubmitWithdrawTravelRuleInfo(ctx).
		TravelRuleWithdrawRequest(travelRuleWithdrawRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `TravelRuleAPI.SubmitWithdrawTravelRuleInfo`: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `TravelRuleAPI.SubmitWithdrawTravelRuleInfo`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.TravelRuleApi();
const opts = {
  TravelRuleWithdrawRequest: new CoboWaas2.TravelRuleWithdrawRequest({
    transaction_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
    travel_rule_info: {
      self_custody_wallet: new CoboWaas2.SelfCustodyWallet({
        wallet_type: CoboWaas2.DestinationWalletType.EXCHANGES_OR_VASP,
        challenge_token: "challenge_token_abc123",
        address: "******************************************",
        signature: "0xf0a0ca69dd3afc57235c72aba3ff1f1144ee5409aeec013a9b17cdb58d0185a66a525945bfbd66e87bf0503eb0b83bf90cb973a8cbb730d19dc032e00dfe393a1c"
      })
    }
  })
};
apiInstance.submitWithdrawTravelRuleInfo(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>