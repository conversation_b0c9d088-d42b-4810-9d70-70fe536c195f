---
openapi: get /swaps/activities
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = ""
    direction = ""

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).Status(status).MinUpdatedTimestamp(minUpdatedTimestamp).MaxUpdatedTimestamp(maxUpdatedTimestamp).Initiator(initiator).Limit(limit).Before(before).After(after).SortBy(sortBy).Direction(direction).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = ""
    direction = ""

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = ""
    direction = ""

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = ""
    direction = ""

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = ""
    direction = ""

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = ""
    direction = ""

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    
    # Parameters
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = "timestamp"
    direction = "ASC"

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    
    // Parameters
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "timestamp";
    String direction = "ASC";

    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Parameters
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	
	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();

const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};

apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import ListSwapActivities200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2"
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = "timestamp"
    direction = "ASC"

    try:
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction
        )
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "timestamp";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result = apiInstance.listSwapActivities(
          status, minUpdatedTimestamp, maxUpdatedTimestamp, initiator, limit, before, after, sortBy, direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC"
};

apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models import ListSwapActivities200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = "timestamp"
    direction = "ASC"

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "timestamp";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Create an API instance
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};

apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data:", data);
  },
  (error) => {
    console.error("Error:", error);
  }
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_swap_activities200_response import (
    ListSwapActivities200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    status = "Success"
    min_updated_timestamp = 1635744000000
    max_updated_timestamp = 1635744000000
    initiator = "<EMAIL>"
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
    sort_by = "timestamp"
    direction = "ASC"

    try:
        # List Swap Activities
        api_response = api_instance.list_swap_activities(
            status=status,
            min_updated_timestamp=min_updated_timestamp,
            max_updated_timestamp=max_updated_timestamp,
            initiator=initiator,
            limit=limit,
            before=before,
            after=after,
            sort_by=sort_by,
            direction=direction,
        )
        print("The response of SwapsApi->list_swap_activities:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->list_swap_activities: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    String status = "Success";
    Long minUpdatedTimestamp = 1635744000000L;
    Long maxUpdatedTimestamp = 1635744000000L;
    String initiator = "<EMAIL>";
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    String sortBy = "timestamp";
    String direction = "ASC";
    try {
      ListSwapActivities200Response result =
          apiInstance.listSwapActivities(
              status,
              minUpdatedTimestamp,
              maxUpdatedTimestamp,
              initiator,
              limit,
              before,
              after,
              sortBy,
              direction);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#listSwapActivities");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Success"
	minUpdatedTimestamp := int64(1635744000000)
	maxUpdatedTimestamp := int64(1635744000000)
	initiator := "<EMAIL>"
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
	sortBy := "timestamp"
	direction := "ASC"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.ListSwapActivities(ctx).
		Status(status).
		MinUpdatedTimestamp(minUpdatedTimestamp).
		MaxUpdatedTimestamp(maxUpdatedTimestamp).
		Initiator(initiator).
		Limit(limit).
		Before(before).
		After(after).
		SortBy(sortBy).
		Direction(direction).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.ListSwapActivities``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListSwapActivities`: ListSwapActivities200Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.ListSwapActivities`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const opts = {
  status: "Success",
  min_updated_timestamp: 1635744000000,
  max_updated_timestamp: 1635744000000,
  initiator: "<EMAIL>",
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
  sort_by: "timestamp",
  direction: "ASC",
};
apiInstance.listSwapActivities(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>