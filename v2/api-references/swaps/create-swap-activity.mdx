---
openapi: post /swaps/swap
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := *coboWaas2.NewCreateSwapActivityRequest("123e4567-e89b-12d3-a456-426614174000", "123e4567-e89b-12d3-a456-426614174001", "0.005")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).CreateSwapActivityRequest(createSwapActivityRequest).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
apiInstance.createSwapActivity(CreateSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := *coboWaas2.NewCreateSwapActivityRequest("123e4567-e89b-12d3-a456-426614174000", "123e4567-e89b-12d3-a456-426614174001", "0.005")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
apiInstance.createSwapActivity(CreateSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := *coboWaas2.NewCreateSwapActivityRequest("123e4567-e89b-12d3-a456-426614174000", "123e4567-e89b-12d3-a456-426614174001", "0.005")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
apiInstance.createSwapActivity(CreateSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := *coboWaas2.NewCreateSwapActivityRequest("123e4567-e89b-12d3-a456-426614174000", "123e4567-e89b-12d3-a456-426614174001", "0.005")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
apiInstance.createSwapActivity(CreateSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := *coboWaas2.NewCreateSwapActivityRequest("123e4567-e89b-12d3-a456-426614174000", "123e4567-e89b-12d3-a456-426614174001", "0.005")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
apiInstance.createSwapActivity(CreateSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := *coboWaas2.NewCreateSwapActivityRequest("123e4567-e89b-12d3-a456-426614174000", "123e4567-e89b-12d3-a456-426614174001", "0.005")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
apiInstance.createSwapActivity(CreateSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python

```
```java Java

```
```go Go

```
```javascript JavaScript

```
</RequestExample>

<RequestExample>
```python Python

```
```java Java

```
```go Go

```
```javascript JavaScript

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure API key authorization
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    # Populate with necessary fields
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling SwapsApi->create_swap_activity: {}\n".format(e))
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.CreateSwapActivityRequest;
import com.cobo.waas2.model.SwapActivity;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    // populate with necessary fields
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Populate with necessary fields
	createSwapActivityRequest := coboWaas2.CreateSwapActivityRequest{}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.SwapsApi();
// Prepare the request body based on your API specification
const createSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest({
  // populate with necessary fields
});

apiInstance.createSwapActivity(createSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error("Error calling API: ", error);
  }
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure API key authorization
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    # Populate with necessary fields
    create_swap_activity_request = cobo_waas2.CreateSwapActivityRequest()

    try:
        # Create Swap Activity
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling SwapsApi->create_swap_activity: {}\n".format(e))
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.CreateSwapActivityRequest;
import com.cobo.waas2.model.SwapActivity;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    // populate with necessary fields
    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Populate with necessary fields
	createSwapActivityRequest := coboWaas2.CreateSwapActivityRequest{}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapActivity`: SwapActivity
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.SwapsApi();
// Prepare the request body based on your API specification
const createSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest({
  // populate with necessary fields
});

apiInstance.createSwapActivity(createSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error("Error calling API: ", error);
  }
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = CreateSwapActivityRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        quote_id="123e4567-e89b-12d3-a456-426614174001",
        slippage_tolerance="0.005",
        app_initiator="<EMAIL>"
    )

    try:
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.CreateSwapActivityRequest;
import com.cobo.waas2.model.SwapActivity;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    SwapsApi apiInstance = new SwapsApi();
    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    createSwapActivityRequest.setWalletId("123e4567-e89b-12d3-a456-426614174000");
    createSwapActivityRequest.setQuoteId("123e4567-e89b-12d3-a456-426614174001");
    createSwapActivityRequest.setSlippageTolerance("0.005");
    createSwapActivityRequest.setAppInitiator("<EMAIL>");

    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := coboWaas2.CreateSwapActivityRequest{
		WalletId:         "123e4567-e89b-12d3-a456-426614174000",
		QuoteId:          "123e4567-e89b-12d3-a456-426614174001",
		SlippageTolerance: "0.005",
		AppInitiator:     "<EMAIL>",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.SwapsApi();
const createSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest();
createSwapActivityRequest.walletId = "123e4567-e89b-12d3-a456-426614174000";
createSwapActivityRequest.quoteId = "123e4567-e89b-12d3-a456-426614174001";
createSwapActivityRequest.slippageTolerance = "0.005";
createSwapActivityRequest.appInitiator = "<EMAIL>";

apiInstance.createSwapActivity(createSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_activity_request import CreateSwapActivityRequest
from cobo_waas2.models.swap_activity import SwapActivity
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_activity_request = CreateSwapActivityRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        quote_id="123e4567-e89b-12d3-a456-426614174001",
        slippage_tolerance="0.005",
        app_initiator="<EMAIL>"
    )
    
    try:
        api_response = api_instance.create_swap_activity(create_swap_activity_request)
        print("The response of SwapsApi->create_swap_activity:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling SwapsApi->create_swap_activity: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.CreateSwapActivityRequest;
import com.cobo.waas2.model.SwapActivity;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();

    CreateSwapActivityRequest createSwapActivityRequest = new CreateSwapActivityRequest();
    createSwapActivityRequest.setWalletId("123e4567-e89b-12d3-a456-426614174000");
    createSwapActivityRequest.setQuoteId("123e4567-e89b-12d3-a456-426614174001");
    createSwapActivityRequest.setSlippageTolerance("0.005");
    createSwapActivityRequest.setAppInitiator("<EMAIL>");

    try {
      SwapActivity result = apiInstance.createSwapActivity(createSwapActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapActivityRequest := coboWaas2.CreateSwapActivityRequest{
		WalletId: "123e4567-e89b-12d3-a456-426614174000",
		QuoteId: "123e4567-e89b-12d3-a456-426614174001",
		SlippageTolerance: "0.005",
		AppInitiator: "<EMAIL>",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.SwapsAPI.CreateSwapActivity(ctx).
		CreateSwapActivityRequest(createSwapActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.SwapsApi();
const createSwapActivityRequest = new CoboWaas2.CreateSwapActivityRequest({
    wallet_id: "123e4567-e89b-12d3-a456-426614174000",
    quote_id: "123e4567-e89b-12d3-a456-426614174001",
    slippage_tolerance: "0.005",
    app_initiator: "<EMAIL>"
});

apiInstance.createSwapActivity(createSwapActivityRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>