---
openapi: post /swaps/quote
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest()

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest("123e4567-e89b-12d3-a456-426614174000", "BTC", "ETH_WBTC")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).CreateSwapQuoteRequest(createSwapQuoteRequest).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest();
apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest()

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest("123e4567-e89b-12d3-a456-426614174000", "BTC", "ETH_WBTC")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest();
apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest()

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest("123e4567-e89b-12d3-a456-426614174000", "BTC", "ETH_WBTC")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest();
apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest()

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest("123e4567-e89b-12d3-a456-426614174000", "BTC", "ETH_WBTC")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest();
apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest()

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest("123e4567-e89b-12d3-a456-426614174000", "BTC", "ETH_WBTC")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest();
apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest()

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest("123e4567-e89b-12d3-a456-426614174000", "BTC", "ETH_WBTC")

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}

```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest();
apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    
    # Create the request object
    create_swap_quote_request = CreateSwapQuoteRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        pay_token_id="BTC",
        receive_token_id="ETH_WBTC",
        pay_amount="100"  # Or use receive_amount="100"
    )

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    createSwapQuoteRequest.setWalletId("123e4567-e89b-12d3-a456-426614174000");
    createSwapQuoteRequest.setPayTokenId("BTC");
    createSwapQuoteRequest.setReceiveTokenId("ETH_WBTC");
    createSwapQuoteRequest.setPayAmount("100"); // Or use setReceiveAmount("100")

    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := coboWaas2.NewCreateSwapQuoteRequest()
	createSwapQuoteRequest.WalletId = "123e4567-e89b-12d3-a456-426614174000"
	createSwapQuoteRequest.PayTokenId = "BTC"
	createSwapQuoteRequest.ReceiveTokenId = "ETH_WBTC"
	createSwapQuoteRequest.PayAmount = "100" // Or use ReceiveAmount = "100"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(*createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const createSwapQuoteRequest = {
  wallet_id: "123e4567-e89b-12d3-a456-426614174000",
  pay_token_id: "BTC",
  receive_token_id: "ETH_WBTC",
  pay_amount: "100"  // Or use receive_amount: "100"
};
apiInstance.createSwapQuote(createSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + JSON.stringify(data));
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    
    # Create the request object
    create_swap_quote_request = CreateSwapQuoteRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        pay_token_id="BTC",
        receive_token_id="ETH_WBTC",
        pay_amount="100"  # Or use receive_amount="100"
    )

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest();
    createSwapQuoteRequest.setWalletId("123e4567-e89b-12d3-a456-426614174000");
    createSwapQuoteRequest.setPayTokenId("BTC");
    createSwapQuoteRequest.setReceiveTokenId("ETH_WBTC");
    createSwapQuoteRequest.setPayAmount("100"); // Or use setReceiveAmount("100")

    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createSwapQuoteRequest := coboWaas2.NewCreateSwapQuoteRequest()
	createSwapQuoteRequest.WalletId = "123e4567-e89b-12d3-a456-426614174000"
	createSwapQuoteRequest.PayTokenId = "BTC"
	createSwapQuoteRequest.ReceiveTokenId = "ETH_WBTC"
	createSwapQuoteRequest.PayAmount = "100" // Or use ReceiveAmount = "100"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(*createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const createSwapQuoteRequest = {
  wallet_id: "123e4567-e89b-12d3-a456-426614174000",
  pay_token_id: "BTC",
  receive_token_id: "ETH_WBTC",
  pay_amount: "100"  // Or use receive_amount: "100"
};
apiInstance.createSwapQuote(createSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + JSON.stringify(data));
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    # Define the request payload
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        pay_token_id="BTC",
        receive_token_id="ETH_WBTC",
        # Only one of pay_amount or receive_amount needs to be set
        pay_amount="100" 
    )

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    // Define the request payload
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest()
      .walletId("123e4567-e89b-12d3-a456-426614174000")
      .payTokenId("BTC")
      .receiveTokenId("ETH_WBTC")
      .payAmount("100"); // Only one of payAmount or receiveAmount needs to be set

    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Define the request payload
	createSwapQuoteRequest := &coboWaas2.CreateSwapQuoteRequest{
		WalletId:      "123e4567-e89b-12d3-a456-426614174000",
		PayTokenId:    "BTC",
		ReceiveTokenId: "ETH_WBTC",
		PayAmount:     "100",  // Only one of PayAmount or ReceiveAmount needs to be set
	}

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(*createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest({
  wallet_id: "123e4567-e89b-12d3-a456-426614174000",
  pay_token_id: "BTC",
  receive_token_id: "ETH_WBTC",
  pay_amount: "100"  // Only one of pay_amount or receive_amount needs to be set
});

apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi(api_client)
    # Define the request payload
    create_swap_quote_request = cobo_waas2.CreateSwapQuoteRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        pay_token_id="BTC",
        receive_token_id="ETH_WBTC",
        # Only one of pay_amount or receive_amount needs to be set
        pay_amount="100" 
    )

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    // Define the request payload
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest()
      .walletId("123e4567-e89b-12d3-a456-426614174000")
      .payTokenId("BTC")
      .receiveTokenId("ETH_WBTC")
      .payAmount("100"); // Only one of payAmount or receiveAmount needs to be set

    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Define the request payload
	createSwapQuoteRequest := &coboWaas2.CreateSwapQuoteRequest{
		WalletId:      "123e4567-e89b-12d3-a456-426614174000",
		PayTokenId:    "BTC",
		ReceiveTokenId: "ETH_WBTC",
		PayAmount:     "100",  // Only one of PayAmount or ReceiveAmount needs to be set
	}

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(*createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
const CreateSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest({
  wallet_id: "123e4567-e89b-12d3-a456-426614174000",
  pay_token_id: "BTC",
  receive_token_id: "ETH_WBTC",
  pay_amount: "100"  // Only one of pay_amount or receive_amount needs to be set
});

apiInstance.createSwapQuote(CreateSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Replace with your private key
    host="https://api.dev.cobo.com/v2"  # Use development environment
)

# API client context
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.SwapsApi(api_client)
    create_swap_quote_request = CreateSwapQuoteRequest(
        wallet_id="123e4567-e89b-12d3-a456-426614174000",
        pay_token_id="BTC",
        receive_token_id="ETH_WBTC",
        pay_amount="100"  # Provide pay_amount or receive_amount, not both
    )

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)
```
```java Java
// Import classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);  // Use development environment
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");  // Replace with your private key
    
    SwapsApi apiInstance = new SwapsApi();
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest()
      .walletId("123e4567-e89b-12d3-a456-426614174000")
      .payTokenId("BTC")
      .receiveTokenId("ETH_WBTC")
      .payAmount("100");  // Provide pay_amount or receive_amount, not both

    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Create a new request for the createSwapQuote
	createSwapQuoteRequest := &coboWaas2.CreateSwapQuoteRequest{
		WalletId:       "123e4567-e89b-12d3-a456-426614174000",
		PayTokenId:     "BTC",
		ReceiveTokenId: "ETH_WBTC",
		PayAmount:      "100",  // Provide either PayAmount or ReceiveAmount, not both
		ReceiveAmount:  nil,
	}

	// Configuration and context
	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)  // Use development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{Secret: "<YOUR_PRIVATE_KEY>"}) // Replace with private key

	// Execute the request
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).CreateSwapQuoteRequest(*createSwapQuoteRequest).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote`: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}

	// Print the response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}
```
```javascript JavaScript
// Import the CoboWaas2 module
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);  // Select the development environment
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");  // Replace with your private key

// Prepare the request
const apiInstance = new CoboWaas2.SwapsApi();
const createSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest(
  "123e4567-e89b-12d3-a456-426614174000", // wallet_id
  "BTC",  // pay_token_id
  "ETH_WBTC",  // receive_token_id
  "100",  // pay_amount, optional if receive_amount is provided
  null  // receive_amount, optional if pay_amount is provided
);

// Call the API to create swap quote
apiInstance.createSwapQuote(createSwapQuoteRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error("Error calling swap quote API: ", error);
  }
);
```
</RequestExample>

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_swap_quote201_response import CreateSwapQuote201Response
from cobo_waas2.models.create_swap_quote_request import CreateSwapQuoteRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.SwapsApi()

    # Create the request payload
    create_swap_quote_request = CreateSwapQuoteRequest(
        wallet_id='123e4567-e89b-12d3-a456-426614174000',
        pay_token_id='BTC',
        receive_token_id='ETH_WBTC',
        pay_amount='100'  # Only one of pay_amount or receive_amount should be provided
        # receive_amount='100'
    )

    try:
        # Create Swap Quote
        api_response = api_instance.create_swap_quote(create_swap_quote_request)
        print("The response of SwapsApi->create_swap_quote:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling SwapsApi->create_swap_quote: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.SwapsApi;
import com.cobo.waas2.model.CreateSwapQuoteRequest;
import com.cobo.waas2.model.CreateSwapQuote201Response;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    SwapsApi apiInstance = new SwapsApi();
    
    // Create the request payload
    CreateSwapQuoteRequest createSwapQuoteRequest = new CreateSwapQuoteRequest()
      .walletId("123e4567-e89b-12d3-a456-426614174000")
      .payTokenId("BTC")
      .receiveTokenId("ETH_WBTC")
      .payAmount("100"); // Only one of payAmount or receiveAmount should be set
    
    try {
      CreateSwapQuote201Response result = apiInstance.createSwapQuote(createSwapQuoteRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SwapsApi#createSwapQuote");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Create the request payload
	createSwapQuoteRequest := *coboWaas2.NewCreateSwapQuoteRequest(
		"123e4567-e89b-12d3-a456-426614174000", // wallet_id
		"BTC",  // pay_token_id
		"ETH_WBTC", // receive_token_id
		) 
	createSwapQuoteRequest.PayAmount = "100" // Only one of PayAmount or ReceiveAmount should be set

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.SwapsAPI.CreateSwapQuote(ctx).
		CreateSwapQuoteRequest(createSwapQuoteRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `SwapsAPI.CreateSwapQuote``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateSwapQuote`: CreateSwapQuote201Response
	fmt.Fprintf(os.Stdout, "Response from `SwapsAPI.CreateSwapQuote`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Prepare the request
const createSwapQuoteRequest = new CoboWaas2.CreateSwapQuoteRequest({
  wallet_id: "123e4567-e89b-12d3-a456-426614174000",
  pay_token_id: "BTC",
  receive_token_id: "ETH_WBTC",
  pay_amount: "100", // Only one of pay_amount or receive_amount should be provided
  // receive_amount: "100"
});

// Call the API
const apiInstance = new CoboWaas2.SwapsApi();
apiInstance
  .createSwapQuote(createSwapQuoteRequest)
  .then((data) => {
    console.log("API called successfully. Returned data: " + data);
  })
  .catch((error) => {
    console.error(error);
  });
```
</RequestExample>