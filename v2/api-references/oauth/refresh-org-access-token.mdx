---
openapi: post /oauth/token
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.token_response import TokenResponse
from cobo_waas2.models.token_request import TokenRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.OAuthApi(api_client)
    token_request = cobo_waas2.TokenRequest(
        grant_type="authorization_code",
        client_id="<YOUR_CLIENT_ID>",
        client_secret="<YOUR_CLIENT_SECRET>",
        redirect_uri="<YOUR_REDIRECT_URI>",
        code="<YOUR_AUTH_CODE>"
    )

    try:
        # Request token
        api_response = api_instance.get_token(token_request)
        print("The response of OAuthApi->get_token:\n")
        pprint(api_response)
    except Exception as e:
        print(f"Exception when calling OAuthApi->get_token: {e}")
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.OAuthApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);

    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    OAuthApi apiInstance = new OAuthApi();

    // Create a request object and set required and optional parameters
    TokenRequest tokenRequest = new TokenRequest();
    tokenRequest.setGrantType("authorization_code");
    tokenRequest.setClientId("<YOUR_CLIENT_ID>");
    tokenRequest.setClientSecret("<YOUR_CLIENT_SECRET>");
    tokenRequest.setRedirectUri("<YOUR_REDIRECT_URI>");
    tokenRequest.setCode("<YOUR_AUTH_CODE>");
    
    try {
      TokenResponse result = apiInstance.getToken(tokenRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling OAuthApi#getToken");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	tokenRequest := coboWaas2.TokenRequest{
		GrantType:    "authorization_code",
		ClientId:     "<YOUR_CLIENT_ID>",
		ClientSecret: "<YOUR_CLIENT_SECRET>",
		RedirectUri:  "<YOUR_REDIRECT_URI>",
		Code:         "<YOUR_AUTH_CODE>",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.OAuthAPI.Token(ctx).
		TokenRequest(tokenRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `OAuthAPI.Token``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `OAuthAPI.Token`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.OAuthApi();
const tokenRequest = new CoboWaas2.TokenRequest({
  grant_type: "authorization_code",
  client_id: "<YOUR_CLIENT_ID>",
  client_secret: "<YOUR_CLIENT_SECRET>",
  redirect_uri: "<YOUR_REDIRECT_URI>",
  code: "<YOUR_AUTH_CODE>"
});

apiInstance.getToken(tokenRequest).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>