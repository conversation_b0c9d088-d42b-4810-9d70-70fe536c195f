---
openapi: get /oauth/token
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.get_token2_xx_response import GetToken2XXResponse
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure API key authorization
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.OAuthApi(api_client)
    client_id = "pvSwS8iFrfK0oZrB0ugG54XPDOLEv0Ij"  # example value
    org_id = "e3986401-4aec-480a-973d-e775a4518413"  # example value
    grant_type = "org_implicit"  # example value

    try:
        api_response = api_instance.get_token(client_id, org_id, grant_type)
        print("The response of OAuthApi->get_token:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling OAuthApi->get_token: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.OAuthApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);

    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    OAuthApi apiInstance = new OAuthApi();
    String clientId = "pvSwS8iFrfK0oZrB0ugG54XPDOLEv0Ij"; 
    String orgId = "e3986401-4aec-480a-973d-e775a4518413"; 
    String grantType = "org_implicit"; 
    try {
      GetToken2XXResponse result = apiInstance.getToken(clientId, orgId, grantType);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling OAuthApi#getToken");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	clientId := "pvSwS8iFrfK0oZrB0ugG54XPDOLEv0Ij"
	orgId := "e3986401-4aec-480a-973d-e775a4518413"
	grantType := "org_implicit"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.OAuthAPI.GetToken(ctx).
		ClientId(clientId).
		OrgId(orgId).
		GrantType(grantType).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `OAuthAPI.GetToken``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `OAuthAPI.GetToken`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);

apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.OAuthApi();
const client_id = "pvSwS8iFrfK0oZrB0ugG54XPDOLEv0Ij"; 
const org_id = "e3986401-4aec-480a-973d-e775a4518413"; 
const grant_type = "org_implicit"; 
apiInstance.getToken(client_id, org_id, grant_type).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>