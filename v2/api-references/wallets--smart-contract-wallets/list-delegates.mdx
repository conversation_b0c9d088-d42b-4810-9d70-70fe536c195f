---
openapi: post /wallets/{wallet_id}/smart_contracts/delegates
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.safe_wallet_delegates_contract_call import SafeWalletDelegatesContractCall
from cobo_waas2.models.safe_wallet_delegates_transfer import SafeWalletDelegatesTransfer
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration setup
configuration = cobo_waas2.Configuration(
    host="https://api.dev.cobo.com/v2",
    api_private_key="<YOUR_PRIVATE_KEY>"
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsSmartContractWalletsApi(api_client)
    wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    
    # Using SafeWalletDelegatesContractCall
    contract_call_delegates = SafeWalletDelegatesContractCall(
        request_type="ContractCall",
        address="******************************************",
        value="1.5",
        calldata="0xa22cb4650000000000000000000000001e0049783f008a0085193e00003d00cd54003c71000000000000000000000000000000000000000000000000000000000000DEMO"
    )
    
    # Using SafeWalletDelegatesTransfer
    transfer_delegates = SafeWalletDelegatesTransfer(
        request_type="Transfer",
        token_id="ETH",
        amount="0.1",
        address="******************************************"
    )
    
    try:
        # Change to appropriate request instance
        api_response = api_instance.delegate_smart_contract_call(wallet_id, safe_wallet_delegates=contract_call_delegates)
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsSmartContractWalletsApi: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsSmartContractWalletsApi;
import com.cobo.waas2.model.SafeWalletDelegatesContractCall;
import com.cobo.waas2.model.SafeWalletDelegatesTransfer;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    WalletsSmartContractWalletsApi apiInstance = new WalletsSmartContractWalletsApi();
    UUID walletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    
    // Contract call delegates
    SafeWalletDelegatesContractCall contractCallDelegates = new SafeWalletDelegatesContractCall()
            .requestType("ContractCall")
            .address("******************************************")
            .value("1.5")
            .calldata("0xa22cb4650000000000000000000000001e0049783f008a0085193e00003d00cd54003c71000000000000000000000000000000000000000000000000000000000000DEMO");
    
    // Transfer delegates
    SafeWalletDelegatesTransfer transferDelegates = new SafeWalletDelegatesTransfer()
            .requestType("Transfer")
            .tokenId("ETH")
            .amount("0.1")
            .address("******************************************");

    try {
      // Choose the appropriate request to call
      // If using contract call delegates:
      List<CoboSafeDelegate> result = apiInstance.delegateSmartContractCall(walletId, contractCallDelegates);
      // If using transfer delegates:
      // List<CoboSafeDelegate> result = apiInstance.delegateSmartContractCall(walletId, transferDelegates);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsSmartContractWalletsApi");
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	walletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	
	// Contract call delegates
	contractCallDelegates := coboWaas2.SafeWalletDelegatesContractCall{
		RequestType: "ContractCall",
		Address:     "******************************************",
		Value:       "1.5",
		Calldata:    "0xa22cb4650000000000000000000000001e0049783f008a0085193e00003d00cd54003c71000000000000000000000000000000000000000000000000000000000000DEMO",
	}

	// Transfer delegates
	transferDelegates := coboWaas2.SafeWalletDelegatesTransfer{
		RequestType: "Transfer",
		TokenId:     "ETH",
		Amount:      "0.1",
		Address:     "******************************************",
	}

	config := coboWaas2.NewConfiguration()
	client := coboWaas2.NewAPIClient(config)
	ctx := context.Background()
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{Secret: "<YOUR_PRIVATE_KEY>"})

	// Choose the appropriate instance to execute
	resp, r, err := client.WalletsSmartContractWalletsAPI.DelegateSmartContractCall(ctx, walletId).
		SafeWalletDelegatesContractCall(contractCallDelegates).
		Execute()
	
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling WalletsSmartContractWalletsAPI: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	
	fmt.Fprintf(os.Stdout, "Response: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.WalletsSmartContractWalletsApi();
const wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

// Contract call delegates
const contractCallOpts = {
  SafeWalletDelegatesContractCall: new CoboWaas2.SafeWalletDelegatesContractCall(
    "ContractCall",
    "******************************************",
    "1.5",
    "0xa22cb4650000000000000000000000001e0049783f008a0085193e00003d00cd54003c71000000000000000000000000000000000000000000000000000000000000DEMO"
  ),
};

// Transfer delegates
const transferOpts = {
  SafeWalletDelegatesTransfer: new CoboWaas2.SafeWalletDelegatesTransfer(
    "Transfer",
    "ETH",
    "0.1",
    "******************************************"
  ),
};

// Choose the appropriate request type to call
apiInstance.delegateSmartContractCall(wallet_id, contractCallOpts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>