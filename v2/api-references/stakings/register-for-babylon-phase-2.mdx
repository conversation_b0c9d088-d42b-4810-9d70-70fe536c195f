---
openapi: post /stakings/protocols/babylon/stakings/registrations
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_babylon_staking_registration201_response import CreateBabylonStakingRegistration201Response
from cobo_waas2.models.create_babylon_staking_registration_request import CreateBabylonStakingRegistrationRequest
from cobo_waas2.models.mpc_stake_source import MpcStakeSource
from cobo_waas2.models.stake_source_type import StakeSourceType
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    request = CreateBabylonStakingRegistrationRequest(
        staking_id="3f2840ce-44eb-450b-aa81-d3f84b772efb",
        babylon_address=MpcStakeSource(
            source_type=StakeSourceType.ORG_CONTROLLED,
            wallet_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
            address="******************************************"
        )
    )

    try:
        api_response = api_instance.create_babylon_staking_registration(request)
        print("The response of StakingsApi->create_babylon_staking_registration:\n")
        pprint(api_response)
    except ApiException as e:
        print(
            "Exception when calling StakingsApi->create_babylon_staking_registration: %s\n"
            % e
        )
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();
    CreateBabylonStakingRegistrationRequest request = new CreateBabylonStakingRegistrationRequest();
    request.setStakingId("3f2840ce-44eb-450b-aa81-d3f84b772efb");
    MpcStakeSource source = new MpcStakeSource();
    source.setSourceType(StakeSourceType.Org_Controlled);
    source.setWalletId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    source.setAddress("******************************************");
    request.setBabylonAddress(source);

    try {
      CreateBabylonStakingRegistration201Response result =
          apiInstance.createBabylonStakingRegistration(request);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#createBabylonStakingRegistration");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	mpcStakeSource := coboWaas2.MpcStakeSource{
		SourceType: coboWaas2.StakeSourceTypeOrgControlled,
		WalletId:   "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		Address:    "******************************************",
	}

	createBabylonStakingRegistrationRequest := coboWaas2.CreateBabylonStakingRegistrationRequest{
		StakingId: "3f2840ce-44eb-450b-aa81-d3f84b772efb",
		BabylonAddress: &mpcStakeSource,
	}

	resp, r, err := apiClient.StakingsAPI.CreateBabylonStakingRegistration(ctx).
		CreateBabylonStakingRegistrationRequest(createBabylonStakingRegistrationRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `StakingsAPI.CreateBabylonStakingRegistration``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(
		os.Stdout,
		"Response from `StakingsAPI.CreateBabylonStakingRegistration`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.StakingsApi();
const opts = {
  CreateBabylonStakingRegistrationRequest: new CoboWaas2.CreateBabylonStakingRegistrationRequest({
    staking_id: "3f2840ce-44eb-450b-aa81-d3f84b772efb",
    babylon_address: new CoboWaas2.MpcStakeSource({
      source_type: CoboWaas2.StakeSourceType.Org_Controlled,
      wallet_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
      address: "******************************************"
    })
  })
};
apiInstance.createBabylonStakingRegistration(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>