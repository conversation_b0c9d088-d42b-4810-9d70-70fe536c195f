---
openapi: post /stakings/activities/claim
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_claim_activity_request import CreateClaimActivityRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    create_claim_activity_request = CreateClaimActivityRequest(
        staking_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        request_id="unique-request-id",  # Optional
        app_initiator="<EMAIL>"  # Optional
    )

    try:
        api_response = api_instance.create_claim_activity(create_claim_activity_request=create_claim_activity_request)
        print("The response of StakingsApi->create_claim_activity:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling StakingsApi->create_claim_activity: %s\n" % e)
```
```java Java
package com.cobo.example;

import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();

    CreateClaimActivityRequest createClaimActivityRequest = new CreateClaimActivityRequest();
    createClaimActivityRequest.setStakingId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    // Optional parameters
    createClaimActivityRequest.setRequestId("unique-request-id");
    createClaimActivityRequest.setAppInitiator("<EMAIL>");

    try {
      CreateStakeActivity201Response result = apiInstance.createClaimActivity(createClaimActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#createClaimActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createClaimActivityRequest := *coboWaas2.NewCreateClaimActivityRequest(
		"f47ac10b-58cc-4372-a567-0e02b2c3d479",
	)
	// Optionally set request ID and app initiator
	createClaimActivityRequest.RequestId = "unique-request-id"
	createClaimActivityRequest.AppInitiator = "<EMAIL>"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.StakingsAPI.CreateClaimActivity(ctx).
		CreateClaimActivityRequest(createClaimActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `StakingsAPI.CreateClaimActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.CreateClaimActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.StakingsApi();
const createClaimActivityRequest = new CoboWaas2.CreateClaimActivityRequest({
  staking_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  request_id: "unique-request-id", // Optional
  app_initiator: "<EMAIL>" // Optional
});

apiInstance.createClaimActivity({ CreateClaimActivityRequest: createClaimActivityRequest }).then(
  function (data) {
    console.log("API called successfully. Returned data: " + data);
  },
  function (error) {
    console.error(error);
  }
);
```
</RequestExample>