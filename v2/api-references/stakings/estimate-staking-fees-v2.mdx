---
openapi: post /stakings/estimate_fee_v2
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models import EthStakeEstimatedFee, EstimateStakeFee
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    
    # Constructing the Request object
    estimate_stake_fee = EstimateStakeFee(
        activity_type="Stake",
        amount="100.00",
        wallet_id="<WALLET_ID>",
        address="<ADDRESS>",
        pool_id="<POOL_ID>",
        fee={"fee_type": "Fixed"}
    )

    try:
        api_response = api_instance.get_staking_estimation_fee_v2(estimate_stake_fee)
        print("The response of StakingsApi->get_staking_estimation_fee_v2:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling StakingsApi->get_staking_estimation_fee_v2: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();

    // Constructing the Request object with required fields
    EstimateStakeFee estimateStakeFee = new EstimateStakeFee();
    estimateStakeFee.setActivityType(ActivityType.Stake);
    estimateStakeFee.setAmount("100.00");
    estimateStakeFee.setWalletId("<WALLET_ID>");
    estimateStakeFee.setAddress("<ADDRESS>");
    estimateStakeFee.setPoolId("<POOL_ID>");
    TransactionRequestFee fee = new TransactionRequestFixedFee();
    estimateStakeFee.setFee(fee);

    try {
      EthStakeEstimatedFee result = apiInstance.getStakingEstimationFeeV2(estimateStakeFee);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#getStakingEstimationFeeV2");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Constructing the Request object
	estimateStakeFee := coboWaas2.EstimateStakeFee{
		ActivityType: "Stake",
		Amount:       "100.00",
		WalletId:     "<WALLET_ID>",
		Address:      "<ADDRESS>",
		PoolId:       "<POOL_ID>",
		Fee:          coboWaas2.TransactionRequestFee{FeeType: "Fixed"},
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.StakingsAPI.GetStakingEstimationFeeV2(ctx).
		EstimateStakeFee(estimateStakeFee).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `StakingsAPI.GetStakingEstimationFeeV2``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `GetStakingEstimationFeeV2`: EthStakeEstimatedFee
	fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.GetStakingEstimationFeeV2`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Constructing the Request object
const estimateStakeFee = new CoboWaas2.EstimateStakeFee();
estimateStakeFee.activity_type = "Stake";
estimateStakeFee.amount = "100.00";
estimateStakeFee.wallet_id = "<WALLET_ID>";
estimateStakeFee.address = "<ADDRESS>";
estimateStakeFee.pool_id = "<POOL_ID>";
estimateStakeFee.fee = { fee_type: "Fixed" };

// Call the API
const apiInstance = new CoboWaas2.StakingsApi();
apiInstance.getStakingEstimationFeeV2({ EstimateStakeFee: estimateStakeFee }).then(
  (data) => {
    console.log("API called successfully. Returned data: ", data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>