---
openapi: get /stakings/protocols/babylon/airdrops/registrations/{registration_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.babylon_airdrop_registration import BabylonAirdropRegistration
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    registration_id = "registration_id_example"

    try:
        api_response = api_instance.get_babylon_airdrop_registration_by_id(
            registration_id
        )
        print("The response of StakingsApi->get_babylon_airdrop_registration_by_id:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling StakingsApi->get_babylon_airdrop_registration_by_id: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();
    String registrationId = "registrationId_example";
    try {
      BabylonAirdropRegistration result = apiInstance.getBabylonAirdropRegistrationById(registrationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#getBabylonAirdropRegistrationById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
    "context"
    "fmt"
    coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
    "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
    "os"
)

func main() {
    registrationId := "registrationId_example"

    configuration := coboWaas2.NewConfiguration()
    apiClient := coboWaas2.NewAPIClient(configuration)
    ctx := context.Background()

    ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
    ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
        Secret: "<YOUR_PRIVATE_KEY>",
    })
    resp, r, err := apiClient.StakingsAPI.GetBabylonAirdropRegistrationById(ctx, registrationId).
        Execute()
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error when calling `StakingsAPI.GetBabylonAirdropRegistrationById``: %v\n", err)
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.GetBabylonAirdropRegistrationById`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.StakingsApi();
const registration_id = "registration_id_example";
apiInstance.getBabylonAirdropRegistrationById(registration_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>