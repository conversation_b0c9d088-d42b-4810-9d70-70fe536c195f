---
openapi: post /stakings/activities/stake
---

<RequestExample>
```python Python
# Python Code Example

import cobo_waas2
from cobo_waas2.models.create_stake_activity201_response import (
    CreateStakeActivity201Response,
)
from cobo_waas2.models.create_stake_activity_request import CreateStakeActivityRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    create_stake_activity_request = CreateStakeActivityRequest(
        staking_pool_id="example_pool_id",
        amount="100.0",
        currency="ETH"
    )

    try:
        api_response = api_instance.create_stake_activity(
            create_stake_activity_request=create_stake_activity_request
        )
        print("The response of StakingsApi->create_stake_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling StakingsApi->create_stake_activity: %s\n" % e)
```
```java Java
// Java Code Example

import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();

    // Create a stake activity request with hypothetical parameters
    CreateStakeActivityRequest createStakeActivityRequest = new CreateStakeActivityRequest()
        .stakingPoolId("example_pool_id")
        .amount("100.0")
        .currency("ETH");
    try {
      CreateStakeActivity201Response result =
          apiInstance.createStakeActivity(createStakeActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#createStakeActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
// Go Code Example

package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createStakeActivityRequest := coboWaas2.NewCreateStakeActivityRequest("example_pool_id", "100.0", "ETH")

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.StakingsAPI.CreateStakeActivity(ctx).
		CreateStakeActivityRequest(*createStakeActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `StakingsAPI.CreateStakeActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.CreateStakeActivity`: %v\n", resp)
}
```
```javascript JavaScript
// JavaScript Code Example

const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Create a stake activity request with hypothetical parameters
const createStakeActivityRequest = new CoboWaas2.CreateStakeActivityRequest({
  stakingPoolId: "example_pool_id",
  amount: "100.0",
  currency: "ETH"
});
// Call the API
const apiInstance = new CoboWaas2.StakingsApi();
apiInstance.createStakeActivity({CreateStakeActivityRequest: createStakeActivityRequest}).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>