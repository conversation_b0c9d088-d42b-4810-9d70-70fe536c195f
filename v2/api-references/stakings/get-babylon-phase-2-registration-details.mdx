---
openapi: get /stakings/protocols/babylon/stakings/registrations/{registration_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.babylon_staking_registration import BabylonStakingRegistration
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.StakingsApi(api_client)
    registration_id = "registration_id_example"

    try:
        # Get Babylon Phase-2 registration details
        api_response = api_instance.get_babylon_staking_registration_by_id(
            registration_id
        )
        print("The response:")
        print(f"Status: {api_response.status}")
        print(f"BTC Address: {api_response.btc_address}")
        print(f"Babylon Address: {api_response.babylon_address}")
    except Exception as e:
        print(
            "Exception when calling StakingsApi->get_babylon_staking_registration_by_id: %s\n"
            % e
        )
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.BabylonStakingRegistration;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();
    String registrationId = "registrationId_example";
    try {
      BabylonStakingRegistration result = apiInstance.getBabylonStakingRegistrationById(registrationId);
      System.out.println("Status: " + result.getStatus());
      System.out.println("BTC Address: " + result.getBtcAddress());
      System.out.println("Babylon Address: " + result.getBabylonAddress());
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#getBabylonStakingRegistrationById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
    "context"
    "fmt"
    coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
    "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
    "os"
)

func main() {
    registrationId := "registrationId_example"

    configuration := coboWaas2.NewConfiguration()
    // Initialize the API client
    apiClient := coboWaas2.NewAPIClient(configuration)
    ctx := context.Background()

    // Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
    ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
        Secret: "<YOUR_PRIVATE_KEY>",
    })
    resp, r, err := apiClient.StakingsAPI.GetBabylonStakingRegistrationById(ctx, registrationId).
        Execute()
    if err != nil {
        fmt.Fprintf(
            os.Stderr,
            "Error when calling `StakingsAPI.GetBabylonStakingRegistrationById``: %v\n",
            err,
        )
        fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
    }
    // Assuming typical fields "Status", "BtcAddress", "BabylonAddress"
    fmt.Fprintf(
        os.Stdout,
        "Response from `StakingsAPI.GetBabylonStakingRegistrationById`: Status: %s, BTC Address: %s, Babylon Address: %s\n",
        resp.Status, resp.BtcAddress, resp.BabylonAddress,
    )
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;

// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);

// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Call the API
const apiInstance = new CoboWaas2.StakingsApi();
const registration_id = "registration_id_example";

apiInstance.getBabylonStakingRegistrationById(registration_id).then(
  (data) => {
    console.log("API called successfully. Returned data: ");
    console.log(`Status: ${data.status}`);
    console.log(`BTC Address: ${data.btc_address}`);
    console.log(`Babylon Address: ${data.babylon_address}`);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>