---
openapi: post /stakings/protocols/babylon/airdrops/registrations
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_babylon_airdrop_registration201_response import (
    CreateBabylonAirdropRegistration201Response,
)
from cobo_waas2.models.create_babylon_airdrop_registration_request import (
    CreateBabylonAirdropRegistrationRequest,
)
from cobo_waas2.models.mpc_stake_source import MpcStakeSource
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    
    btc_address = MpcStakeSource(
        source_type="Org-Controlled",  # Populate with a valid staking source type
        wallet_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",  # Example UUID
        address="******************************************",  # Example address
    )
    babylon_address = MpcStakeSource(
        source_type="Org-Controlled",
        wallet_id="another-uuid-value",  # Another valid UUID
        address="another-address-value",  # Another valid address
    )
    create_babylon_airdrop_registration_request = CreateBabylonAirdropRegistrationRequest(
        btc_address=btc_address,
        babylon_address=babylon_address,
    )
    
    try:
        api_response = api_instance.create_babylon_airdrop_registration(
            create_babylon_airdrop_registration_request=create_babylon_airdrop_registration_request
        )
        print("The response of StakingsApi->create_babylon_airdrop_registration:\n")
        pprint(api_response)
    except ApiException as e:
        print(
            "Exception when calling StakingsApi->create_babylon_airdrop_registration: %s\n" % e
        )
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.CreateBabylonAirdropRegistrationRequest;
import com.cobo.waas2.model.CreateBabylonAirdropRegistration201Response;
import com.cobo.waas2.model.MpcStakeSource;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    StakingsApi apiInstance = new StakingsApi();
    
    MpcStakeSource btcAddress = new MpcStakeSource();
    btcAddress.setSourceType("Org-Controlled"); // Populate with a valid staking source type
    btcAddress.setWalletId("f47ac10b-58cc-4372-a567-0e02b2c3d479"); // Example UUID
    btcAddress.setAddress("******************************************"); // Example address
    
    MpcStakeSource babylonAddress = new MpcStakeSource();
    babylonAddress.setSourceType("Org-Controlled"); // Same setup as above
    babylonAddress.setWalletId("another-uuid-value"); // Another valid UUID
    babylonAddress.setAddress("another-address-value"); // Another valid address
    
    CreateBabylonAirdropRegistrationRequest createBabylonAirdropRegistrationRequest = new CreateBabylonAirdropRegistrationRequest();
    createBabylonAirdropRegistrationRequest.setBtcAddress(btcAddress);
    createBabylonAirdropRegistrationRequest.setBabylonAddress(babylonAddress);
    
    try {
      CreateBabylonAirdropRegistration201Response result = apiInstance.createBabylonAirdropRegistration(createBabylonAirdropRegistrationRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#createBabylonAirdropRegistration");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createBabylonAirdropRegistrationRequest := *coboWaas2.NewCreateBabylonAirdropRegistrationRequest()

	btcAddress := coboWaas2.MpcStakeSource{
		BaseStakeSource: coboWaas2.BaseStakeSource{
			SourceType: "Org-Controlled",
			WalletId:   "f47ac10b-58cc-4372-a567-0e02b2c3d479",
			Address:    "******************************************",
		},
	}
	babylonAddress := coboWaas2.MpcStakeSource{
		BaseStakeSource: coboWaas2.BaseStakeSource{
			SourceType: "Org-Controlled",
			WalletId:   "another-uuid-value",
			Address:    "another-address-value",
		},
	}

	createBabylonAirdropRegistrationRequest.BtcAddress = &btcAddress
	createBabylonAirdropRegistrationRequest.BabylonAddress = &babylonAddress

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.StakingsAPI.CreateBabylonAirdropRegistration(ctx).
		CreateBabylonAirdropRegistrationRequest(createBabylonAirdropRegistrationRequest).
		Execute()

	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `StakingsAPI.CreateBabylonAirdropRegistration``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	} else {
		fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.CreateBabylonAirdropRegistration`: %v\n", resp)
	}
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.StakingsApi();

const btcAddress = new CoboWaas2.MpcStakeSource();
btcAddress.sourceType = "Org-Controlled";
btcAddress.walletId = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
btcAddress.address = "******************************************";

const babylonAddress = new CoboWaas2.MpcStakeSource();
babylonAddress.sourceType = "Org-Controlled";
babylonAddress.walletId = "another-uuid-value";
babylonAddress.address = "another-address-value";

const opts = {
  CreateBabylonAirdropRegistrationRequest: new CoboWaas2.CreateBabylonAirdropRegistrationRequest({
    btcAddress: btcAddress,
    babylonAddress: babylonAddress,
  }),
};

apiInstance.createBabylonAirdropRegistration(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + JSON.stringify(data));
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>