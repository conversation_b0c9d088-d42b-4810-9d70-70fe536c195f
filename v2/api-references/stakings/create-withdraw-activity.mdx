---
openapi: post /stakings/activities/withdraw
---

<RequestExample>
```python Python
# Import required libraries and modules
import cobo_waas2
from cobo_waas2.models.create_stake_activity201_response import CreateStakeActivity201Response
from cobo_waas2.models.create_withdraw_activity_request import CreateWithdrawActivityRequest
from cobo_waas2.models.transaction_request_fixed_fee import TransactionRequestFixedFee
from cobo_waas2.models.transaction_request_fee import TransactionRequestFee
from cobo_waas2.models.activity_initiator import ActivityInitiator
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration for the API
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>", # Replace with your private key
    host="https://api.dev.cobo.com/v2"    # Development environment URL
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.StakingsApi(api_client)

    # Create an instance of CreateWithdrawActivityRequest
    create_withdraw_activity_request = CreateWithdrawActivityRequest(
        request_id='f47ac10b-58cc-4372-a567-0e02b2c3d479',
        staking_id='0011039d-27fb-49ba-b172-6e0aa80e37ec',
        amount='100.00',
        fee=TransactionRequestFee(
            fee_type="Fixed",
            token_id="TRON",
            max_fee_amount="0.1"
        ),
        app_initiator=ActivityInitiator(
            app_initiator='<EMAIL>'
        )
    )

    try:
        # Create withdraw activity
        api_response = api_instance.create_withdraw_activity(
            create_withdraw_activity_request=create_withdraw_activity_request
        )
        print("The response of StakingsApi->create_withdraw_activity:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling StakingsApi->create_withdraw_activity: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.*;

// Define the request models
class WithdrawRequest {
    public static class CreateExtendWithdrawActivityRequest extends CreateWithdrawActivityRequest {
        private TransactionRequestFixedFee fee;
        private ActivityInitiator initiator;

        public CreateExtendWithdrawActivityRequest(String requestId, String stakingId) {
            super.setRequestId(requestId);
            super.setStakingId(stakingId);
            this.fee = new TransactionRequestFixedFee();
            this.initiator = new ActivityInitiator();
        }

        public void setFixedFee(String feeType, String tokenId, String maxFeeAmount) {
            this.fee.setFeeType(FeeType.fromValue(feeType));
            this.fee.setTokenId(tokenId);
            this.fee.setMaxFeeAmount(maxFeeAmount);
        }

        public void setInitiator(String appInitiator) {
            this.initiator.setAppInitiator(appInitiator);
        }
    }
}

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);

    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key
    StakingsApi apiInstance = new StakingsApi();
    
    WithdrawRequest.CreateExtendWithdrawActivityRequest createWithdrawActivityRequest = 
        new WithdrawRequest.CreateExtendWithdrawActivityRequest("f47ac10b-58cc-4372-a567-0e02b2c3d479", "0011039d-27fb-49ba-b172-6e0aa80e37ec");
    createWithdrawActivityRequest.setAmount("100.00");
    createWithdrawActivityRequest.setFixedFee("Fixed", "TRON", "0.1");
    createWithdrawActivityRequest.setInitiator("<EMAIL>");

    try {
      CreateStakeActivity201Response result = apiInstance.createWithdrawActivity(createWithdrawActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#createWithdrawActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Create a new instance of CreateWithdrawActivityRequest
	createWithdrawActivityRequest := coboWaas2.CreateWithdrawActivityRequest{
		RequestId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		StakingId: "0011039d-27fb-49ba-b172-6e0aa80e37ec",
		Amount:    "100.00",
		Fee: &coboWaas2.TransactionRequestFixedFee{
			FeeType:     coboWaas2.FeeTypeFixed,
			TokenId:     "TRON",
			MaxFeeAmount: "0.1",
		},
		AppInitiator: &coboWaas2.ActivityInitiator{
			AppInitiator: "<EMAIL>",
		},
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.StakingsAPI.CreateWithdrawActivity(ctx).
		CreateWithdrawActivityRequest(createWithdrawActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `StakingsAPI.CreateWithdrawActivity``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateWithdrawActivity`: CreateStakeActivity201Response
	fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.CreateWithdrawActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);

// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Call the API
const apiInstance = new CoboWaas2.StakingsApi();
const createWithdrawActivityRequest = new CoboWaas2.CreateWithdrawActivityRequest();

createWithdrawActivityRequest.request_id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
createWithdrawActivityRequest.staking_id = '0011039d-27fb-49ba-b172-6e0aa80e37ec';
createWithdrawActivityRequest.amount = '100.00';

// Define Fixed Fee
const fixedFee = new CoboWaas2.TransactionRequestFixedFee();
fixedFee.fee_type = 'Fixed';
fixedFee.token_id = 'TRON';
fixedFee.max_fee_amount = '0.1';
createWithdrawActivityRequest.fee = fixedFee;

// Define Activity Initiator
createWithdrawActivityRequest.app_initiator = new CoboWaas2.ActivityInitiator();
createWithdrawActivityRequest.app_initiator.app_initiator = '<EMAIL>';

const opts = {
  createWithdrawActivityRequest: createWithdrawActivityRequest
};

apiInstance.createWithdrawActivity(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>