---
openapi: post /stakings/activities/unstake
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_stake_activity201_response import CreateStakeActivity201Response
from cobo_waas2.models.create_unstake_activity_request import CreateUnstakeActivityRequest
from cobo_waas2.models.eth_unstake_extra import EthUnstakeExtra
from cobo_waas2.models.transaction_request_fee import TransactionRequestFee
from cobo_waas2.models.activity_initiator import ActivityInitiator
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.StakingsApi(api_client)
    create_unstake_activity_request = CreateUnstakeActivityRequest(
        request_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        staking_id="0011039d-27fb-49ba-b172-6e0aa80e37ec",
        amount="100.00",
        fee=TransactionRequestFee(
            # Set fee details
        ),
        extra=EthUnstakeExtra(
            # Set extra details
        )
    )
    activity_initiator = ActivityInitiator(
        app_initiator="<EMAIL>"
    )

    try:
        api_response = api_instance.create_unstake_activity(create_unstake_activity_request=create_unstake_activity_request)
        print("The response of StakingsApi->create_unstake_activity:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling StakingsApi->create_unstake_activity: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");

    StakingsApi apiInstance = new StakingsApi();
    CreateUnstakeActivityRequest createUnstakeActivityRequest = new CreateUnstakeActivityRequest();
    
    // Set parameters
    createUnstakeActivityRequest.setRequestId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    createUnstakeActivityRequest.setStakingId("0011039d-27fb-49ba-b172-6e0aa80e37ec");
    createUnstakeActivityRequest.setAmount("100.00");
    // Assuming a method setFee exists in your SDK
    TransactionRequestFee fee = new TransactionRequestFee();
    // Set fee details
    createUnstakeActivityRequest.setFee(fee);
    // Assuming a method setExtra exists in your SDK
    EthUnstakeExtra extra = new EthUnstakeExtra();
    // Set extra details as per EthUnstakeExtra
    createUnstakeActivityRequest.setExtra(extra);

    ActivityInitiator activityInitiator = new ActivityInitiator();
    activityInitiator.setAppInitiator("<EMAIL>");
    
    try {
      CreateStakeActivity201Response result = apiInstance.createUnstakeActivity(createUnstakeActivityRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#createUnstakeActivity");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createUnstakeActivityRequest := *coboWaas2.NewCreateUnstakeActivityRequest("0011039d-27fb-49ba-b172-6e0aa80e37ec")
	createUnstakeActivityRequest.RequestId = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	createUnstakeActivityRequest.Amount = "100.00"

	fee := coboWaas2.TransactionRequestFee{}
	// Set fee details
	createUnstakeActivityRequest.Fee = fee

	extra := coboWaas2.EthUnstakeExtra{}
	// Set extra details
	createUnstakeActivityRequest.Extra = extra

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.StakingsAPI.CreateUnstakeActivity(ctx).
		CreateUnstakeActivityRequest(createUnstakeActivityRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `StakingsAPI.CreateUnstakeActivity``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.CreateUnstakeActivity`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.StakingsApi();
const createUnstakeActivityRequest = new CoboWaas2.CreateUnstakeActivityRequest();
createUnstakeActivityRequest.requestId = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
createUnstakeActivityRequest.stakingId = "0011039d-27fb-49ba-b172-6e0aa80e37ec";
createUnstakeActivityRequest.amount = "100.00";

const fee = new CoboWaas2.TransactionRequestFee();
// Set fee details
createUnstakeActivityRequest.fee = fee;

const extra = new CoboWaas2.EthUnstakeExtra();
// Set extra details
createUnstakeActivityRequest.extra = extra;

const activityInitiator = new CoboWaas2.ActivityInitiator();
activityInitiator.appInitiator = "<EMAIL>";

const opts = { CreateUnstakeActivityRequest: createUnstakeActivityRequest };
apiInstance.createUnstakeActivity(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>