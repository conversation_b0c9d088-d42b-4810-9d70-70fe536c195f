---
openapi: get /stakings/pools/{pool_id}
---

<RequestExample>
```python Python
# Import required modules
import cobo_waas2
from cobo_waas2.models.pool_details import PoolDetails
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration setup
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Replace with your private key
    host="https://api.dev.cobo.com/v2"  # Development environment
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Instantiate the StakingsApi
    api_instance = cobo_waas2.StakingsApi(api_client)
    pool_id = "babylon_btc"  # Example pool ID

    try:
        # Call the get_staking_pool_by_id method
        api_response = api_instance.get_staking_pool_by_id(pool_id)
        print("The response of StakingsApi->get_staking_pool_by_id:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling StakingsApi->get_staking_pool_by_id: %s\n" % e)
```
```java Java
// Import classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;
import com.cobo.waas2.model.PoolDetails;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        defaultClient.setEnv(Env.DEV); // Development environment

        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key
        StakingsApi apiInstance = new StakingsApi();
        String poolId = "babylon_btc"; // Example pool ID

        try {
            PoolDetails result = apiInstance.getStakingPoolById(poolId);
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling StakingsApi#getStakingPoolById");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	poolId := "babylon_btc" // Example pool ID

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Set development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>", // Replace with your private key
	})

	// Call API method
	resp, r, err := apiClient.StakingsAPI.GetStakingPoolById(ctx, poolId).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `StakingsAPI.GetStakingPoolById``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	} else {
		// Response from `GetStakingPoolById`: PoolDetails
		fmt.Fprintf(os.Stdout, "Response from `StakingsAPI.GetStakingPoolById`: %v\n", resp)
	}
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV); // Development environment

apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

// Instantiate the StakingsApi
const apiInstance = new CoboWaas2.StakingsApi();
const pool_id = "babylon_btc"; // Example pool ID

// Call the API
apiInstance.getStakingPoolById(pool_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error("Exception when calling StakingsApi->getStakingPoolById:", error);
  }
);
```
</RequestExample>