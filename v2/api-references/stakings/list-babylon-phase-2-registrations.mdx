---
openapi: get /stakings/protocols/babylon/stakings/registrations
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_babylon_staking_registrations200_response import (
    ListBabylonStakingRegistrations200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.StakingsApi(api_client)
    status = "Processing"  # The registration request status. Possible values: Processing, Completed, Failed
    staking_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"  # The ID of the Phase-1 BTC staking position
    limit = 10  # The maximum number of objects to return
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"  # Paginate before this object
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"  # Paginate after this object

    try:
        # List Babylon Phase-2 registrations
        api_response = api_instance.list_babylon_staking_registrations(
            status=status,
            staking_id=staking_id,
            limit=limit,
            before=before,
            after=after,
        )
        print("The response of StakingsApi->list_babylon_staking_registrations:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling StakingsApi->list_babylon_staking_registrations: %s\n"
            % e
        )
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.StakingsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    StakingsApi apiInstance = new StakingsApi();
    String status = "Processing"; // The registration request status. Possible values: Processing, Completed, Failed
    String stakingId = "f47ac10b-58cc-4372-a567-0e02b2c3d479"; // The ID of the Phase-1 BTC staking position
    Integer limit = 10; // The maximum number of objects to return
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"; // Paginate before this object
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"; // Paginate after this object
    try {
      ListBabylonStakingRegistrations200Response result =
          apiInstance.listBabylonStakingRegistrations(status, stakingId, limit, before, after);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StakingsApi#listBabylonStakingRegistrations");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	status := "Processing" // The registration request status. Possible values: Processing, Completed, Failed
	stakingId := "f47ac10b-58cc-4372-a567-0e02b2c3d479" // The ID of the Phase-1 BTC staking position
	limit := int32(10) // The maximum number of objects to return
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1" // Paginate before this object
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk" // Paginate after this object

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.StakingsAPI.ListBabylonStakingRegistrations(ctx).
		Status(status).
		StakingId(stakingId).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `StakingsAPI.ListBabylonStakingRegistrations``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListBabylonStakingRegistrations`: ListBabylonStakingRegistrations200Response
	fmt.Fprintf(
		os.Stdout,
		"Response from `StakingsAPI.ListBabylonStakingRegistrations`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.StakingsApi();
const opts = {
  status: "Processing", // The registration request status. Possible values: Processing, Completed, Failed
  staking_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479", // The ID of the Phase-1 BTC staking position
  limit: 10, // The maximum number of objects to return
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1", // Paginate before this object
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk", // Paginate after this object
};
apiInstance.listBabylonStakingRegistrations(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>