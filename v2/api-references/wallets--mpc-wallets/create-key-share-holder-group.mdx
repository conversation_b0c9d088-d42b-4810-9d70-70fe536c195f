---
openapi: post /wallets/mpc/vaults/{vault_id}/key_share_holder_groups
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_key_share_holder_group_request import CreateKeyShareHolderGroupRequest
from cobo_waas2.models.key_share_holder_group import KeyShareHolderGroup
from cobo_waas2.models.create_key_share_holder import CreateKeyShareHolder
from cobo_waas2.rest import ApiException
from pprint import pprint

# 配置
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    create_key_share_holder_group_request = CreateKeyShareHolderGroupRequest(
        key_share_holder_group_type='MainGroup',
        participants=3,
        threshold=2,
        key_share_holders=[
            CreateKeyShareHolder(
                name="Key Share Holder Name",
                tss_node_id="coboAbCdEfGhIjKlMnOpQrStUvWxYz1234567890abcdefghi",
                signer=True
            )
        ]
    )

    try:
        api_response = api_instance.create_key_share_holder_group(
            vault_id,
            create_key_share_holder_group_request=create_key_share_holder_group_request,
        )
        print("The response of WalletsMPCWalletsApi->create_key_share_holder_group:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsMPCWalletsApi->create_key_share_holder_group: %s\n" % e)
```
```java Java
// 导入类
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

import java.util.UUID;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // 选择开发环境。使用生产环境时，将 `Env.DEV` 替换为 `Env.PROD`
        defaultClient.setEnv(Env.DEV);
        // 替换 `<YOUR_PRIVATE_KEY>` 为您的私钥
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();

        UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
        CreateKeyShareHolderGroupRequest createKeyShareHolderGroupRequest = new CreateKeyShareHolderGroupRequest()
            .keyShareHolderGroupType(KeyShareHolderGroupTypeEnum.MAINGROUP)
            .participants(3)
            .threshold(2)
            .addKeyShareHoldersItem(new CreateKeyShareHolder()
                .name("Key Share Holder Name")
                .type(KeyShareHolderTypeEnum.CUSTOMTYPE)
                .tssNodeId("coboAbCdEfGhIjKlMnOpQrStUvWxYz1234567890abcdefghi")
                .signer(true)
            );

        try {
            KeyShareHolderGroup result = apiInstance.createKeyShareHolderGroup(vaultId, createKeyShareHolderGroupRequest);
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling WalletsMpcWalletsApi#createKeyShareHolderGroup");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	createKeyShareHolderGroupRequest := coboWaas2.CreateKeyShareHolderGroupRequest{
		KeyShareHolderGroupType: "MainGroup",
		Participants:            int32(3),
		Threshold:               int32(2),
		KeyShareHolders: []coboWaas2.CreateKeyShareHolder{
			{
				Name:      "Key Share Holder Name",
				TssNodeId: "coboAbCdEfGhIjKlMnOpQrStUvWxYz1234567890abcdefghi",
				Signer:    true,
			},
		},
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// 选择开发环境。使用生产环境时，将 coboWaas2.DevEnv 替换为 coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// 替换 `<YOUR_PRIVATE_KEY>` 为您的私钥
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.WalletsMPCWalletsAPI.CreateKeyShareHolderGroup(ctx, vaultId).
		CreateKeyShareHolderGroupRequest(createKeyShareHolderGroupRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsMPCWalletsAPI.CreateKeyShareHolderGroup``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	} else {
		fmt.Fprintf(os.Stdout, "Response from `WalletsMPCWalletsAPI.CreateKeyShareHolderGroup`: %v\n", resp)
	}
}
```
```javascript JavaScript
const CoboWaas2 = require('@cobo/cobo-waas2');

// 初始化API客户端
const apiClient = CoboWaas2.ApiClient.instance;
// 选择开发环境。使用生产环境时，将 `Env.DEV` 替换为 `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// 替换 `<YOUR_PRIVATE_KEY>` 为您的私钥
apiClient.setPrivateKey('<YOUR_PRIVATE_KEY>');

// 调用API
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
const vault_id = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';

// 设置请求体
const createKeyShareHolderGroupRequest = new CoboWaas2.CreateKeyShareHolderGroupRequest({
    key_share_holder_group_type: 'MainGroup', // 或者 'SigningGroup', 'RecoveryGroup'
    participants: 3,
    threshold: 2,
    key_share_holders: [
        new CoboWaas2.CreateKeyShareHolder({
            name: 'Key Share Holder Name',
            type: 'CustomType',
            tss_node_id: 'coboAbCdEfGhIjKlMnOpQrStUvWxYz1234567890abcdefghi',
            signer: true
        })
    ]
});

// 调用API并处理响应
apiInstance.createKeyShareHolderGroup(vault_id, { CreateKeyShareHolderGroupRequest: createKeyShareHolderGroupRequest })
    .then(
        (data) => {
            console.log('API called successfully. Returned data: ' + data);
        },
        (error) => {
            console.error(error);
        }
    );
```
</RequestExample>