---
openapi: get /wallets/mpc/vaults/{vault_id}/key_share_holder_groups
---

<RequestExample>
```python Python

import cobo_waas2
from cobo_waas2.models.key_share_holder_group_type import KeyShareHolderGroupType
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    key_share_holder_group_type = KeyShareHolderGroupType.from_value("MainGroup")
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        api_response = api_instance.list_key_share_holder_groups(
            vault_id,
            key_share_holder_group_type=key_share_holder_group_type,
            limit=limit,
            before=before,
            after=after,
        )
        print("The response of WalletsMPCWalletsApi->list_key_share_holder_groups:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling WalletsMPCWalletsApi->list_key_share_holder_groups: %s\n"
            % e
        )

```
```java Java

import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    KeyShareHolderGroupType keyShareHolderGroupType = KeyShareHolderGroupType.fromValue("MainGroup");
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    
    try {
      ListKeyShareHolderGroups200Response result = apiInstance.listKeyShareHolderGroups(vaultId, keyShareHolderGroupType, limit, before, after);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsMpcWalletsApi#listKeyShareHolderGroups");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```
```go Go

package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	keyShareHolderGroupType := coboWaas2.KeyShareHolderGroupType("MainGroup")
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsMPCWalletsAPI.ListKeyShareHolderGroups(ctx, vaultId).
		KeyShareHolderGroupType(keyShareHolderGroupType).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `WalletsMPCWalletsAPI.ListKeyShareHolderGroups``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(
		os.Stdout,
		"Response from `WalletsMPCWalletsAPI.ListKeyShareHolderGroups`: %v\n",
		resp,
	)
}

```
```javascript JavaScript

const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
const vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  key_share_holder_group_type: CoboWaas2.KeyShareHolderGroupType.fromValue("MainGroup"),
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
};
apiInstance.listKeyShareHolderGroups(vault_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);

```
</RequestExample>