---
openapi: get /wallets/mpc/cobo_key_share_holders
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.key_share_holder import KeyShareHolder
from cobo_waas2.models.error_response import ErrorResponse
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Create a client
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)

    try:
        # Retrieve key share holders
        api_response = api_instance.list_cobo_key_holders()
        print("Key share holders retrieved successfully:")
        pprint(api_response)
    except ApiException as e:
        error = ErrorResponse.from_dict(e.body)
        print(f"Exception when calling API: {e.status}: {error.message}")

### Java Example:

```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.KeyShareHolder;
import com.cobo.waas2.model.ErrorResponse;

import java.util.List;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    
    try {
      List<KeyShareHolder> result = apiInstance.listCoboKeyHolders();
      System.out.println("Key share holders retrieved successfully:");
      System.out.println(result);
    } catch (ApiException e) {
      ErrorResponse error = ErrorResponse.from(e.getResponseBody());
      System.err.println("Exception when calling WalletsMpcWalletsApi#listCoboKeyHolders");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + error.getMessage());
      e.printStackTrace();
    }
  }
}

### JavaScript Example:

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsMPCWalletsAPI.ListCoboKeyHolders(ctx).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling API: %v\n", err)
		var errorResponse coboWaas2.ErrorResponse
		if err := json.NewDecoder(r.Body).Decode(&errorResponse); err == nil {
			fmt.Fprintf(os.Stderr, "Error message: %v\n", errorResponse.Message)
		}
		return
	}
	fmt.Fprintf(os.Stdout, "Key share holders retrieved successfully: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
apiInstance.listCoboKeyHolders().then(
  (data) => {
    console.log("Key share holders retrieved successfully:", data);
  },
  (error) => {
    console.error("Exception when calling API:", error.response.data.message);
  },
);

### Go Example:

```
</RequestExample>