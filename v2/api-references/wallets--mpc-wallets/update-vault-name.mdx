---
openapi: put /wallets/mpc/vaults/{vault_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.mpc_vault import MPCVault
from cobo_waas2.models.update_mpc_vault_by_id_request import UpdateMpcVaultByIdRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    update_mpc_vault_by_id_request = UpdateMpcVaultByIdRequest(name="The new name of the vault")

    try:
        api_response = api_instance.update_mpc_vault_by_id(
            vault_id, update_mpc_vault_by_id_request=update_mpc_vault_by_id_request
        )
        print("The response of WalletsMPCWalletsApi->update_mpc_vault_by_id:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsMPCWalletsApi->update_mpc_vault_by_id: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    UpdateMpcVaultByIdRequest updateMpcVaultByIdRequest = new UpdateMpcVaultByIdRequest();
    updateMpcVaultByIdRequest.setName("The new name of the vault");
    try {
      MPCVault result = apiInstance.updateMpcVaultById(vaultId, updateMpcVaultByIdRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsMpcWalletsApi#updateMpcVaultById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	updateMpcVaultByIdRequest := *coboWaas2.NewUpdateMpcVaultByIdRequest("The new name of the vault")

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsMPCWalletsAPI.UpdateMpcVaultById(ctx, vaultId).
		UpdateMpcVaultByIdRequest(updateMpcVaultByIdRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `WalletsMPCWalletsAPI.UpdateMpcVaultById``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsMPCWalletsAPI.UpdateMpcVaultById`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
const vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  UpdateMpcVaultByIdRequest: new CoboWaas2.UpdateMpcVaultByIdRequest({
      name: "The new name of the vault"
  }),
};
apiInstance.updateMpcVaultById(vault_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>