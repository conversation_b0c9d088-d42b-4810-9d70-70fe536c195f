---
openapi: get /wallets/mpc/vaults
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.mpc_vault_type import MPCVaultType
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration with your private key and the API host
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Create an API client context
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    
    # Define the required parameters
    vault_type = MPCVaultType("Org-Controlled")  # or "User-Controlled"
    project_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"  # project_id may not be necessary based on vault_type
    limit = 10
    before = "RqeEoTkg123456789"
    after = "RqeEoTkg987654321"

    try:
        # Calling the API
        api_response = api_instance.list_mpc_vaults(
            vault_type=vault_type, 
            project_id=project_id, 
            limit=limit, 
            before=before, 
            after=after
        )
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsMPCWalletsApi->list_mpc_vaults: %s" % e)
```
```java Java

```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	vaultType := coboWaas2.MPCVaultType("Org-Controlled") // Can be "User-Controlled"
	projectId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	limit := int32(10)
	before := "RqeEoTkg123456789"
	after := "RqeEoTkg987654321"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()
	
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.WalletsMPCWalletsAPI.ListMpcVaults(ctx).
		VaultType(vaultType).
		ProjectId(projectId).
		Limit(limit).
		Before(before).
		After(after).
		Execute()

	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsMPCWalletsAPI.ListMpcVaults`: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `WalletsMPCWalletsAPI.ListMpcVaults`: %v\n", resp)
}
```
```javascript JavaScript
```
</RequestExample>