---
openapi: post /wallets/mpc/vaults/{vault_id}/key_share_holder_groups/{key_share_holder_group_id}/delete
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.delete_key_share_holder_group_by_id201_response import (
    DeleteKeyShareHolderGroupById201Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    key_share_holder_group_id = "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce"

    try:
        api_response = api_instance.delete_key_share_holder_group_by_id(
            vault_id, key_share_holder_group_id
        )
        print(
            "The response of WalletsMPCWalletsApi->delete_key_share_holder_group_by_id:\n"
        )
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling WalletsMPCWalletsApi->delete_key_share_holder_group_by_id: %s\n"
            % e
        )
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    String keyShareHolderGroupId = "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce";
    try {
      DeleteKeyShareHolderGroupById201Response result =
          apiInstance.deleteKeyShareHolderGroupById(vaultId, keyShareHolderGroupId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println(
          "Exception when calling WalletsMpcWalletsApi#deleteKeyShareHolderGroupById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	keyShareHolderGroupId := "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsMPCWalletsAPI.DeleteKeyShareHolderGroupById(ctx, vaultId, keyShareHolderGroupId).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `WalletsMPCWalletsAPI.DeleteKeyShareHolderGroupById``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(
		os.Stdout,
		"Response from `WalletsMPCWalletsAPI.DeleteKeyShareHolderGroupById`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
const vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const key_share_holder_group_id = "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce";
apiInstance
  .deleteKeyShareHolderGroupById(vault_id, key_share_holder_group_id)
  .then(
    (data) => {
      console.log("API called successfully. Returned data: " + data);
    },
    (error) => {
      console.error(error);
    },
  );
```
</RequestExample>