---
openapi: post /wallets/mpc/vaults
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_mpc_vault_request import CreateMpcVaultRequest
from cobo_waas2.models.mpc_vault import MPCVault
from cobo_waas2.models.mpc_vault_type import MPCVaultType
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>", # Replace with your private key
    host="https://api.dev.cobo.com/v2"    # Use the development environment
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    
    # Define the createMpcVaultRequest
    create_mpc_vault_request = CreateMpcVaultRequest(
        name="My vault",
        vault_type=MPCVaultType("Org-Controlled") # use "User-Controlled" if needed
        # project_id can be supplied only if `vault_type` is `User-Controlled`
    )
    
    try:
        # Create vault
        api_response = api_instance.create_mpc_vault(create_mpc_vault_request=create_mpc_vault_request)
        print("The response of WalletsMPCWalletsApi->create_mpc_vault:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsMPCWalletsApi->create_mpc_vault: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    
    // Define the create vault request
    CreateMpcVaultRequest createMpcVaultRequest = new CreateMpcVaultRequest();
    createMpcVaultRequest.setName("My vault");
    createMpcVaultRequest.setVaultType(MPCVaultType.ORG_CONTROLLED); // Use MPCVaultType.USER_CONTROLLED if needed
    // Optionally set project_id if vault_type is USER_CONTROLLED
    // createMpcVaultRequest.setProjectId("0111039d-27fb-49ba-b172-6e0aa80e37ec");
    
    try {
      MPCVault result = apiInstance.createMpcVault(createMpcVaultRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsMpcWalletsApi#createMpcVault");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Define the create vault request
	createMpcVaultRequest := coboWaas2.CreateMpcVaultRequest{
		Name:      "My vault",
		VaultType: "Org-Controlled", // Use "User-Controlled" if needed
		// Optionally set ProjectId if VaultType is "User-Controlled"
		// ProjectId: "0111039d-27fb-49ba-b172-6e0aa80e37ec",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>", // Replace with your private key
	})
	resp, r, err := apiClient.WalletsMPCWalletsAPI.CreateMpcVault(ctx).
		CreateMpcVaultRequest(createMpcVaultRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `WalletsMPCWalletsAPI.CreateMpcVault``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateMpcVault`: MPCVault
	fmt.Fprintf(os.Stdout, "Response from `WalletsMPCWalletsAPI.CreateMpcVault`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV); // Use the development environment
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

// Define the create vault request
const createMpcVaultRequest = new CoboWaas2.CreateMpcVaultRequest();
createMpcVaultRequest.name = "My vault";
createMpcVaultRequest.vault_type = "Org-Controlled"; // Use "User-Controlled" if needed
// Optionally set project_id if vault_type is "User-Controlled"
// createMpcVaultRequest.project_id = "0111039d-27fb-49ba-b172-6e0aa80e37ec";

// Call the API
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
apiInstance.createMpcVault({ createMpcVaultRequest }).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error("Error: " + error);
  }
);
```
</RequestExample>