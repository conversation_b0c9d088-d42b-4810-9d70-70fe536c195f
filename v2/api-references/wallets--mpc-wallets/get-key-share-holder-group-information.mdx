---
openapi: get /wallets/mpc/vaults/{vault_id}/key_share_holder_groups/{key_share_holder_group_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.key_share_holder_group import KeyShareHolderGroup
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    key_share_holder_group_id = "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce"

    try:
        # Get key share holder group information
        api_response = api_instance.get_key_share_holder_group_by_id(vault_id, key_share_holder_group_id)
        print("The response of WalletsMPCWalletsApi->get_key_share_holder_group_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling WalletsMPCWalletsApi->get_key_share_holder_group_by_id: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    String keyShareHolderGroupId = "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce";
    try {
      KeyShareHolderGroup result = apiInstance.getKeyShareHolderGroupById(vaultId, keyShareHolderGroupId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsMpcWalletsApi#getKeyShareHolderGroupById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	keyShareHolderGroupId := "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsMPCWalletsAPI.GetKeyShareHolderGroupById(ctx, vaultId, keyShareHolderGroupId).Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `WalletsMPCWalletsAPI.GetKeyShareHolderGroupById``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `GetKeyShareHolderGroupById`: KeyShareHolderGroup
	fmt.Fprintf(
		os.Stdout,
		"Response from `WalletsMPCWalletsAPI.GetKeyShareHolderGroupById`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();
const vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const key_share_holder_group_id = "e8257ac8-76b8-4d1e-a1f9-eec4cb931dce";
apiInstance.getKeyShareHolderGroupById(vault_id, key_share_holder_group_id)
  .then(
    (data) => {
      console.log("API called successfully. Returned data: " + data);
    },
    (error) => {
      console.error(error);
    },
  );
```
</RequestExample>