---
openapi: put /wallets/mpc/projects/{project_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.mpc_project import MPCProject
from cobo_waas2.models.update_mpc_project_by_id_request import UpdateMpcProjectByIdRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configure API key authorization
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Replace with your private key
    host="https://api.dev.cobo.com/v2"     # Development environment
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.WalletsMPCWalletsApi(api_client)
    project_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"  # Project ID
    
    # Create the request body with the required name parameter
    update_mpc_project_by_id_request = UpdateMpcProjectByIdRequest(
        name="New project name"
    )

    try:
        # Call the API
        api_response = api_instance.update_mpc_project_by_id(
            project_id=project_id,
            update_mpc_project_by_id_request=update_mpc_project_by_id_request,
        )
        print("API called successfully. Returned data:")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling WalletsMPCWalletsApi->update_mpc_project_by_id: %s\n" % e)
```
```java Java
// Import necessary classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsMpcWalletsApi;
import com.cobo.waas2.model.*;

// Main class to execute the API
public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV); // Select the development environment
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

    WalletsMpcWalletsApi apiInstance = new WalletsMpcWalletsApi();
    UUID projectId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    
    // Create the request with the required name
    UpdateMpcProjectByIdRequest updateMpcProjectByIdRequest = new UpdateMpcProjectByIdRequest();
    updateMpcProjectByIdRequest.setName("New project name");

    try {
      MPCProject result = apiInstance.updateMpcProjectById(projectId, updateMpcProjectByIdRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling WalletsMpcWalletsApi#updateMpcProjectById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Define the project ID and request body
	projectId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	updateMpcProjectByIdRequest := *coboWaas2.NewUpdateMpcProjectByIdRequest("New project name")

	// Configure the API client
	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv) // Development environment
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>", // Replace with your private key
	})

	// Execute the API call
	resp, r, err := apiClient.WalletsMPCWalletsAPI.UpdateMpcProjectById(ctx, projectId).
		UpdateMpcProjectByIdRequest(updateMpcProjectByIdRequest).
		Execute()

	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `WalletsMPCWalletsAPI.UpdateMpcProjectById`: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
		return
	}

	// Process the response
	fmt.Fprintf(os.Stdout, "Response from `WalletsMPCWalletsAPI.UpdateMpcProjectById`: %v\n", resp)
}
```
```javascript JavaScript
// Import required modules and initialize the API client
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV); // Select the development environment
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>"); // Replace with your private key

// Define the API instance
const apiInstance = new CoboWaas2.WalletsMPCWalletsApi();

// Define the project ID and request body
const projectId = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  UpdateMpcProjectByIdRequest: {
    name: "New project name" // The required name property
  }
};

// Execute the API call
apiInstance.updateMpcProjectById(projectId, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>