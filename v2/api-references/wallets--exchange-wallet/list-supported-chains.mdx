---
openapi: get /wallets/exchanges/{exchange_id}/assets/{asset_id}/chains
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.chain_info import ChainInfo
from cobo_waas2.models.exchange_id import ExchangeId
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.WalletsExchangeWalletApi(api_client)
    exchange_id = cobo_waas2.ExchangeId()
    asset_id = "USDT"

    try:
        api_response = api_instance.list_supported_chains_for_exchange(
            exchange_id, asset_id
        )
        print("The response of WalletsExchangeWalletApi->list_supported_chains_for_exchange:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling WalletsExchangeWalletApi->list_supported_chains_for_exchange: %s\n"
            % e
        )
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.WalletsExchangeWalletApi;
import com.cobo.waas2.model.ChainInfo;
import com.cobo.waas2.model.ExchangeId;
import java.util.List;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    WalletsExchangeWalletApi apiInstance = new WalletsExchangeWalletApi();
    ExchangeId exchangeId = ExchangeId.fromValue("binance");
    String assetId = "USDT";
    try {
      List<ChainInfo> result = apiInstance.listSupportedChainsForExchange(exchangeId, assetId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println(
          "Exception when calling WalletsExchangeWalletApi#listSupportedChainsForExchange");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	exchangeId := coboWaas2.ExchangeId("binance")
	assetId := "USDT"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.WalletsExchangeWalletAPI.ListSupportedChainsForExchange(ctx, exchangeId, assetId).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `WalletsExchangeWalletAPI.ListSupportedChainsForExchange``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(
		os.Stdout,
		"Response from `WalletsExchangeWalletAPI.ListSupportedChainsForExchange`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.WalletsExchangeWalletApi();
const exchange_id = new CoboWaas2.ExchangeId();
const asset_id = "USDT";

apiInstance.listSupportedChainsForExchange(exchange_id, asset_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>