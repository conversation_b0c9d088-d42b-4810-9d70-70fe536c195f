---
openapi: get /webhooks/endpoints/{endpoint_id}/events
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_webhook_events200_response import (
    ListWebhookEvents200Response,
)
from cobo_waas2.models.webhook_event_status import WebhookEventStatus
from cobo_waas2.models.webhook_event_type import WebhookEventType
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    status = WebhookEventStatus.from_value("Success")
    type = WebhookEventType.from_value("wallets.transaction.updated")
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        api_response = api_instance.list_webhook_events(
            endpoint_id,
            status=status,
            type=type,
            limit=limit,
            before=before,
            after=after,
        )
        print("The response of DevelopersWebhooksApi->list_webhook_events:\n")
        pprint(api_response)
    except ApiException as e:
        print(
            "Exception when calling DevelopersWebhooksApi->list_webhook_events: %s\n"
            % e
        )
```
```java Java
// Import classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
    UUID endpointId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    WebhookEventStatus status = WebhookEventStatus.fromValue("Success");
    WebhookEventType type = WebhookEventType.fromValue("wallets.transaction.updated");
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    try {
      ListWebhookEvents200Response result =
          apiInstance.listWebhookEvents(endpointId, status, type, limit, before, after);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DevelopersWebhooksApi#listWebhookEvents");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	endpointId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	status := coboWaas2.WebhookEventStatus("Success")
	type_ := coboWaas2.WebhookEventType("wallets.transaction.updated")
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersWebhooksAPI.ListWebhookEvents(ctx, endpointId).
		Status(status).
		Type_(type_).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `DevelopersWebhooksAPI.ListWebhookEvents``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `DevelopersWebhooksAPI.ListWebhookEvents`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.DevelopersWebhooksApi();
const endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  status: CoboWaas2.WebhookEventStatus.fromValue("Success"),
  type: CoboWaas2.WebhookEventType.fromValue("wallets.transaction.updated"),
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
};
apiInstance.listWebhookEvents(endpoint_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>