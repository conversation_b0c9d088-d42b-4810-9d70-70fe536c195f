---
openapi: post /webhooks/endpoints/{endpoint_id}/events/{event_id}/retry
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.retry_webhook_event_by_id201_response import (
    RetryWebhookEventById201Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
    # Replace with your actual API key
    api_key="<YOUR_API_KEY>"
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    event_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"

    try:
        # Retry event
        api_response = api_instance.retry_webhook_event_by_id(event_id, endpoint_id)
        print("The response of DevelopersWebhooksApi->retry_webhook_event_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling DevelopersWebhooksApi->retry_webhook_event_by_id: %s\n"
            % e
        )
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

import java.util.UUID;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace with your actual API key
    defaultClient.setApiKey("<YOUR_API_KEY>");

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
    UUID eventId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    UUID endpointId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    try {
      RetryWebhookEventById201Response result = apiInstance.retryWebhookEventById(eventId, endpointId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DevelopersWebhooksApi#retryWebhookEventById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	eventId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	endpointId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_API_KEY>` and `<YOUR_PRIVATE_KEY>` with your credentials
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersWebhooksAPI.RetryWebhookEventById(ctx, eventId, endpointId).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `DevelopersWebhooksAPI.RetryWebhookEventById``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `RetryWebhookEventById`: RetryWebhookEventById201Response
	fmt.Fprintf(
		os.Stdout,
		"Response from `DevelopersWebhooksAPI.RetryWebhookEventById`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_API_KEY>` and `<YOUR_PRIVATE_KEY>` with your credentials
apiClient.setApiKey("<YOUR_API_KEY>");
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.DevelopersWebhooksApi();
const event_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
apiInstance.retryWebhookEventById(event_id, endpoint_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>