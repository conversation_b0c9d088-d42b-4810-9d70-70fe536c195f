---
openapi: post /webhooks/events/trigger
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.trigger_test_webhook_event201_response import TriggerTestWebhookEvent201Response
from cobo_waas2.models.trigger_test_webhook_event_request import TriggerTestWebhookEventRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    request = TriggerTestWebhookEventRequest(
        event_type="wallets.transaction.created",
        override_data={
            "chain_id": "ETH",
            "transaction_id": "Test-transaction-id"
        }
    )

    try:
        api_response = api_instance.trigger_test_webhook_event(
            trigger_test_webhook_event_request=request
        )
        pprint(api_response.triggered)
    except ApiException as e:
        print("Exception when calling DevelopersWebhooksApi->trigger_test_webhook_event: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

import java.util.HashMap;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);

    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
    TriggerTestWebhookEventRequest request = new TriggerTestWebhookEventRequest();

    request.setEventType("wallets.transaction.created");
    HashMap<String, Object> overrideData = new HashMap<>();
    overrideData.put("chain_id", "ETH");
    overrideData.put("transaction_id", "Test-transaction-id");
    request.setOverrideData(overrideData);

    try {
      TriggerTestWebhookEvent201Response result = apiInstance.triggerTestWebhookEvent(request);
      System.out.println(result.getTriggered());
    } catch (ApiException e) {
      System.err.println("Exception when calling DevelopersWebhooksApi#triggerTestWebhookEvent");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	request := *coboWaas2.NewTriggerTestWebhookEventRequest("wallets.transaction.created")
	overrideData := make(map[string]interface{})
	overrideData["chain_id"] = "ETH"
	overrideData["transaction_id"] = "Test-transaction-id"
	request.OverrideData = overrideData

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersWebhooksAPI.TriggerTestWebhookEvent(ctx).
		TriggerTestWebhookEventRequest(request).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `DevelopersWebhooksAPI.TriggerTestWebhookEvent``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
		return
	}

	fmt.Fprintf(os.Stdout, "Response from `DevelopersWebhooksAPI.TriggerTestWebhookEvent`: %v\n", resp.Triggered)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.DevelopersWebhooksApi();
const eventRequest = {
  event_type: "wallets.transaction.created",
  override_data: {
    chain_id: "ETH",
    transaction_id: "Test-transaction-id"
  }
};

apiInstance.triggerTestWebhookEvent({ TriggerTestWebhookEventRequest: eventRequest }).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>