---
openapi: put /webhooks/endpoints/{endpoint_id}
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.update_webhook_endpoint_by_id_request import UpdateWebhookEndpointByIdRequest
from cobo_waas2.models.webhook_endpoint import WebhookEndpoint
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    update_webhook_endpoint_by_id_request = UpdateWebhookEndpointByIdRequest(
        subscribed_events=[],
        status="STATUS_INACTIVE",
        description="My webhook endpoint"
    )

    try:
        # Update webhook endpoint
        api_response = api_instance.update_webhook_endpoint_by_id(
            endpoint_id,
            update_webhook_endpoint_by_id_request=update_webhook_endpoint_by_id_request,
        )
        print("The response of DevelopersWebhooksApi->update_webhook_endpoint_by_id:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling DevelopersWebhooksApi->update_webhook_endpoint_by_id: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
    UUID endpointId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    UpdateWebhookEndpointByIdRequest updateWebhookEndpointByIdRequest =
        new UpdateWebhookEndpointByIdRequest();
    updateWebhookEndpointByIdRequest.setSubscribedEvents(new ArrayList<WebhookEventType>());
    updateWebhookEndpointByIdRequest.setStatus("STATUS_INACTIVE");
    updateWebhookEndpointByIdRequest.setDescription("My webhook endpoint");

    try {
      WebhookEndpoint result =
          apiInstance.updateWebhookEndpointById(endpointId, updateWebhookEndpointByIdRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DevelopersWebhooksApi#updateWebhookEndpointById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	endpointId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	updateWebhookEndpointByIdRequest := *coboWaas2.NewUpdateWebhookEndpointByIdRequest()
	updateWebhookEndpointByIdRequest.SubscribedEvents = []string{}
	updateWebhookEndpointByIdRequest.Status = "STATUS_INACTIVE"
	updateWebhookEndpointByIdRequest.Description = "My webhook endpoint"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersWebhooksAPI.UpdateWebhookEndpointById(ctx, endpointId).
		UpdateWebhookEndpointByIdRequest(updateWebhookEndpointByIdRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `DevelopersWebhooksAPI.UpdateWebhookEndpointById``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `UpdateWebhookEndpointById`: WebhookEndpoint
	fmt.Fprintf(
		os.Stdout,
		"Response from `DevelopersWebhooksAPI.UpdateWebhookEndpointById`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.DevelopersWebhooksApi();
const endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  UpdateWebhookEndpointByIdRequest: new CoboWaas2.UpdateWebhookEndpointByIdRequest({
    subscribed_events: [],
    status: "STATUS_INACTIVE",
    description: "My webhook endpoint",
  }),
};
apiInstance.updateWebhookEndpointById(endpoint_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>