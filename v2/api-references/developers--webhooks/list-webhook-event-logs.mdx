---
openapi: get /webhooks/endpoints/{endpoint_id}/events/{event_id}/logs
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_webhook_event_logs200_response import (
    ListWebhookEventLogs200Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"  # UUID
    event_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"    # UUID
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        # List webhook event logs
        api_response = api_instance.list_webhook_event_logs(endpoint_id, event_id, limit=limit, before=before, after=after)
        print("The response of DevelopersWebhooksApi->list_webhook_event_logs:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling DevelopersWebhooksApi->list_webhook_event_logs: %s\n"
            % e
        )
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
        defaultClient.setEnv(Env.DEV);
        
        // Replace `<YOUR_PRIVATE_KEY>` with your private key
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
        UUID endpointId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
        UUID eventId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
        Integer limit = 10;
        String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
        String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
        
        try {
            ListWebhookEventLogs200Response result = apiInstance.listWebhookEventLogs(endpointId, eventId, limit, before, after);
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling DevelopersWebhooksApi#listWebhookEventLogs");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	eventId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"  // UUID
	endpointId := "f47ac10b-58cc-4372-a567-0e02b2c3d479" // UUID
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersWebhooksAPI.ListWebhookEventLogs(ctx, endpointId, eventId).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `DevelopersWebhooksAPI.ListWebhookEventLogs``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListWebhookEventLogs`: ListWebhookEventLogs200Response
	fmt.Fprintf(os.Stdout, "Response from `DevelopersWebhooksAPI.ListWebhookEventLogs`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.DevelopersWebhooksApi();
const endpoint_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";  // UUID
const event_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";    // UUID
const opts = {
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
};
apiInstance.listWebhookEventLogs(endpoint_id, event_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>