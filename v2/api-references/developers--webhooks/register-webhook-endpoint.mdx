---
openapi: post /webhooks/endpoints
---

<RequestExample>
```python Python
# Python Example using the appropriate API client

import cobo_waas2
from cobo_waas2.models.create_webhook_endpoint_request import CreateWebhookEndpointRequest
from cobo_waas2.models.webhook_endpoint import WebhookEndpoint
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    create_webhook_endpoint_request = CreateWebhookEndpointRequest(
        url="https://example.com/webhook",
        subscribed_events=["wallets.transaction.created", "wallets.transaction.updated"],
        description="My webhook endpoint"
    )

    try:
        api_response = api_instance.create_webhook_endpoint(create_webhook_endpoint_request=create_webhook_endpoint_request)
        print("The response of DevelopersWebhooksApi->create_webhook_endpoint:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling DevelopersWebhooksApi->create_webhook_endpoint: %s\n" % e)
```
```java Java
// Java Example using the appropriate API client

import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
    CreateWebhookEndpointRequest createWebhookEndpointRequest = new CreateWebhookEndpointRequest();
    createWebhookEndpointRequest.setUrl("https://example.com/webhook");
    createWebhookEndpointRequest.setSubscribedEvents(Arrays.asList("wallets.transaction.created", "wallets.transaction.updated"));
    createWebhookEndpointRequest.setDescription("My webhook endpoint");
    
    try {
      WebhookEndpoint result = apiInstance.createWebhookEndpoint(createWebhookEndpointRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DevelopersWebhooksApi#createWebhookEndpoint");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
// Go Example using the appropriate API client

package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	createWebhookEndpointRequest := coboWaas2.CreateWebhookEndpointRequest{
		Url:              "https://example.com/webhook",
		SubscribedEvents: []coboWaas2.WebhookEventType{"wallets.transaction.created", "wallets.transaction.updated"},
		Description:      "My webhook endpoint",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersWebhooksAPI.CreateWebhookEndpoint(ctx).
		CreateWebhookEndpointRequest(createWebhookEndpointRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `DevelopersWebhooksAPI.CreateWebhookEndpoint``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `DevelopersWebhooksAPI.CreateWebhookEndpoint`: %v\n", resp)
}
```
```javascript JavaScript
// JavaScript Example using Axios for HTTP requests

const axios = require('axios');

const createWebhookEndpoint = async () => {
  const url = 'https://api.dev.cobo.com/v2/webhooks/endpoints';
  const data = {
    url: 'https://example.com/webhook',
    subscribed_events: ['wallets.transaction.created', 'wallets.transaction.updated'],
    description: 'My webhook endpoint'
  };
  
  const config = {
    headers: {
      'Authorization': 'Bearer <YOUR_PRIVATE_KEY>',
      'Content-Type': 'application/json'
    }
  };
  
  try {
    const response = await axios.post(url, data, config);
    console.log('Webhook endpoint created successfully:', response.data);
  } catch (error) {
    console.error('Error creating webhook endpoint:', error.response ? error.response.data : error.message);
  }
};

createWebhookEndpoint();
```
</RequestExample>