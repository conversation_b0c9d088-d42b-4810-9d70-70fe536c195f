---
openapi: get /webhooks/endpoints
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_webhook_endpoints200_response import ListWebhookEndpoints200Response
from cobo_waas2.models.webhook_endpoint_status import WebhookEndpointStatus
from cobo_waas2.models.webhook_event_type import WebhookEventType
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration settings for the API client
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Replace `<YOUR_PRIVATE_KEY>` with your private key
    host="https://api.dev.cobo.com/v2"     # Change the URL to https://api.cobo.com/v2 for production
)

# Entering a context with an API client instance
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.DevelopersWebhooksApi(api_client)
    
    # Parameters
    status = WebhookEndpointStatus("STATUS_ACTIVE")
    event_type = WebhookEventType("wallets.transaction.created")
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        api_response = api_instance.list_webhook_endpoints(status=status, event_type=event_type, limit=limit, before=before, after=after)
        pprint(api_response)
    except ApiException as e:
        print(f"Exception when calling DevelopersWebhooksApi->list_webhook_endpoints: {e}")
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersWebhooksApi;
import com.cobo.waas2.model.*;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // Select the development environment. To use the production environment, replace `Env.DEV` with
        // `Env.PROD`
        defaultClient.setEnv(Env.DEV);

        // Replace `<YOUR_PRIVATE_KEY>` with your private key
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        
        DevelopersWebhooksApi apiInstance = new DevelopersWebhooksApi();
        
        // Parameters
        WebhookEndpointStatus status = WebhookEndpointStatus.fromValue("STATUS_ACTIVE");
        WebhookEventType eventType = WebhookEventType.fromValue("wallets.transaction.created");
        Integer limit = 10;
        String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
        String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";

        try {
            ListWebhookEndpoints200Response result = apiInstance.listWebhookEndpoints(status, eventType, limit, before, after);
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling DevelopersWebhooksApi#listWebhookEndpoints");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Define the parameters
	status := coboWaas2.WebhookEndpointStatus("STATUS_ACTIVE")
	eventType := coboWaas2.WebhookEventType("wallets.transaction.created")
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. For production, replace `coboWaas2.DevEnv` with `coboWaas2.ProdEnv`.
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Set your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	// Execute the API call
	resp, r, err := apiClient.DevelopersWebhooksAPI.ListWebhookEndpoints(ctx).
		Status(status).
		EventType(eventType).
		Limit(limit).
		Before(before).
		After(after).
		Execute()

	if err != nil {
		fmt.Fprintf(os.Stderr, "Error calling `DevelopersWebhooksAPI.ListWebhookEndpoints`: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}

	// Print the successful response
	fmt.Fprintf(os.Stdout, "Response from `DevelopersWebhooksAPI.ListWebhookEndpoints`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. For production use, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);

// Replace `<YOUR_PRIVATE_KEY>` with your actual private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Calling the API
const apiInstance = new CoboWaas2.DevelopersWebhooksApi();
const opts = {
  status: CoboWaas2.WebhookEndpointStatus.STATUS_ACTIVE,
  event_type: CoboWaas2.WebhookEventType.WALLETS_TRANSACTION_CREATED,
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
};

apiInstance.listWebhookEndpoints(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: ", data);
  },
  (error) => {
    console.error("Error calling API: ", error);
  }
);
```
</RequestExample>