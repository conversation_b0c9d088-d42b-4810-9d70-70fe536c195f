---
openapi: post /developers/callback_messages/{message_id}/retry
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.retry_callback_message201_response import (
    RetryCallbackMessage201Response,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# Configuration for API client
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Context management for API client
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.DevelopersApi(api_client)
    message_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"  # example value from specs

    try:
        api_response = api_instance.retry_callback_message(message_id)
        print("The response of DevelopersApi->retry_callback_message:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling DevelopersApi->retry_callback_message: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersApi;
import com.cobo.waas2.model.RetryCallbackMessage201Response;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    DevelopersApi apiInstance = new DevelopersApi();
    String messageId = "f47ac10b-58cc-4372-a567-0e02b2c3d479"; // example value from specs
    try {
      RetryCallbackMessage201Response result = apiInstance.retryCallbackMessage(messageId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DevelopersApi#retryCallbackMessage");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	messageId := "f47ac10b-58cc-4372-a567-0e02b2c3d479" // example value from specs

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersAPI.RetryCallbackMessage(ctx, messageId).Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `DevelopersAPI.RetryCallbackMessage``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `DevelopersAPI.RetryCallbackMessage`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.DevelopersApi();
const message_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";  // example value from specs
apiInstance.retryCallbackMessage(message_id).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>