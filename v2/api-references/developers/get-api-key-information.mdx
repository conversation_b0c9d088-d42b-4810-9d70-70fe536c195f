---
openapi: get /developers/api_key_info
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.get_api_key_info200_response import GetApiKeyInfo200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.DevelopersApi(api_client)

    try:
        # Get API key information
        api_response = api_instance.get_api_key_info()
        print("The response of DevelopersApi->get_api_key_info:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DevelopersApi->get_api_key_info: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.DevelopersApi;
import com.cobo.waas2.model.GetApiKeyInfo200Response;

public class Example {
    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
        defaultClient.setEnv(Env.DEV);

        // Replace `<YOUR_PRIVATE_KEY>` with your private key
        defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
        DevelopersApi apiInstance = new DevelopersApi();
        try {
            GetApiKeyInfo200Response result = apiInstance.getApiKeyInfo();
            System.out.println(result);
        } catch (ApiException e) {
            System.err.println("Exception when calling DevelopersApi#getApiKeyInfo");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.DevelopersAPI.GetApiKeyInfo(ctx).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `DevelopersAPI.GetApiKeyInfo``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `GetApiKeyInfo`: GetApiKeyInfo200Response
	fmt.Fprintf(os.Stdout, "Response from `DevelopersAPI.GetApiKeyInfo`: %v\n", resp)
}
```
```javascript JavaScript
```
</RequestExample>