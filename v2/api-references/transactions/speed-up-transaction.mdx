---
openapi: post /transactions/{transaction_id}/speedup
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.transaction_request_fee import (
    TransactionRequestEvmEip1559Fee,
)
from cobo_waas2.models.transaction_rbf import TransactionRbf
from cobo_waas2.models.mpc_transfer_source import MpcTransferSource
from cobo_waas2.models.auto_fuel_type import AutoFuelType
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.TransactionsApi(api_client)
    transaction_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    
    fee = TransactionRequestEvmEip1559Fee(
        max_fee_per_gas="9000000000000",
        max_priority_fee_per_gas="1000000000000",
        fee_type="Fixed",
        currency="ETH"
    )
    
    source = MpcTransferSource(
        source_type="Org-Controlled",
        wallet_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        address="**********************************"
    )
    
    transaction_rbf = TransactionRbf(
        request_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        fee=fee,
        source=source,
        category_names=["Trading"],
        description="Speedup transaction",
        auto_fuel=AutoFuelType.PASSIVE_AUTO_FUEL
    )

    try:
        api_response = api_instance.speedup_transaction_by_id(
            transaction_id, transaction_rbf=transaction_rbf
        )
        print("The response of TransactionsApi->speedup_transaction_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling TransactionsApi->speedup_transaction_by_id: %s\n"
            % e
        )
```
```java Java
// Import necessary classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class EnhancedExample {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);

    // Replace <YOUR_PRIVATE_KEY> with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();
    UUID transactionId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");

    // Create a TransactionRbf object
    TransactionRbf transactionRbf = new TransactionRbf();
    transactionRbf.setRequestId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    TransactionRequestEvmEip1559Fee fee = new TransactionRequestEvmEip1559Fee("9000000000000", "1000000000000", "Fixed", "ETH");
    transactionRbf.setFee(fee);
    
    MpcTransferSource source = new MpcTransferSource();
    source.setSourceType(WalletSubtype.ORG_CONTROLLED);
    source.setWalletId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    source.setAddress("**********************************");
    transactionRbf.setSource(source);

    transactionRbf.setCategoryNames(Arrays.asList("Trading"));
    transactionRbf.setDescription("Speedup transaction");
    transactionRbf.setAutoFuel(AutoFuelType.PASSIVE_AUTO_FUEL);

    try {
      CreateTransferTransaction201Response result = apiInstance.speedupTransactionById(transactionId, transactionRbf);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#speedupTransactionById");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	transactionId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	fee := coboWaas2.NewTransactionRequestEvmEip1559Fee("9000000000000", "1000000000000", coboWaas2.FeeType("Fixed"), "ETH")
	source := coboWaas2.MpcTransferSource{
		SourceType: coboWaas2.WalletSubtype("Org-Controlled"),
		WalletId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		Address: "**********************************",
	}

	transactionRbf := coboWaas2.TransactionRbf{
		RequestId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		Fee: fee,
		Source: &source,
		CategoryNames: []string{"Trading"},
		Description: "Speedup transaction",
		AutoFuel: coboWaas2.AutoFuelType("PassiveAutoFuel"),
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.TransactionsAPI.SpeedupTransactionById(ctx, transactionId).
		TransactionRbf(transactionRbf).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TransactionsAPI.SpeedupTransactionById``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `TransactionsAPI.SpeedupTransactionById`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.TransactionsApi();
const transaction_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

const fee = new CoboWaas2.TransactionRequestEvmEip1559Fee({
  maxFeePerGas: "9000000000000",
  maxPriorityFeePerGas: "1000000000000",
  feeType: "Fixed",
  currency: "ETH",
});

const source = new CoboWaas2.MpcTransferSource({
  sourceType: CoboWaas2.WalletSubtype.ORG_CONTROLLED,
  walletId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  address: "**********************************",
});

const opts = {
  TransactionRbf: new CoboWaas2.TransactionRbf({
    requestId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
    fee: fee,
    source: source,
    categoryNames: ["Trading"],
    description: "Speedup transaction",
    autoFuel: CoboWaas2.AutoFuelType.PASSIVE_AUTO_FUEL,
  }),
};

apiInstance.speedupTransactionById(transaction_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>