---
openapi: post /transactions/contract_call
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.contract_call_params import ContractCallParams
from cobo_waas2.models.create_transfer_transaction201_response import CreateTransferTransaction201Response
from cobo_waas2.models.mpc_contract_call_source import MpcContractCallSource
from cobo_waas2.models.evm_contract_call_destination import EvmContractCallDestination
from cobo_waas2.models.transaction_request_fixed_fee import TransactionRequestFixedFee
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.TransactionsApi(api_client)
    contract_call_params = ContractCallParams(
        request_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        chain_id="ETH",
        source=MpcContractCallSource(), # Configure the source type
        destination=EvmContractCallDestination(), # Configure the destination type
        description="Transaction to call a smart contract initiated from a wallet",
        category_names=["Trading"],
        fee=TransactionRequestFixedFee(), # Configure the fee type
        transaction_process_type="AutoProcess",
        auto_fuel="PassiveAutoFuel"
    )

    try:
        api_response = api_instance.create_contract_call_transaction(contract_call_params=contract_call_params)
        print("The response of TransactionsApi->create_contract_call_transaction:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling TransactionsApi->create_contract_call_transaction: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();

    ContractCallParams contractCallParams = new ContractCallParams();
    contractCallParams.setRequestId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    contractCallParams.setChainId("ETH");
    contractCallParams.setSource(new MpcContractCallSource()); // Example: new OrgControlledSource(...)
    contractCallParams.setDestination(new EvmContractCallDestination()); // Example: new EvmContractCallDestination(...)
    contractCallParams.setDescription("Transaction to call a smart contract initiated from a wallet");
    contractCallParams.setCategoryNames(Arrays.asList("Trading"));
    contractCallParams.setFee(new TransactionRequestFixedFee()); // Configure appropriate fee
    contractCallParams.setTransactionProcessType("AutoProcess");
    contractCallParams.setAutoFuel("PassiveAutoFuel");

    try {
      CreateTransferTransaction201Response result = apiInstance.createContractCallTransaction(contractCallParams);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#createContractCallTransaction");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	contractCallParams := *coboWaas2.NewContractCallParams(
		"f47ac10b-58cc-4372-a567-0e02b2c3d479",
		"ETH",
		coboWaas2.ContractCallSource{
			MpcContractCallSource: coboWaas2.NewMpcContractCallSource(
				coboWaas2.ContractCallSourceType("Org-Controlled"),
				"f47ac10b-58cc-4372-a567-0e02b2c3d479",
				"******************************************",
			),
		},
		coboWaas2.ContractCallDestination{
			EvmContractCallDestination: coboWaas2.NewEvmContractCallDestination(
				coboWaas2.ContractCallDestinationType("EVM_Contract"),
				"******************************************",
				"0xa22cb4650000000000000000000000001e0049783f008a0085193e00003d00cd54003c71000000000000000000000000000000000000000000000000000000000000DEMO",
			),
		},
	)
	
	contractCallParams.SetDescription("Transaction to call a smart contract initiated from a wallet")
	contractCallParams.SetCategoryNames([]string{"Trading"})
	contractCallParams.SetFee(coboWaas2.TransactionRequestFixedFee{})
	contractCallParams.SetTransactionProcessType("AutoProcess")
	contractCallParams.SetAutoFuel("PassiveAutoFuel")

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.CreateContractCallTransaction(ctx).
		ContractCallParams(contractCallParams).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TransactionsAPI.CreateContractCallTransaction``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(
		os.Stdout,
		"Response from `TransactionsAPI.CreateContractCallTransaction`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.TransactionsApi();

const contractCallParams = new CoboWaas2.ContractCallParams();
contractCallParams.request_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
contractCallParams.chain_id = "ETH";
contractCallParams.source = new CoboWaas2.MpcContractCallSource(); // Configure source
contractCallParams.destination = new CoboWaas2.EvmContractCallDestination(); // Configure destination
contractCallParams.description = "Transaction to call a smart contract initiated from a wallet";
contractCallParams.category_names = ["Trading"];
contractCallParams.fee = new CoboWaas2.TransactionRequestFixedFee(); // Configure fee
contractCallParams.transaction_process_type = "AutoProcess";
contractCallParams.auto_fuel = "PassiveAutoFuel";

const opts = { ContractCallParams: contractCallParams };
apiInstance.createContractCallTransaction(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>