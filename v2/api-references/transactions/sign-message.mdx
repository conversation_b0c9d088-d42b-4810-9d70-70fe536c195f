---
openapi: post /transactions/message_sign
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_transfer_transaction201_response import (
    CreateTransferTransaction201Response,
)
from cobo_waas2.models.message_sign_params import MessageSignParams
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.TransactionsApi()

    # Define message sign parameters
    message_sign_params = {
        "request_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
        "chain_id": "ETH",
        "source": {},  # Add properties based on actual schema
        "destination": {},  # Add properties based on actual schema
        "description": "Transaction to sign a message initiated from a wallet",
        "category_names": ["Trading"]
    }

    try:
        # Sign message
        api_response = api_instance.create_message_sign_transaction(
            message_sign_params=message_sign_params
        )
        print("The response of TransactionsApi->create_message_sign_transaction:\n")
        pprint(api_response)
    except Exception as e:
        print(
            "Exception when calling TransactionsApi->create_message_sign_transaction: %s\n"
            % e
        )
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();

    // Create an instance of MessageSignParams and populate it
    MessageSignParams messageSignParams = new MessageSignParams()
        .requestId("f47ac10b-58cc-4372-a567-0e02b2c3d479")
        .chainId("ETH")
        .description("Transaction to sign a message initiated from a wallet")
        .addCategoryNamesItem("Trading");

    // Add the source and destination properties based on the actual schema
    messageSignParams.source(new MessageSignSource());
    messageSignParams.destination(new MessageSignDestination());

    try {
      CreateTransferTransaction201Response result =
          apiInstance.createMessageSignTransaction(messageSignParams);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#createMessageSignTransaction");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Define message sign parameters
	messageSignParams := coboWaas2.MessageSignParams{
		RequestId:     "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		ChainId:       "ETH",
		Description:   "Transaction to sign a message initiated from a wallet",
		CategoryNames: []string{"Trading"},
		Source:        coboWaas2.MessageSignSource{},  // Add properties based on actual schema
		Destination:   coboWaas2.MessageSignDestination{},  // Add properties based on actual schema
	}

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.CreateMessageSignTransaction(ctx).
		MessageSignParams(messageSignParams).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TransactionsAPI.CreateMessageSignTransaction``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateMessageSignTransaction`: CreateTransferTransaction201Response
	fmt.Fprintf(
		os.Stdout,
		"Response from `TransactionsAPI.CreateMessageSignTransaction`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Define the message sign parameters
const messageSignParams = {
  request_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  chain_id: "ETH",
  source: {}, // Add properties based on actual schema
  destination: {}, // Add properties based on actual schema
  description: "Transaction to sign a message initiated from a wallet",
  category_names: ["Trading"],
};

// Call the API
const apiInstance = new CoboWaas2.TransactionsApi();
apiInstance.createMessageSignTransaction({ MessageSignParams: messageSignParams }).then(
  (data) => {
    console.log("API called successfully. Returned data: " + JSON.stringify(data));
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>