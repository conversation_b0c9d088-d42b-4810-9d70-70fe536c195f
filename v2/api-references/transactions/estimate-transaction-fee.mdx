---
openapi: post /transactions/estimate_fee
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models import EstimateTransferFeeParams, EstimatedFee, TransferSource, TransferDestination, FeeType, EstimateFeeRequestType
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.TransactionsApi(api_client)

    transfer_params = EstimateTransferFeeParams(
        request_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        request_type=EstimateFeeRequestType.TRANSFER,
        source=TransferSource(...),  # Replace with actual initialization
        token_id="ETH_USDT",
        destination=TransferDestination(...),  # Replace with actual initialization
        fee_type=FeeType.SLOW,
    )

    try:
        api_response = api_instance.estimate_fee(estimate_fee_params=transfer_params)
        print("The response of TransactionsApi->estimate_fee:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling TransactionsApi->estimate_fee: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();

    // Create a Transfer fee params object
    EstimateTransferFeeParams transferParams = new EstimateTransferFeeParams();
    transferParams.setRequestId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    transferParams.setRequestType(EstimateFeeRequestType.Transfer);
    transferParams.setSource(new TransferSource().setXXX("...")); // Initialize with specific values
    transferParams.setTokenId("ETH_USDT");
    transferParams.setDestination(new TransferDestination().setXXX("...")); // Initialize with specific values
    transferParams.setFeeType(FeeType.Slow);

    try {
      EstimatedFee result = apiInstance.estimateFee(transferParams);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#estimateFee");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	transferParams := coboWaas2.EstimateTransferFeeParams{
		RequestId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
		RequestType: coboWaas2.EstimateFeeRequestTypeTransfer,
		Source: coboWaas2.TransferSource{},  // Replace with actual initialization
		TokenId: "ETH_USDT",
		Destination: coboWaas2.TransferDestination{},  // Replace with actual initialization
		FeeType: coboWaas2.FeeTypeSlow,
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.EstimateFee(ctx).
		EstimateFeeParams(transferParams).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `TransactionsAPI.EstimateFee``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `TransactionsAPI.EstimateFee`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.TransactionsApi();

const transferParams = new CoboWaas2.EstimateTransferFeeParams(
  "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  CoboWaas2.EstimateFeeRequestType.TRANSFER,
  new CoboWaas2.TransferSource(...),  // Initialize with actual values
  "ETH_USDT",
  new CoboWaas2.TransferDestination(...),  // Initialize with actual values
  CoboWaas2.FeeType.SLOW
);

apiInstance.estimateFee({ estimateFeeParams: transferParams }).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>