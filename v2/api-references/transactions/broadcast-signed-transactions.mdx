---
openapi: post /transactions/broadcast
---

<RequestExample>
```python Python
# Import necessary libraries
import cobo_waas2
from cobo_waas2.models.broadcast_signed_transactions201_response_inner import BroadcastSignedTransactions201ResponseInner
from cobo_waas2.models.broadcast_signed_transactions_request import BroadcastSignedTransactionsRequest
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",  # Replace with your private key
    host="https://api.dev.cobo.com/v2"  # Select the development environment. Change to production as needed.
)

# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.TransactionsApi(api_client)
    
    # Construct the request body
    broadcast_signed_transactions_request = BroadcastSignedTransactionsRequest()
    # Populate broadcast_signed_transactions_request with necessary data

    try:
        # Broadcast signed transactions
        api_response = api_instance.broadcast_signed_transactions(
            broadcast_signed_transactions_request=broadcast_signed_transactions_request
        )
        print("The response of TransactionsApi->broadcast_signed_transactions:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling TransactionsApi->broadcast_signed_transactions: %s\n" % e)
```
```java Java
// Import classes
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

import java.util.List;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();
    
    // Construct the request body
    BroadcastSignedTransactionsRequest broadcastSignedTransactionsRequest = new BroadcastSignedTransactionsRequest();
    // Populate broadcastSignedTransactionsRequest with necessary data

    try {
      List<BroadcastSignedTransactions201ResponseInner> result =
          apiInstance.broadcastSignedTransactions(broadcastSignedTransactionsRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#broadcastSignedTransactions");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	// Construct the request body
	broadcastSignedTransactionsRequest := *coboWaas2.NewBroadcastSignedTransactionsRequest()
	// Populate broadcastSignedTransactionsRequest with necessary data

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.BroadcastSignedTransactions(ctx).
		BroadcastSignedTransactionsRequest(broadcastSignedTransactionsRequest).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TransactionsAPI.BroadcastSignedTransactions``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `BroadcastSignedTransactions`: []BroadcastSignedTransactions201ResponseInner
	fmt.Fprintf(
		os.Stdout,
		"Response from `TransactionsAPI.BroadcastSignedTransactions`: %v\n",
		resp,
	)
}
```
```javascript JavaScript
// Import the necessary module
const CoboWaas2 = require("@cobo/cobo-waas2");

// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

// Call the API
const apiInstance = new CoboWaas2.TransactionsApi();

// Construct the request body
const broadcastRequest = new CoboWaas2.BroadcastSignedTransactionsRequest();
// Populate broadcastRequest with necessary data

const opts = {
  BroadcastSignedTransactionsRequest: broadcastRequest
};

apiInstance.broadcastSignedTransactions(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>