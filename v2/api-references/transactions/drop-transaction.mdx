---
openapi: post /transactions/{transaction_id}/drop
---

<RequestExample>
```python Python
# Python code example with all parameters

import cobo_waas2
from cobo_waas2.models import TransactionRbf, TransactionRequestFee
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.TransactionsApi(api_client)
    transaction_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    transaction_rbf = TransactionRbf(
        request_id="unique-request-id",
        fee=TransactionRequestFee(),  # You need to create and set this appropriately
        description="Speedup transaction"
    )

    try:
        api_response = api_instance.drop_transaction_by_id(
            transaction_id, transaction_rbf=transaction_rbf
        )
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling TransactionsApi->drop_transaction_by_id: %s\n" % e)
```
```java Java
public class TransactionRbf {
    private String requestId;
    private TransactionRequestFee fee;
    private MpcTransferSource source;
    private List<String> categoryNames;
    private String description;
    private AutoFuelType autoFuel;

    // getters and setters
}
```
```go Go
// Go code example with all parameters

package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	transactionId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	transactionRbf := coboWaas2.TransactionRbf{
		RequestId: "unique-request-id",
		Fee:       coboWaas2.TransactionRequestFee{}, // Configure this as needed
		Description: "Speedup transaction",
	}

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.DropTransactionById(ctx, transactionId).
		TransactionRbf(transactionRbf).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `TransactionsAPI.DropTransactionById`: %v\n", err)
	}
	fmt.Fprintf(os.Stdout, "Response from `TransactionsAPI.DropTransactionById`: %v\n", resp)
}
```
```javascript JavaScript
// JavaScript code example with all parameters

const CoboWaas2 = require("@cobo/cobo-waas2");
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
const apiInstance = new CoboWaas2.TransactionsApi();
const transaction_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const opts = {
  TransactionRbf: new CoboWaas2.TransactionRbf({
    requestId: "unique-request-id",
    fee: new CoboWaas2.TransactionRequestFee(), // Configure as needed
    description: "Speedup transaction"
  })
};
apiInstance.dropTransactionById(transaction_id, opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  }
);
```
</RequestExample>