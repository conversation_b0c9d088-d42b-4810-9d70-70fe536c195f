---
openapi: get /transactions/check_loop_transfers
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.check_loop_transfers200_response_inner import (
    CheckLoopTransfers200ResponseInner,
)
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.TransactionsApi(api_client)
    token_id = "ETH_USDT"
    source_wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    destination_addresses = "******************************************,******************************************"

    try:
        # Check Cobo Loop transfers
        api_response = api_instance.check_loop_transfers(
            token_id, source_wallet_id, destination_addresses
        )
        print("The response of TransactionsApi->check_loop_transfers:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling TransactionsApi->check_loop_transfers: %s\n" % e)
```
```java Java
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();
    String tokenId = "ETH_USDT";
    UUID sourceWalletId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    String destinationAddresses = 
        "******************************************,******************************************";
    try {
      List<CheckLoopTransfers200ResponseInner> result =
          apiInstance.checkLoopTransfers(tokenId, sourceWalletId, destinationAddresses);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#checkLoopTransfers");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	tokenId := "ETH_USDT"
	sourceWalletId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	destinationAddresses := "******************************************,******************************************"

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.CheckLoopTransfers(ctx).
		TokenId(tokenId).
		SourceWalletId(sourceWalletId).
		DestinationAddresses(destinationAddresses).
		Execute()
	if err != nil {
		fmt.Fprintf(
			os.Stderr,
			"Error when calling `TransactionsAPI.CheckLoopTransfers``: %v\n",
			err,
		)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	fmt.Fprintf(os.Stdout, "Response from `TransactionsAPI.CheckLoopTransfers`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.TransactionsApi();
const token_id = "ETH_USDT";
const source_wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
const destination_addresses =
  "******************************************,******************************************";
apiInstance
  .checkLoopTransfers(token_id, source_wallet_id, destination_addresses)
  .then(
    (data) => {
      console.log("API called successfully. Returned data: " + data);
    },
    (error) => {
      console.error(error);
    },
  );
```
</RequestExample>