---
openapi: get /transactions
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.list_transactions200_response import ListTransactions200Response
from cobo_waas2.rest import ApiException
from pprint import pprint

# See configuration.py for a list of all supported configurations.
configuration = cobo_waas2.Configuration(
    # Replace `<YOUR_PRIVATE_KEY>` with your private key
    api_private_key="<YOUR_PRIVATE_KEY>",
    # Select the development environment. To use the production environment, change the URL to https://api.cobo.com/v2.
    host="https://api.dev.cobo.com/v2",
)
# Enter a context with an instance of the API client
with cobo_waas2.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = cobo_waas2.TransactionsApi(api_client)
    request_id = "web_send_by_user_327_1610444045047"
    cobo_ids = "20231213122855000000000000000000,20231213122955000000000000000000"
    transaction_ids = "f47ac10b-58cc-4372-a567-0e02b2c3d479,557918d2-632a-4fe1-932f-315711f05fe3"
    transaction_hashes = "239861be9a4afe080c359b7fe4a1d035945ec46256b1a0f44d1267c71de8ec28"
    types = "Deposit,Withdrawal"
    statuses = "Completed,Failed"
    wallet_ids = "f47ac10b-58cc-4372-a567-0e02b2c3d479,1ddca562-8434-41c9-8809-d437bad9c868"
    chain_ids = "BTC,ETH"
    token_ids = "ETH_USDT,ETH_USDC"
    asset_ids = "USDT,USDC"
    vault_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    project_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479"
    min_created_timestamp = 1635744000000
    max_created_timestamp = 1635744000000
    limit = 10
    before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
    after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

    try:
        # List all transactions
        api_response = api_instance.list_transactions(
            request_id=request_id,
            cobo_ids=cobo_ids,
            transaction_ids=transaction_ids,
            transaction_hashes=transaction_hashes,
            types=types,
            statuses=statuses,
            wallet_ids=wallet_ids,
            chain_ids=chain_ids,
            token_ids=token_ids,
            asset_ids=asset_ids,
            vault_id=vault_id,
            project_id=project_id,
            min_created_timestamp=min_created_timestamp,
            max_created_timestamp=max_created_timestamp,
            limit=limit,
            before=before,
            after=after,
        )
        print("The response of TransactionsApi->list_transactions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling TransactionsApi->list_transactions: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    // Select the development environment. To use the production environment, replace `Env.DEV` with
    // `Env.PROD
    defaultClient.setEnv(Env.DEV);

    // Replace `<YOUR_PRIVATE_KEY>` with your private key
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    TransactionsApi apiInstance = new TransactionsApi();
    String requestId = "web_send_by_user_327_1610444045047";
    String coboIds = "20231213122855000000000000000000,20231213122955000000000000000000";
    String transactionIds = "f47ac10b-58cc-4372-a567-0e02b2c3d479,557918d2-632a-4fe1-932f-315711f05fe3";
    String transactionHashes = "239861be9a4afe080c359b7fe4a1d035945ec46256b1a0f44d1267c71de8ec28";
    String types = "Deposit,Withdrawal";
    String statuses = "Completed,Failed";
    String walletIds = "f47ac10b-58cc-4372-a567-0e02b2c3d479,1ddca562-8434-41c9-8809-d437bad9c868";
    String chainIds = "BTC,ETH";
    String tokenIds = "ETH_USDT,ETH_USDC";
    String assetIds = "USDT,USDC";
    UUID vaultId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    UUID projectId = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    Long minCreatedTimestamp = 1635744000000L;
    Long maxCreatedTimestamp = 1635744000000L;
    Integer limit = 10;
    String before = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1";
    String after = "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk";
    try {
      ListTransactions200Response result = apiInstance.listTransactions(
          requestId,
          coboIds,
          transactionIds,
          transactionHashes,
          types,
          statuses,
          walletIds,
          chainIds,
          tokenIds,
          assetIds,
          vaultId,
          projectId,
          minCreatedTimestamp,
          maxCreatedTimestamp,
          limit,
          before,
          after);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#listTransactions");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	requestId := "web_send_by_user_327_1610444045047"
	coboIds := "20231213122855000000000000000000,20231213122955000000000000000000"
	transactionIds := "f47ac10b-58cc-4372-a567-0e02b2c3d479,557918d2-632a-4fe1-932f-315711f05fe3"
	transactionHashes := "239861be9a4afe080c359b7fe4a1d035945ec46256b1a0f44d1267c71de8ec28"
	types := "Deposit,Withdrawal"
	statuses := "Completed,Failed"
	walletIds := "f47ac10b-58cc-4372-a567-0e02b2c3d479,1ddca562-8434-41c9-8809-d437bad9c868"
	chainIds := "BTC,ETH"
	tokenIds := "ETH_USDT,ETH_USDC"
	assetIds := "USDT,USDC"
	vaultId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	projectId := "f47ac10b-58cc-4372-a567-0e02b2c3d479"
	minCreatedTimestamp := int64(1635744000000)
	maxCreatedTimestamp := int64(1635744000000)
	limit := int32(10)
	before := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1"
	after := "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"

	configuration := coboWaas2.NewConfiguration()
	// Initialize the API client
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	// Select the development environment. To use the production environment, replace coboWaas2.DevEnv with coboWaas2.ProdEnv
	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	// Replace `<YOUR_PRIVATE_KEY>` with your private key
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})
	resp, r, err := apiClient.TransactionsAPI.ListTransactions(ctx).
		RequestId(requestId).
		CoboIds(coboIds).
		TransactionIds(transactionIds).
		TransactionHashes(transactionHashes).
		Types(types).
		Statuses(statuses).
		WalletIds(walletIds).
		ChainIds(chainIds).
		TokenIds(tokenIds).
		AssetIds(assetIds).
		VaultId(vaultId).
		ProjectId(projectId).
		MinCreatedTimestamp(minCreatedTimestamp).
		MaxCreatedTimestamp(maxCreatedTimestamp).
		Limit(limit).
		Before(before).
		After(after).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `TransactionsAPI.ListTransactions``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ListTransactions`: ListTransactions200Response
	fmt.Fprintf(os.Stdout, "Response from `TransactionsAPI.ListTransactions`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");
// Initialize the API client
const apiClient = CoboWaas2.ApiClient.instance;
// Select the development environment. To use the production environment, replace `Env.DEV` with `Env.PROD`
apiClient.setEnv(CoboWaas2.Env.DEV);
// Replace `<YOUR_PRIVATE_KEY>` with your private key
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");
// Call the API
const apiInstance = new CoboWaas2.TransactionsApi();
const opts = {
  request_id: "web_send_by_user_327_1610444045047",
  cobo_ids: "20231213122855000000000000000000,20231213122955000000000000000000",
  transaction_ids: "f47ac10b-58cc-4372-a567-0e02b2c3d479,557918d2-632a-4fe1-932f-315711f05fe3",
  transaction_hashes: "239861be9a4afe080c359b7fe4a1d035945ec46256b1a0f44d1267c71de8ec28",
  types: "Deposit,Withdrawal",
  statuses: "Completed,Failed",
  wallet_ids: "f47ac10b-58cc-4372-a567-0e02b2c3d479,1ddca562-8434-41c9-8809-d437bad9c868",
  chain_ids: "BTC,ETH",
  token_ids: "ETH_USDT,ETH_USDC",
  asset_ids: "USDT,USDC",
  vault_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  project_id: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  min_created_timestamp: 1635744000000,
  max_created_timestamp: 1635744000000,
  limit: 10,
  before: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGmk1",
  after: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk",
};
apiInstance.listTransactions(opts).then(
  (data) => {
    console.log("API called successfully. Returned data: " + data);
  },
  (error) => {
    console.error(error);
  },
);
```
</RequestExample>