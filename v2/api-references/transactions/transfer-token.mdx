---
openapi: post /transactions/transfer
---

<RequestExample>
```python Python
import cobo_waas2
from cobo_waas2.models.create_transfer_transaction201_response import CreateTransferTransaction201Response
from cobo_waas2.models.transfer_params import TransferParams
from cobo_waas2.models.transfer_source import TransferSource
from cobo_waas2.models.custodial_transfer_source import CustodialTransferSource
from cobo_waas2.models.transfer_destination import TransferDestination
from cobo_waas2.models.address_transfer_destination import AddressTransferDestination
from cobo_waas2.models.transaction_request_fee import TransactionRequestFee
from cobo_waas2.models.transaction_request_fixed_fee import TransactionRequestFixedFee
from cobo_waas2.rest import ApiException
from pprint import pprint

configuration = cobo_waas2.Configuration(
    api_private_key="<YOUR_PRIVATE_KEY>",
    host="https://api.dev.cobo.com/v2",
)

with cobo_waas2.ApiClient(configuration) as api_client:
    api_instance = cobo_waas2.TransactionsApi(api_client)
    
    transfer_params = TransferParams(
        request_id="f47ac10b-58cc-4372-a567-0e02b2c3d479",
        source=TransferSource(
            custodial_transfer_source=CustodialTransferSource(
                wallet_subtype="Asset",
                wallet_id="f47ac10b-58cc-4372-a567-0e02b2c3d479"
            )
        ),
        token_id="ETH_USDT",
        destination=TransferDestination(
            address_transfer_destination=AddressTransferDestination(
                destination_type="Address",
                address="0xRecipientAddressHere"
            )
        ),
        category_names=["Trading"],
        description="Transfer from wallet",
        fee=TransactionRequestFee(
            transaction_request_fixed_fee=TransactionRequestFixedFee(
                fee_type="Fixed",
                amount="0.01"
            )
        ),
        transaction_process_type="AutoProcess",
        auto_fuel="PassiveAutoFuel"
    )

    try:
        api_response = api_instance.create_transfer_transaction(transfer_params=transfer_params)
        print("The response of TransactionsApi->create_transfer_transaction:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling TransactionsApi->create_transfer_transaction: %s\n" % e)
```
```java Java
// Import classes:
import com.cobo.waas2.ApiClient;
import com.cobo.waas2.ApiException;
import com.cobo.waas2.Configuration;
import com.cobo.waas2.Env;
import com.cobo.waas2.api.TransactionsApi;
import com.cobo.waas2.model.*;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setEnv(Env.DEV);
    defaultClient.setPrivKey("<YOUR_PRIVATE_KEY>");
    
    TransactionsApi apiInstance = new TransactionsApi();

    TransferParams transferParams = new TransferParams();
    transferParams.setRequestId("f47ac10b-58cc-4372-a567-0e02b2c3d479");
    transferParams.setSource(
      new TransferSource().custodialTransferSource(
        new CustodialTransferSource()
          .walletSubtype(WalletSubtype.ASSET)
          .walletId("f47ac10b-58cc-4372-a567-0e02b2c3d479")
      )
    );
    transferParams.setTokenId("ETH_USDT");
    transferParams.setDestination(
      new TransferDestination().addressTransferDestination(
        new AddressTransferDestination()
          .destinationType("Address")
          .address("0xRecipientAddressHere")
      )
    );
    transferParams.setCategoryNames(Arrays.asList("Trading"));
    transferParams.setDescription("Transfer from wallet");
    transferParams.setFee(
      new TransactionRequestFee().transactionRequestFixedFee(
        new TransactionRequestFixedFee()
          .feeType("Fixed")
          .amount("0.01")
      )
    );
    transferParams.setTransactionProcessType(TransactionProcessType.AUTO_PROCESS);
    transferParams.setAutoFuel(AutoFuelType.PASSIVE_AUTO_FUEL);

    try {
      CreateTransferTransaction201Response result =
          apiInstance.createTransferTransaction(transferParams);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling TransactionsApi#createTransferTransaction");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```
```go Go
package main

import (
	"context"
	"fmt"
	coboWaas2 "github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2"
	"github.com/CoboGlobal/cobo-waas2-go-sdk/cobo_waas2/crypto"
	"os"
)

func main() {
	transferParams := *coboWaas2.NewTransferParams(
		"f47ac10b-58cc-4372-a567-0e02b2c3d479",
		coboWaas2.TransferSource{
			CustodialTransferSource: coboWaas2.NewCustodialTransferSource(
				coboWaas2.WalletSubtype("Asset"),
				"f47ac10b-58cc-4372-a567-0e02b2c3d479",
			),
		},
		"ETH_USDT",
		coboWaas2.TransferDestination{
			AddressTransferDestination: coboWaas2.NewAddressTransferDestination(
				coboWaas2.TransferDestinationType("Address"),
				"0xRecipientAddressHere",
			),
		},
	)

	transferParams.SetCategoryNames([]string{"Trading"})
	transferParams.SetDescription("Transfer from wallet")
	transferParams.SetFee(coboWaas2.TransactionRequestFee{
		TransactionRequestFixedFee: coboWaas2.NewTransactionRequestFixedFee("Fixed", "0.01"),
	})
	transferParams.SetTransactionProcessType("AutoProcess")
	transferParams.SetAutoFuel(coboWaas2.AutoFuelType("PassiveAutoFuel"))

	configuration := coboWaas2.NewConfiguration()
	apiClient := coboWaas2.NewAPIClient(configuration)
	ctx := context.Background()

	ctx = context.WithValue(ctx, coboWaas2.ContextEnv, coboWaas2.DevEnv)
	ctx = context.WithValue(ctx, coboWaas2.ContextPortalSigner, crypto.Ed25519Signer{
		Secret: "<YOUR_PRIVATE_KEY>",
	})

	resp, r, err := apiClient.TransactionsAPI.CreateTransferTransaction(ctx).
		TransferParams(transferParams).
		Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `TransactionsAPI.CreateTransferTransaction``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `CreateTransferTransaction`: CreateTransferTransaction201Response
	fmt.Fprintf(os.Stdout, "Response from `TransactionsAPI.CreateTransferTransaction`: %v\n", resp)
}
```
```javascript JavaScript
const CoboWaas2 = require("@cobo/cobo-waas2");

const apiClient = CoboWaas2.ApiClient.instance;
apiClient.setEnv(CoboWaas2.Env.DEV);
apiClient.setPrivateKey("<YOUR_PRIVATE_KEY>");

const apiInstance = new CoboWaas2.TransactionsApi();

const transferParams = new CoboWaas2.TransferParams();
transferParams.request_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";
transferParams.source = new CoboWaas2.TransferSource();
transferParams.source.custodial_transfer_source = new CoboWaas2.CustodialTransferSource();
transferParams.source.custodial_transfer_source.wallet_subtype = "Asset";
transferParams.source.custodial_transfer_source.wallet_id = "f47ac10b-58cc-4372-a567-0e02b2c3d479";

transferParams.token_id = "ETH_USDT";
transferParams.destination = new CoboWaas2.TransferDestination();
transferParams.destination.address_transfer_destination = new CoboWaas2.AddressTransferDestination();
transferParams.destination.address_transfer_destination.destination_type = "Address";
transferParams.destination.address_transfer_destination.address = "0xRecipientAddressHere";

transferParams.category_names = ["Trading"];
transferParams.description = "Transfer from wallet";

transferParams.fee = new CoboWaas2.TransactionRequestFee();
transferParams.fee.transaction_request_fixed_fee = new CoboWaas2.TransactionRequestFixedFee();
transferParams.fee.transaction_request_fixed_fee.fee_type = "Fixed";
transferParams.fee.transaction_request_fixed_fee.amount = "0.01";

transferParams.transaction_process_type = "AutoProcess";
transferParams.auto_fuel = "PassiveAutoFuel";

apiInstance.createTransferTransaction(transferParams)
    .then((data) => {
        console.log("API called successfully. Returned data: ", data);
    }, (error) => {
        console.error(error);
    });
```
</RequestExample>