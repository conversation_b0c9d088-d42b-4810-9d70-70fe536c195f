description: The request body to bind addresses to a broker user.
content:
  application/json:
    schema:
      type: object
      properties:
        addresses:
          type: array
          items:
            type: object
            required:
              - chain_id
              - address
            properties:
              address:
                type: string
                description: The wallet address.
                example: "******************************************"
              chain_id:
                type: string
                description: The chain ID, which is the unique identifier of a blockchain. You can retrieve the IDs of all the chains you can use by calling [List enabled chains](https://www.cobo.com/developers/v2/api-references/wallets/list-enabled-chains).
                example: 'ETH'