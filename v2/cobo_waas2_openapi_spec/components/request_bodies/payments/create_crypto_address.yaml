description: The request body to create a crypto address.
content:
  application/json:
    schema:
      type: object
      required:
        - token_id
        - address
      properties:
        token_id:
          type: string
          description: |
            The token ID that identifies the cryptocurrency and its corresponding blockchain.

            **Supported values**:
              - **USDC**: `ETH_USDC`, `ARBITRUM_USDC`, `SOL_USDC`, `BASE_USDC`, `MATIC_USDC`, `BSC_USDC`
              - **USDT**: `TRON_USDT`, `ETH_USDT`, `ARBITRUM_USDT`, `SOL_USDT`, `BASE_USDT`, `MATIC_USDT`, `BSC_USDT`
          example: "ETH_USDT"

        address:
          type: string
          description: |
            The actual blockchain address to be used for payouts or transfers.
            It must be a valid address on the blockchain associated with the specified `token_id`.
          example: "******************************************"

        label:
          type: string
          description: |
            An optional label or alias to help identify the address's purpose.
            For example: `'Main Payout Wallet'`, `'Cold Storage'`.
            This field is for internal reference only.
          example: "Main Payout Wallet"
