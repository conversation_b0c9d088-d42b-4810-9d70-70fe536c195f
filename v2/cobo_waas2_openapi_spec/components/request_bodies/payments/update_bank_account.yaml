description: The request body for updating an existing bank account.
content:
  application/json:
    schema:
      type: object
      required:
        - info
      properties:
        info:
          type: object
          additionalProperties: true
          description: >
            JSON-formatted bank account details. The object should include the following fields:
           
            - beneficiary_name: Name of the account holder
           
            - beneficiary_address: Address of the account holder
           
            - account_number: Bank account number
            
            - bank_name: Name of the bank
           
            - bank_address: Address of the bank
            
            - iban: (Optional) International Bank Account Number
            
            - swift_or_bic: SWIFT or BIC code of the bank
          example: {"beneficiary_name": "<PERSON>", "beneficiary_address": "123 Main St, Anytown, USA", "account_number": "****************", "bank_name": "ABC Bank", "bank_address": "456 Bank Ave, Cityville, USA", "swift_or_bic": "ABCDEFGH"}
