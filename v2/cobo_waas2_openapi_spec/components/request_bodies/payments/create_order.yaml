description: The request body to create a pay-in order.
content:
  application/json:
    schema:
      type: object
      required:
        - merchant_id
        - token_id
        - order_amount
        - fee_amount
        - psp_order_code
      properties:
        merchant_id:
          type: string
          description: The merchant ID.
          example: "1001"
        token_id:
          type: string
          description: >
            The ID of the cryptocurrency used for payment. Supported values: 
              - USDC: `ETH_USDC`, `ARBITRUM_USDC`, `SOL_USDC`, `BASE_USDC`, `MATIC_USDC`, `BSC_USDC`
              - USDT: `TRON_USDT`, `ETH_USDT`, `ARBITRUM_USDT`, `SOL_USDT`, `BASE_USDT`, `MATIC_USDT`, `BSC_USDT`
          example: "ETH_USDT"
        currency:
          type: string
          description: The fiat currency of the order.
          default: "USD"
          example: "USD"
        order_amount:
          type: string
          description: The base amount of the order in fiat currency, excluding the developer fee (specified in `fee_amount`). Values must be greater than `0` and contain two decimal places.
          example: "100.00"
        fee_amount:
          type: string
          description: The developer fee for the order in fiat currency. It is added to the base amount (`order_amount`) to determine the final charge. For example, if order_amount is "100.00" and fee_amount is "2.00", the customer will be charged "102.00" in total, with "100.00" being settled to the merchant and "2.00" settled to the developer. Values must be greater than 0 and contain two decimal places.
          example: "2.00"
        merchant_order_code:
          type: string
          description: A unique reference code assigned by the merchant to identify this order in their system.
          example: "M20240201001"
        psp_order_code:
          type: string
          description: A unique reference code assigned by the developer to identify this order in their system.
          example: "P20240201001"
        expired_in:
          type: integer
          description: |
            The pay-in order will expire after approximately a certain number of seconds:
            - The order status becomes final and cannot be changed
            - The `received_token_amount` field will no longer be updated
            - Funds received after expiration will be categorized as late payments and can only be settled from the developer balance.
            - A late payment will trigger a `transactionLate` webhook event.
          example: 1800
        use_dedicated_address:
          type: boolean
          description: |
            Indicates whether to allocate a dedicated address for this order. 
            If false, a shared address from the address pool will be used.
          example: false
