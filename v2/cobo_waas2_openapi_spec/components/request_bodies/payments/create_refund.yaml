description: The request body to create a refund order.
content:
  application/json:
    schema:
      type: object
      required:
        - request_id
        - payable_amount
        - to_address
        - token_id
        - refund_type
      properties:
        request_id:
          type: string
          description: The request ID that is used to track a refund request. The request ID is provided by you and must be unique.
          example: "123e4567-e89b-12d3-a456-426614174004"
        merchant_id:
          type: string
          description: The merchant ID.
          example: "M1001"
        payable_amount:
          type: string
          description: The amount to refund in cryptocurrency.
          example: "0.0025"
        to_address:
          type: string
          description: The address where the refunded cryptocurrency will be sent.
          example: "******************************************"
        token_id:
          type: string
          description: >
            The ID of the cryptocurrency used for refund. Supported values: 
              - USDC: `ETH_USDC`, `ARBITRUM_USDC`, `SOL_USDC`, `BASE_USDC`, `MATIC_USDC`, `BSC_USDC`
              - USDT: `TRON_USDT`, `ETH_USDT`, `ARBITRUM_USDT`, `SOL_USDT`, `BASE_USDT`, `MATIC_USDT`, `BSC_USDT`
          example: "ETH_USDT"
        refund_type:
          $ref: "../../schemas/payments/refund_type.yaml"
        order_id:
          type: string
          description: The ID of the original pay-in order associated with this refund. Use this to track refunds against specific payments.
          example: "R20250304-M1001-1001"
        charge_merchant_fee:
            type: boolean
            description: >
                Indicates whether the merchant should bear the transaction fee for the refund. 
                If true, the fee will be deducted from merchant's account balance.
            example: false
        merchant_fee_amount:
            type: string
            description: >
                The amount of the transaction fee that the merchant will bear for the refund. 
                This is only applicable if `charge_merchant_fee` is set to true.
            example: "0.0001"
        merchant_fee_token_id:
            type: string
            description: >
                The ID of the cryptocurrency used for the transaction fee. 
                This is only applicable if `charge_merchant_fee` is set to true.
            example: "ETH_USDT"
