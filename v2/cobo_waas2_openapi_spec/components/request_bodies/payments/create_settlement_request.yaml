description: The request body to create a settlement request.
content:
  application/json:
    schema:
      type: object
      required:
        - request_id
        - settlements
      properties:
        request_id:
          type: string
          description: The request ID that is used to track a settlement request. The request ID is provided by you and must be unique.
          example: "SETTLEMENT123"
        payout_channel:
          $ref: "../../schemas/payments/payout_channel.yaml"
        settlement_type:
          $ref: "../../schemas/payments/settlement_type.yaml"
        settlements:
          type: array
          items:
            $ref: "../../schemas/payments/create_settlement.yaml"
