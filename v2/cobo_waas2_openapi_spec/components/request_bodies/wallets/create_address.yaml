description: The request body to generates addresses within a specified wallet.
content:
  application/json:
    schema:
      type: object
      required:
        - chain_id
        - count
      properties:
        chain_id:
          type: string
          description: The chain ID, which is the unique identifier of a blockchain. You can retrieve the IDs of all the chains you can use by calling [List enabled chains](https://www.cobo.com/developers/v2/api-references/wallets/list-enabled-chains).
          example: 'ETH'
        count:
          type: integer
          maximum: 50
          minimum: 1
          default: 1
          description: The number of addresses to create. This property will be ignored if you are generating tweaked Taproot addresses.
          example: 1
        taproot_script_tree_hashes:
          type: array
          items:
            type: string
            example: '0x138fdd0f6c3803d45553e730c25924baf7be741b8a72a4e6fdbd9d44cb19f85b'
          description: A list of script tree hashes used to generate a tweaked Taproot address. This property is required only if you want to generate tweaked Taproot addresses.
        taproot_internal_address:
          type: string
          description: The original Taproot address to be tweaked. This property is required only if you want to generate tweaked Taproot addresses.
          example: "**********************************"
        encoding:
          $ref: '../../schemas/wallets/addresses/address_encoding.yaml'