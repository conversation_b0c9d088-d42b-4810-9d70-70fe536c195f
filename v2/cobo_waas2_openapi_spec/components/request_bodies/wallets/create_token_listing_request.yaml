description: |
  Request body for submitting a token listing request.
  <note>
    wallet_type only supports `Custodial` and `MPC`.
    wallet_subtype only supports `Asset`, `Web3`, and `Org-Controlled`.
  </note>
required: true
content:
  application/json:
    schema:
      type: object
      required:
        - wallet_type
        - wallet_subtype
        - chain_id
        - contract_address
      properties:
        wallet_type:
          $ref: "../../schemas/wallets/bases/wallet_type.yaml"
        wallet_subtype:
          $ref: "../../schemas/wallets/bases/wallet_subtype.yaml"
        chain_id:
          type: string
          description: ID of the blockchain where the token exists
          example: "ETH"
        contract_address:
          type: string
          description: Contract address of the token
          example: "******************************************"