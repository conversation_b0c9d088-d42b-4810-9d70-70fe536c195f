description: The request body of the batch check UTXOs operation.
content:
  application/json:
    schema:
      type: object
      required:
        - token_id
        - utxos
      properties:
        token_id:
          type: string
          description: The token ID, which is the unique identifier of a token. You can retrieve the IDs of all the tokens you can use by calling [List enabled tokens](https://www.cobo.com/developers/v2/api-references/wallets/list-enabled-tokens).
          example: 'BTC'
        utxos:
          type: array
          items:
            $ref: '../../schemas/wallets/batch_utxo_param.yaml'

