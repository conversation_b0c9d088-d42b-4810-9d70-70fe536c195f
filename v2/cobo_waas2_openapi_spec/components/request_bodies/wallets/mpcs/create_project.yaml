description: The request body to create a project.
content:
  application/json:
    schema:
      type: object
      required:
        - name
        - participants
        - threshold
      properties:
        name:
          type: string
          example: "Project name"
          description: The project name.
        participants:
          type: integer
          description: | 
            The number of key share holders in the project.
            
            **Notes:**
            1. Currently, the available [Threshold Signature Schemes (TSS)](https://manuals.cobo.com/en/portal/mpc-wallets/introduction#threshold-signature-scheme-tss) are 2-2, 2-3, and 3-3 schemes (in the "threshold - participants" format), so you can only set `participants` to 2 or 3. 
            
            2. `threshold` must be less than or equal to `participants`.
          example: 3
        threshold:
          type: integer
          description: |
            The number of key share holders required to sign an operation in the project.
            
            **Notes:**
            1. Currently, the available [Threshold Signature Schemes (TSS)](https://manuals.cobo.com/en/portal/mpc-wallets/introduction#threshold-signature-scheme-tss) are 2-2, 2-3, and 3-3 schemes (in the "threshold - participants" format), so you can only set `threshold` to 2 or 3. 
            
            2. `threshold` must be less than or equal to `participants`.
          example: 2


