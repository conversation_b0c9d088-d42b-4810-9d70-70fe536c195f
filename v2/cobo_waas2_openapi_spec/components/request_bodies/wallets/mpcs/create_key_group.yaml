description: The request body to create a key share holder group.
content:
  application/json:
    schema:
      type: object
      required:
        - key_share_holder_group_type
        - participants
        - threshold
        - key_share_holders

      properties:
        key_share_holder_group_type:
          $ref: '../../../schemas/wallets/mpcs/key_group_type.yaml'
        participants:
          type: integer
          description: |
            The number of key share holders in this key share holder group.
            
            **Notes:**
            1. Currently, the available [Threshold Signature Schemes (TSS)](https://manuals.cobo.com/en/portal/mpc-wallets/introduction#threshold-signature-scheme-tss) are 2-2, 2-3, and 3-3 schemes (in the "threshold - participants" format), so you can only set `participants` to 2 or 3. 

            2. `threshold` must be less than or equal to `participants`.
          example: 3
        threshold:
          type: integer
          description: |
            The number of key share holders required to sign an operation.

            **Notes:**
            1. Currently, the available [Threshold Signature Schemes (TSS)](https://manuals.cobo.com/en/portal/mpc-wallets/introduction#threshold-signature-scheme-tss) are 2-2, 2-3, and 3-3 schemes (in the "threshold - participants" format), so you can only set `threshold` to 2 or 3. 

            2. `threshold` must be less than or equal to `participants`.
          example: 2
        key_share_holders:
          type: array
          items:
            $ref: '../../../schemas/wallets/mpcs/create_key_holder.yaml'

