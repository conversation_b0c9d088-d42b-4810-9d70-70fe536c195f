description: The request body to create a vault.
content:
  application/json:
    schema:
      type: object
      required:
        - name
        - vault_type
      properties:
        project_id:
          type: string
          example: "0111039d-27fb-49ba-b172-6e0aa80e37ec"
          description: |
            The project ID, which you can retrieve by calling [List all projects](https://www.cobo.com/developers/v2/api-references/wallets--mpc-wallets/list-all-projects).
            
            **Notes:**
            1. If you set `vault_type` to `OrgControlled`, the value of `project_id` will be ignored.
            2. If you set `vault_type` to `UserControlled`, then `project_id` is required.

        name:
          type: string
          example: "My vault"
          description: The vault name.
        vault_type:
          $ref: '../../../schemas/wallets/mpcs/mpc_vault_type.yaml'


