description: The request body to create a TSS request.
content:
  application/json:
    schema:
      type: object
      required:
        - type
        - target_key_share_holder_group_id
      properties:
        type:
          $ref: '../../../schemas/wallets/mpcs/tss_request_type.yaml'
        target_key_share_holder_group_id:
          type: string
          description: The target key share holder group ID.
          example: "a1bf161f-8b60-4f61-9c35-6434b8654437"
        source_key_share_holder_group:
          $ref: '../../../schemas/wallets/mpcs/source_key_group.yaml'
        description:
          type: string
          description: The description of the TSS request.
          example: 'This is a request to create key shares using the Recovery Group for a key share holder in the Main Group if their key share has been lost (e.g. by losing their phone).'


