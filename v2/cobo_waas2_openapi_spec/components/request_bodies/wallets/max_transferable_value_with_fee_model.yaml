description: The request body to get max transferable value within a specified wallet.
content:
  application/json:
    schema:
      type: object
      required:
        - token_id
        - fee
        - to_address
      properties:
        token_id:
          type: string
          description: The token ID of the transferred token. You can retrieve the IDs of all the tokens you can use by calling [List enabled tokens](https://www.cobo.com/developers/v2/api-references/wallets/list-enabled-tokens). For transfers from Exchange Wallets, this property value represents the asset ID.
          example: 'ETH_USDT'
        fee:
          $ref: '../../schemas/transactions/fees/transaction_fee.yaml'
        to_address:
          type: string
          description: The recipient's address.
          example: '2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE'
        from_address:
          type: string
          description: The sender's address. For EVM addresses in MPC Wallets, this parameter is required.
          example: '2N2xFZtbCFB6Nb3Pj9Sxsx5mX2fxX3yEgkE'