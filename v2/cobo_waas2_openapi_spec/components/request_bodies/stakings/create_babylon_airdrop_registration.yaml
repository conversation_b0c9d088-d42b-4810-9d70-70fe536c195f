description: The request body to register for the Babylon airdrop.
content:
  application/json:
    schema:
      type: object
      properties:
        btc_address:
          $ref: '../../schemas/stakings/source/staking_source.yaml'
          description: The Bitcoin (BTC) address that was used for staking.
        babylon_address:
          $ref: '../../schemas/stakings/source/staking_source.yaml'
          description: The Babylon address used to receive the airdrop.