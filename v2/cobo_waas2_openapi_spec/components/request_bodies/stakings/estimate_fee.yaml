description: The request body to get the estimated fee of a staking activity.
content:
  application/json:
    schema:
      oneOf:
        - $ref: '../../schemas/stakings/estimate_stake_fee.yaml'
        - $ref: '../../schemas/stakings/estimate_unstake_fee.yaml'
        - $ref: '../../schemas/stakings/estimate_withdraw_fee.yaml'
        - $ref: '../../schemas/stakings/estimate_claim_fee.yaml'
      discriminator:
        propertyName: activity_type
        mapping:
          Stake: '#/components/schemas/EstimateStakeFee'
          Unstake: '#/components/schemas/EstimateUnstakeFee'
          Withdraw: '#/components/schemas/EstimateWithdrawFee'
          Claim: '#/components/schemas/EstimateClaimFee'


