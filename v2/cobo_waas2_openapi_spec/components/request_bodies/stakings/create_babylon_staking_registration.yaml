description: The request body to transit Babylon BTC staking to phase 2
content:
  application/json:
    schema:
      type: object
      properties:
        staking_id:
          description: The ID of the Phase-1 BTC staking position.
          type: string
          example: '3f2840ce-44eb-450b-aa81-d3f84b772efb'
        babylon_address:
          $ref: '../../schemas/stakings/source/staking_source.yaml'
          description: The Babylon address used for receiving the BABY rewards.
