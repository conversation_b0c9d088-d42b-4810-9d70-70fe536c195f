description: The request body to update a webhook endpoint.
content:
  application/json:
    schema:
      type: object
      properties:
        subscribed_events:
          description: The new event types you want to subscribe to for this webhook endpoint. You can call [Get webhook event types](https://www.cobo.com/developers/v2/api-references/developers--webhooks/get-webhook-event-types) to retrieve all available event types.
          items:
            $ref: '../../schemas/webhooks/event_type.yaml'
          type: array
        status:
          type: string
          enum:
            - STATUS_INACTIVE
          example: STATUS_INACTIVE
          description: >-
            The new status you want to set the webhook endpoint to. If you set `status` to `STATUS_INACTIVE`, the endpoint will be revoked, meaning it will no longer receive any webhook events.
        description:
          description: The webhook endpoint description.
          type: string
          example: 'My webhook endpoint'
