description: >-
  The request body used to trigger a test webhook event. 
content:
  application/json:
    schema:
      type: object
      required:
        - event_type
      properties:
        event_type:
          $ref: '../../schemas/webhooks/event_type.yaml'
        override_data:
          type: object
          description: |
            An object for customization of the webhook event payload. You only need to include the fields you want to customize. 
            
            The provided fields must match the webhook event data structure, depending on the specified event type. For a complete introduction of the webhook event data structure, refer to the `data.data` property in the response of [List all webhook events](https://www.cobo.com/developers/v2/api-references/developers--webhooks/list-all-webhook-events).

            If this property is not provided, a default payload will be returned.
          example:
            {
              "chain_id": "ETH",
              "transaction_id": "Test-transaction-id"
            }

