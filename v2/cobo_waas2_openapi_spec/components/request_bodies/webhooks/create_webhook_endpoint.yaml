description: The request body to register a webhook endpoint.
content:
  application/json:
    schema:
      type: object
      required:
        - subscribed_events
        - url
      properties:
        url:
          description: The webhook endpoint URL.
          type: string
          format: url
          example: 'https://example.com/webhook'
        subscribed_events:
          description: >
            The event types you want to subscribe to for this webhook endpoint. You can call [Get webhook event types](https://www.cobo.com/developers/v2/api-references/developers--webhooks/get-webhook-event-types) to retrieve all available event types.
          items:
            $ref: '../../schemas/webhooks/event_type.yaml'
          type: array
        description:
          description: The description of the webhook endpoint.
          type: string
          example: 'My webhook endpoint'
