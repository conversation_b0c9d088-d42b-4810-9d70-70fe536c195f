description: The request body for creating a swap activity.
required: true
content:
  application/json:
    schema:
      type: object
      required:
        - wallet_id
        - quote_id
      properties:
        wallet_id:
          type: string
          description: The unique identifier of the wallet to pay.
          example: "123e4567-e89b-12d3-a456-************"
        address:
          type: string
          description: The wallet address.
          example: "**********************************"
        quote_id:
          type: string
          format: uuid
          description: The unique identifier of the quote.
          example: "123e4567-e89b-12d3-a456-************"
        app_initiator:
          type: string
          example: '<EMAIL>'
          description: The initiator of the app activity. If you do not specify this property, the WaaS service will automatically designate the API key as the initiator.
        request_id:
          type: string
          description: The request id of the swap activity.
          example: "123e4567-e89b-12d3-a456-************"
        destination:
          $ref: '../../schemas/transactions/transfers/destinations/address_transfer_destination.yaml'
          description: The destination of the swap activity. Only required when the swap type is 'Bridge' and the source wallet is not a custodial wallet.
