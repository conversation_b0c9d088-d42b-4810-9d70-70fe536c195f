description: The request body for creating a swap activity.
required: true
content:
  application/json:
    schema:
      type: object
      required:
        - wallet_id
        - pay_token_id
        - receive_token_id
      properties:
        wallet_id:
          type: string
          format: uuid
          description: The unique identifier of the wallet.
          example: "123e4567-e89b-12d3-a456-************"
        pay_token_id:
          type: string
          description: Unique id of the token to pay.
          example: "BTC"
        receive_token_id:
          type: string
          description: Unique id of the token to receive.
          example: "ETH_WBTC"
        pay_amount:
          type: string
          description: |
            Amount of tokens to pay. For example "0.5 BTC".
            Note: Either pay_amount or receive_amount must be provided, but not both.
          example: "100"
        receive_amount:
          type: string
          description: |
            Amount of tokens to receive. For example "0.5 ETH_WBTC".
            Note: Either pay_amount or receive_amount must be provided, but not both.
          example: "100"