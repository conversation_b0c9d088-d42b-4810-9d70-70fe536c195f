description: The request body for refreshing an Org Access Token.
required: true
content:
  application/json:
    schema:
      type: object
      properties:
        client_id:
          type: string
          description: The client ID, a unique identifier to distinguish Cobo Portal Apps. You can get the client ID by retrieving the manifest file after publishing the app.
          example: AnCEPEp5Q8qjAOA1Lb6kVd2OlkCyJnMTeMPdLbPOM8cz176Eb5y7EJoUjJJ0vkzz
        grant_type:
          type: string
          description: The OAuth grant type. Set the value as `refresh_token`.
          example: 'refresh_token'
        refresh_token:
          type: string
          description: The Refresh Token of the current Org Access Token.
          example: rK49jI0zt49gsttzscscik15Asmlpu1TdcxqguJJS8B9f6ilJEC0y3PbVqwsEAw5