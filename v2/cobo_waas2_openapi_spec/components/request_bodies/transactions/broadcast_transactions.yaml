description: The request body to broadcast a list of signed transactions.
content:
  application/json:
    schema:
      type: object
      properties:
        transaction_ids:
          type: array
          items:
            type: string
            example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479'
          description: The transaction IDs of the signed transactions to be broadcast. You can retrieve the transactions corresponding to a staking activity by calling [Get staking activity details](https://www.cobo.com/developers/v2/api-references/stakings/get-staking-activity-details).
