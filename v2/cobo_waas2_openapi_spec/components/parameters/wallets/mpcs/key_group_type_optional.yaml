name: key_share_holder_group_type
in: query
description: |
    The key share holder group type. Possible values include:
    - `MainGroup`: The [Main Group](https://manuals.cobo.com/en/portal/mpc-wallets/ocw/create-key-share-groups#main-group).
  
    - `SigningGroup`: The [Signing Group](https://manuals.cobo.com/en/portal/mpc-wallets/ocw/create-key-share-groups#signing-group).
  
    - `RecoveryGroup`: The [Recovery Group](https://manuals.cobo.com/en/portal/mpc-wallets/ocw/create-key-share-groups#recovery-group).
  
    **Note**: If this parameter is left empty, all key share holder group types will be retrieved.
    

required: false
schema:
  $ref: "../../../schemas/wallets/mpcs/key_group_type.yaml"
