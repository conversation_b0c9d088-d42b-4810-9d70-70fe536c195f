name: chain_ids
in: query
required: false
description: A list of chain IDs, separated by comma. The chain ID is the unique identifier of a blockchain. You can retrieve the IDs of all the chains you can use by calling [List enabled chains](https://www.cobo.com/developers/v2/api-references/wallets/list-enabled-chains).
schema:
  type: string
  description: A list of chain IDs, separated by comma.
example: 'BTC,ETH'
