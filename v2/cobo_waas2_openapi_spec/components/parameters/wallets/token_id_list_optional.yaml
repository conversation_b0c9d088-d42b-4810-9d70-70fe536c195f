name: token_ids
in: query
required: false
description: A list of token IDs, separated by comma. The token ID is the unique identifier of a token. You can retrieve the IDs of all the tokens you can use by calling [List enabled tokens](https://www.cobo.com/developers/v2/api-references/wallets/list-enabled-tokens).
schema:
  type: string
  description: A list of token IDs, separated by comma.
example: 'ETH_USDT,ETH_USDC'
