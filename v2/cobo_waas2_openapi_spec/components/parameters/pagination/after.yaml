name: after
in: query
description: |
  This parameter specifies an object ID as a starting point for pagination, retrieving data after the specified object relative to the current dataset.  

  Suppose the current data is ordered as Object A, Object B, and Object C. If you set `after` to the ID of Object A (`RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk`), the response will include Object B and Object C.  

  **Notes**:  
  - If you set both `after` and `before`, an error will occur.
  - If you leave both `before` and `after` empty, the first page of data is returned.
required: false
schema:
  type: string
example: "RqeEoTkgKG5rpzqYzg2Hd3szmPoj2cE7w5jWwShz3C1vyGSAk"
