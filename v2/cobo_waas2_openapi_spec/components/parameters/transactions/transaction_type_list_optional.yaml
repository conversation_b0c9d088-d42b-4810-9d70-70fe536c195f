name: types
in: query
required: false
description: |
  A list of transaction types, separated by comma. Possible values include: 
    - `Deposit`: A deposit transaction.
    - `Withdrawal`: A withdrawal transaction.
    - `ContractCall`: A transaction that interacts with a smart contract.
    - `MessageSign`: A transaction that signs a message. 
    - `ExternalSafeTx`: A transaction to a Smart Contract Wallet (Safe{Wallet}) that requires one or multiple signatures to be executed.
    - `Stake`: A transaction that creates a staking request.
    - `Unstake`: A transaction that creates a unstaking request.
schema:
  type: string
example: 'Deposit,Withdrawal'
