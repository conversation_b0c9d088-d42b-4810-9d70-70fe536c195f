name: destination_types
in: query
required: false
description: | 
  A list of transaction destination types, separated by comma. Possible values include:
    - `Address`: An external address. 
    - `ContractCall`: A transaction that interacts with a smart contract.
    - `MessageSign`: A transaction that signs a message. 
    - `CustodialWallet`: A Custodial Wallet.
    - `MPCWallet`: An MPC Wallet.
    - `SafeWallet`: A Smart Contract Wallets (Safe{Wallet}).
    - `ExchangeWallet`: An Exchange Wallet.
schema:
  type: string
example: 'ContractCall,MessageSign'
