name: statuses
in: query
required: false
description: |
  A list of transaction statuses, separated by comma. Possible values include: 
    - `Submitted`: The transaction is submitted.
    - `PendingScreening`: The transaction is pending screening by Risk Control. 
    - `PendingAuthorization`: The transaction is pending approvals.
    - `PendingSignature`: The transaction is pending signature. 
    - `Broadcasting`: The transaction is being broadcast.
    - `Confirming`: The transaction is waiting for the required number of confirmations.
    - `Completed`: The transaction is completed.
    - `Failed`: The transaction failed.
    - `Rejected`: The transaction is rejected.
    - `Pending`: The transaction is waiting to be included in the next block of the blockchain.
schema:
  type: string
example: 'Completed,Failed'
