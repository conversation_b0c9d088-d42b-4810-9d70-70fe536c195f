name: source_types
in: query
required: false
description: | 
  A list of transaction source types, separated by comma. Possible values include:
    - `Asset`: Custodial Wallets (Asset Wallets).
    - `Org-Controlled`: MPC Wallets (Organization-Controlled Wallets).
    - `User-Controlled`: MPC Wallets (User-Controlled Wallets).
    - `Safe{Wallet}`: Smart Contract Wallets (Safe{Wallet}).
    - `Main`: Exchange Wallets (Main Account).
    - `Sub`: Exchange Wallets (Sub Account).
    - `DepositFromAddress`: An address which can include both Cobo's wallet addresses and external addresses.
    - `DepositFromWallet`: An Exchange Wallet.
    - `DepositFromLoop`: A transfer sender through the [Cobo Loop](https://manuals.cobo.com/en/portal/custodial-wallets/cobo-loop) network.
schema:
  type: string
example: 'Asset,DepositFromAddress'
