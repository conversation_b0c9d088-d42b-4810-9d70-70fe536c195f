name: status
in: query
description: |
    The status of Babylon airdrop or Phase-2 registration. Possible values are:
    - `Registered`: Registered for Babylon airdrop or Phase-2.
    - `Unregistered`: Not registered for any Babylon airdrop or Phase-2.
    - `Registering`: The Babylon airdrop or Phase-2 registration is in progress but not yet completed.
required: false
schema:
  type: string
  enum: [Registered, Unregistered, Registering] 
  example: "Registered"