name: status
in: query
description: The registration request status.
required: false
schema:
  type: string
  enum: [Processing, Completed, Failed] 
  example: "Processing"
  description: |
    The status of a Babylon airdrop or Babylon Phase-2 registration request. Possible values include:
    - `Processing`: The registration request is being processed.
    - `Completed`: The registration has been completed.
    - `Failed`: The registration failed.