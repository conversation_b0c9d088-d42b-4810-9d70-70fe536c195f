description: The information about challenge for security key.
content:
  application/json:
    schema:
      type: object
      properties:
        challenge:
          type: string
          example: "MjJhZTdhOWMtZmU1Yy00MjY5LTkzNWItYjcyMWE4NjA5MTY0"
          description: The challenge for the security key.
        timeout:
          type: integer
          example: 60000
          description: The timeout for the security key challenge.
        rpId:
          type: string
          example: "cobo.com"
          description: The relying party ID for the security key.
        allowCredentials:
          type: array
          example:
            - id: "c2VjdXJpdHktY3JlZGVudGlhbC1pZA=="
              type: "public-key"
              transports: ["usb", "nfc", "ble"]
          items:
            type: object
            properties:
              id:
                type: string
                description: The ID of the credential.
              type:
                type: string
                enum: ['public-key']
                description: The type of the credential.
              transports:
                type: array
                items:
                  type: string
                  description: The transport methods supported by the credential.
        userVerification:
            type: string
            enum: ['required', 'preferred', 'discouraged']
            example: "discouraged"
            description: The user verification requirement for the security key.
