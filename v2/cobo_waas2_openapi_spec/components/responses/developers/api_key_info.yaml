description: The API key information.
content:
  application/json:
    schema:
      type: object
      required:
        - name
        - curve_type
        - key
        - created_timestamp
        - updated_timestamp
      properties:
        name:
          type: string
          description: The API key name.
          example: "my_api_key"
        curve_type:
          type: string
          description: |
            The curve type used for the API key, which determines the cryptographic algorithm for key generation and signing. Possible values include:
            - `ED25519`: Ed25519
            - `SECP256K1`: Secp256k1
          enum:
            - ED25519
            - SECP256K1
          example: ED25519
        key:
          type: string
          description: The API key value.
          example: "427b06814cca3359bd0e710c1187833b7f052748a3fdf59888fad4ddc4bd379f"
        callback_url:
          type: string
          description: The URL of the callback endpoint that receives callback messages triggered by this API key.
          example: "https://example.com/api/callback"
        valid_ips:
          type: array
          description: (Applicable to permanent API keys only) The list of IP addresses that are allowed to use this API key.
          items:
            type: string
            example: "127.0.0.1"
        created_timestamp:
          type: integer
          format: int64
          description: The time when the API key was registered, in Unix timestamp format, measured in milliseconds.
          example: 1701396866000
        updated_timestamp:
          type: integer
          format: int64
          description: The time when the API key information was last updated, in Unix timestamp format, measured in milliseconds.
          example: 1701396866000
        expired_timestamp:
          type: integer
          format: int64
          description: The time when the API key expires, in Unix timestamp format, measured in milliseconds. For permanent API keys, this property value is `null`.
          example: 1701396866000
        role_scopes:
          type: array
          description: The list of user roles and wallet scopes associated with the API key.
          items:
            $ref: '../../schemas/developers/role_scopes.yaml'

