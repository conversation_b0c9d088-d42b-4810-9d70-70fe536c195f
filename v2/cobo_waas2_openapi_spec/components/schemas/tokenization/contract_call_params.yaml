type: object
description: The information about the contract call.
required:
  - calldata
properties:
  source:
    $ref: './token_operation_source.yaml'
  data:
    oneOf:
      - $ref: './evm_contract_call_params.yaml'
      - $ref: './sol_contract_call_params.yaml'
    discriminator:
      propertyName: type
      mapping:
        EVM_Contract: '#/components/schemas/TokenizationEvmContractCallParams' 
        SOL_Contract: '#/components/schemas/TokenizationSolContractCallParams'