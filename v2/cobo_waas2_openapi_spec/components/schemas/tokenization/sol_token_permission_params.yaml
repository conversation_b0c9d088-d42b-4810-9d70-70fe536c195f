type: object
description: "Role-based permission settings for token contract. If not provided, all permissions will be granted to the issuance wallet by default."
properties:
  permanent_delegate:
    type: string
    description: "Wallet address"
    example: "******************************************"
  minter:
    type: array
    description: "List of addresses for the minter role."
    items:
      type: string
      description: "Wallet address"
      example: "******************************************"
    example: 
      - '******************************************'
      - '0x8ba1f109551bD432803012645Hac136c34B8f7f7'
  freezer:
    type: array
    description: "List of addresses for the burner role."
    items:
      type: string
      description: "Wallet address"
      example: "0x8ba1f109551bD432803012645Hac136c34B8f7f7"
    example: 
      - '0x8ba1f109551bD432803012645Hac136c34B8f7f7'
      - '0x9cb2f210662eE543904023756Ibd247d45C9g8g8'
  updater:
    type: array
    description: "List of addresses for the upgrader role."
    items:
      type: string
      description: "Wallet address"
      example: "******************************************"
    example: 
      - '******************************************'
      - '0x8ba1f109551bD432803012645Hac136c34B8f7f7'
  pauser:
    type: array
    description: "List of addresses for the pauser role."
    items:
      type: string
      description: "Wallet address"
      example: "******************************************"
    example: 
      - '******************************************'
      - '0x8ba1f109551bD432803012645Hac136c34B8f7f7'