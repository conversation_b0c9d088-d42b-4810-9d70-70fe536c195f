type: object
properties:
  standard:
    $ref: './sol_token_standard.yaml'
  name:
    type: string
    description: "The name of the token."
    example: 'My Awesome Token'
  symbol:
    type: string
    description: "The symbol of the token."
    example: 'MAT'
  decimals:
    type: integer
    format: int32
    description: "The number of decimals for the token (0-18)."
    minimum: 0
    maximum: 18
    example: 18
  allowlist_activated:
    type: boolean
    description: "Whether the allowlist feature is activated for the token. When activated, only addresses in the allowlist can perform token operations."
    default: false
    example: false
  permissions:
    $ref: './token_permission_params.yaml'
required:
  - standard
  - name
  - symbol
  - decimals 