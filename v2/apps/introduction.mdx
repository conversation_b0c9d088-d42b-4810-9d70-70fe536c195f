---
title: "Introduction to Cobo Portal App development"
lang: "en"
description: "Explore Cobo Portal App development with WaaS 2.0 API, Cobo CLI, and Cobo UI toolkit."
--- 

Cobo Portal Apps are extensions of [Cobo Portal](https://manuals.cobo.com/en/portal/introduction). They can exist as iframe elements within Cobo Portal or as bots in the form of APIs and webhooks. By leveraging tools including the [Cobo Wallet-as-a-Service (WaaS) 2.0 API](/v2/guides/overview/introduction), developers like you can build Cobo Portal Apps to significantly expand the capabilities of Cobo Portal, such as staking, trading, automating finance operations, and more. 

User can easily explore Cobo Portal Apps by visiting [Cobo Portal](https://portal.cobo.com/apps). To learn more about publicly available Cobo Portal Apps, see [Introduction to Cobo Portal Apps](https://manuals.cobo.com/en/portal/portal-apps/introduction).


<img src="/v2/images/apps/all_apps.png" className="screenshot_full_screen" alt="cobo portal app homepage"/>

## Tools you can leverage

Cobo offers the following tools that allow you to develop a Cobo Portal App with ease.

### WaaS 2.0 API

The [WaaS 2.0 API](/v2/api-references/playground) is the latest version of Cobo's WaaS API offering. It enables a Cobo Portal App to interact with users' wallets, including:

- Querying information, such as wallet specifics, balances, and transaction history.
- Managing wallets, such as transferring tokens and calling smart contracts.
- Subscribing to webhook events to receive notifications.

The API uses [Cobo OAuth](/v2/apps/authentication) to authorize a Cobo Portal App to access other organizations' data and perform operations on the organizations' wallets.

### Cobo CLI

Cobo CLI is a developer tool that allows you to build, test, and publish Cobo Portal Apps directly from the command line. With Cobo CLI, you can easily handle common tasks such as creating, publishing, and updating an app.

### Cobo UI toolkit

The Cobo UI toolkit is a library of pre-built components that adhere to Cobo Portal's design and visual standards. You can leverage the toolkit to quickly build a user interface for your Cobo Portal Apps.

## Related links

- Cobo offers a Hello World sample project that showcases how to quickly build a Cobo Portal App. Refer to [Get started with Cobo Portal Apps](/v2/apps/get-started) for details.
- For a step-by-step guide on how to build a Cobo Portal App from scratch, refer to [Build an Cobo Portal App](/v2/apps/build-app).
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>