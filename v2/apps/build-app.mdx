---
title: "Build a Cobo Portal App"
lang: "en"
description: "Build a Cobo Portal App from scratch. Follow detailed steps for development, authentication, and publishing using Cobo CLI and WaaS API."
---

This article walks you through the detailed steps required to build a Cobo Portal App from scratch.

For an overview of developing Cobo Portal Apps, refer to [Introduction to Cobo Portal Apps Development](/v2/apps/introduction).

## Sample project

Cobo offers a sample project Hello World that allows you to quickly build a Cobo Portal App and preview it on your local machine. Refer to [Create a sample app](/v2/apps/get-started) for details.

## Prerequisites

- To build and manage a Cobo Portal App, you need to first create an organization in Cobo Portal in the [development environment](https://portal.dev.cobo.com/). Follow the instructions in [Set up your account and organization](https://manuals.cobo.com/en/portal/quick-start-guide) to set up your Cobo account and create your organization. If an organization has already been set up, ask your organization admin to invite you to join the organization.
- Choose and install either FastAPI or Next.js as your development framework.

## Develop the app

### Create an app using Cobo CLI

Cobo Command Line Interface (CLI) is a powerful developer tool designed to help you build, test, and manage your integration with Cobo Wallet-as-a-Service (WaaS) 2.0 directly from the command line.

1. Install Cobo CLI using the following command:

    ```shell
    # Install Cobo CLI using pip (Python 3.9 or newer is required).
    pip install cobo-cli
    # Alternatively, you can install Cobo CLI using Homebrew.
    brew install cobo-cli
    # Test if the installation is successful
    cobo version
    ```

    For more details about the system requirements for Cobo CLI, please refer to [Install Cobo CLI](/v2/developer-tools/cobo-cli/quick-start-guide#system-requirements).

2. Log in to Cobo Portal and connect Cobo CLI to your Cobo Portal account. You can choose one of the [three authentication methods](/v2/developer-tools/cobo-cli/login-and-authentication) to authenticate. As an example, this step will show you how to authenticate with your Cobo Portal account:

   ```shell
   # Use your Cobo Portal account to authenticate.
   cobo auth user
   # Initiate the user login process.
   cobo login
   ```

   You'll be prompted to open a browser to continue the authorization process. Complete the authorization process in your browser, granting the necessary permissions for Cobo CLI to access your crypto wallets. For more details about authentication methods, see [Login and authentication](/v2/developer-tools/cobo-cli/login-and-authentication).

3. Use the [app init](/v2/developer-tools/cobo-cli/app-init) command to create a Hello World app project. You can specify the details of the WaaS application you want to create through options, or choose these parameters step by step and create the project using the prompts of Cobo CLI. 

   The following command creates a Cobo Portal App project, using Org Access Tokens for authentication, and creates it in the directory named `hello_world`:

   ```shell
   cobo app init --app-type portal --auth org --directory hello_world
   ```

### Acquire authentication information

To authenticate your API requests to the WaaS service in Cobo Portal Apps, you need to provide your app key and Org Access Token. You also need to calculate an API signature if you are not using the [WaaS SDK](/v2/developer-tools/quickstart-python). Refer to [Cobo OAuth](https://www.cobo.com/developers/v2/apps/authentication) for how to acquire all the necessary information for authentication.

### Verify app users

User Info Tokens are JSON Web Tokens (JWTs) signed by Cobo Portal. These tokens allow Cobo Portal Apps to retrieve information about users, including their user ID, email address, and permissions. A Cobo Portal App can use the information to verify a user's identity and control their access to the organization's resources based on the permissions. It is recommended that you verify a user's identity before they can get started using the app. For more information, refer to [User Info Tokens](/v2/apps/verify-users).

### Call the WaaS API

You can now call the WaaS 2.0 API to build features for your app. When calling an API operation, you need to provide the required authentication information in the request header. 

The following code snippets show how to list all the wallets in an organization using the [WaaS Python SDK](/v2/developer-tools/quickstart-python).

```python
from cobo_waas2 import Configuration, ApiClient, WalletsApi

configuration = Configuration(
    # Use `https://api.dev.cobo.com/v2` for the development environment, or `https://api.cobo.com/v2` for the production environment.
    host="https://api.dev.cobo.com/v2",
    # Replace `<APP_SECRET>` with your app secret.
    api_private_key=<APP_SECRET>,
)
client = ApiClient(configuration=configuration)
# Replace `{ORG_ACCESS_TOKEN}` with the Org Access Token.
client.set_default_header("AUTHORIZATION", f"Bearer {ORG_ACCESS_TOKEN}")
api_instance = WalletsApi(client)
wallets = api_instance.list_wallets(wallet_type=WalletType.CUSTODIAL).data
```

For details about each API operation, refer to [the API reference](/v2/api-references).

### Configure the manifest file

Before you publish the app, you need to fill out the manifest file, which contains the app's configuration information. Refer to the following table for field descriptions and examples.

<table class="table-three-cols">
    <thead>
        <tr>
            <th>Field</th>
            <th>Description</th>
            <th>Example</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>app_name</td>
            <td>The app name.</td>
            <td><code>SuperLoop</code></td>
        </tr>
        <tr>
            <td>app_desc</td>
            <td>A brief description of the app, with a maximum length of 80 characters.</td>
            <td><code>SuperLoop is an off-exchange settlement network that allows institutions to trade on exchanges without worrying about counterparty risks.</code></td>
        </tr>
        <tr>
            <td>app_desc_long</td>
            <td>A detailed description of the app, including use cases and key features. <p>The description must be under 1000 characters and formatted in Markdown.</p></td>
            <td><code>SuperLoop is an off-exchange settlement network that allows institutions to trade on exchanges without worrying about counterparty risks. For asset managers, trading on exchanges can be conducted with greater confidence and efficiency. The need to pre-fund on exchanges before trading is eliminated, minimizing counterparty risks.</code></td>
        </tr>
        <tr>
            <td>app_icon_url</td>
            <td>The URL of the app icon. <p>Please contact our Operation team at <a href="mailto:<EMAIL>"><EMAIL></a> to help you upload the app icon.</p></td>
            <td><code>https://d.cobo.com/public/logos/Logo.png</code></td>
        </tr>
        <tr>
            <td>screen_shots</td>
            <td>The URLs of at least three screenshots showcasing key features. <p>Please contact our Operation team at <a href="mailto:<EMAIL>"><EMAIL></a> to help you upload the screenshots.</p></td>
            <td><code>"https://d.cobo.com/apk/android/SuperLoop.png", "https://d.cobo.com/apk/android/Loop.png", "https://d.cobo.com/apk/android/MirrorModal.png"</code></td>
        </tr>
        <tr>
            <td>required_permissions</td>
            <td>A list of the permissions required for the app to access resources within users' organizations. <p>A permission consists of a resource type and an operation, separated by a comma. For example, <code>wallet:create</code> represents the permission to create a wallet.</p>For the full list of permissions in WaaS 2.0, refer to <a href="https://www.cobo.com/developers/v2/guides/overview/permission-list">Permissions list</a>.</td>
            <td><code>"wallet:create", "transaction:withdraw", "mpc_project:update"</code></td>
        </tr>
        <tr>
            <td>optional_permissions</td>
            <td>(Optional) A list of permissions that the app can request to access additional resources within users' organizations. <p>These are not mandatory but may enhance app functionality. A permission consists of a resource type and an operation, separated by a comma. For example, <code>wallet:create</code> represents the permission to create a wallet.</p>For the full list of permissions in WaaS 2.0, refer to <a href="https://www.cobo.com/developers/v2/guides/overview/permission-list">Permissions list</a>.</td>
            <td><code>"address_book:create", "kyc_profile:read", "team_member:freeze"</code></td>
        </tr>
        <tr>
            <td>homepage_url</td>
            <td>The host URL of the app homepage, used to embed the app into Cobo Portal via an iframe.</td>
            <td><code>https://example.com</code></td>
        </tr>
        <tr>
            <td>callback_urls</td>
            <td>The URL of the endpoint used to receive the callback after the organization admin approves the app's request for permissions.</td>
            <td><code>https://example.com/webhooks/organization/authorization</code></td>
        </tr>
        <tr>
            <td>client_key</td>
            <td>The app key. <p>For instructions on how to generate an app key, refer to <a href="https://www.cobo.com/developers/v2/apps/authentication#generate-an-app-key-and-an-app-secret">Generate an app key and an app secret</a>.</p></td>
            <td><code>AbCdEfGhIjKlMnOpQrStUvWxYz1234567890</code></td>
        </tr>
        <tr>
            <td>creator_name</td>
            <td>The developer's name.</td>
            <td><code>Cobo</code></td>
        </tr>
        <tr>
            <td>contact_email</td>
            <td>The developer's contact email.</td>
            <td><code><EMAIL></code></td>
        </tr>
        <tr>
            <td>operation_approval_rules</td>
            <td>(Optional) The app's default approval workflows. To use this feature, the `required_permissions` field must be contain `app_governance_policy:update` and `app_governance_policy:read`. For how to configure approval workflows, refer to <a href="https://www.cobo.com/developers/v2/apps/configure-workflow">Configure approval workflows</a>.</td>
            <td><code>[\{"operation_id": "25cbb115-82a1-4474-9993-538bb439ad33", "operation_name": "Make Batch Payout", "default_approval_policies": [\{"action": \{"type": "Quorum", "content": \{"roles": ["admin"], "threshold": 1, "type": "PART_APPROVAL"}}, "conditions": [\{"field": "amount", "value_type": "INT", "value": "100", "operator": ">"}]}, \{"action": \{"type": "Accept", "content": ""}, "conditions": null}], "descriptions": [\{"field": "amount", "description": "Total USD amount", "value_type": "INT", "display_name": "Total Amount(USD)"}]}]</code></td>
        </tr>
        <tr>
            <td>wallet_type</td>
            <td>(Optional) The wallet types supported by the app. The possible values are as follows: <ul><li><code>Custodial</code></li><li><code>MPC</code></li><li><code>SmartContract</code></li><li><code>Exchange</code></li><li><code>User-Controlled</code></li><li><code>Org-Controlled</code></li><li><code>Asset</code></li><li><code>Safe{Wallet}</code></li><li><code>Main</code></li></ul> For example, if this field is set to <code>Custodial, MPC</code>, users will only be able to select these wallet types as the wallet scope that the app can access. <p>If this field is left empty, users can select any wallet type available.</p>For the full list of wallet scopes in WaaS 2.0, refer to <a href="https://www.cobo.com/developers/v2/guides/overview/permissions-and-scopes#wallet-scopes">Wallet scopes</a>.</td>
            <td><code>"Custodial", "MPC"</code></td>
        </tr>
        <tr>
            <td>is_policy_reminded</td>
            <td>(Optional) Whether app users will be reminded of setting up transaction policies for the app. <p>The default value is <code>true</code>.</p></td>
            <td><code>true</code></td>
        </tr>
    </tbody>
</table>

Refer to the following screenshots for where these fields will display on the app UI.

**The Apps page**
<img src="/v2/images/apps/app_list.png" className="screenshot_full_screen" alt="The Apps page" />

**The Overview tab of the App Details page**
<img src="/v2/images/apps/app_overview.png" className="screenshot_full_screen" alt="The Overview tab of the App Details page" />

**The Permissions tab of the App Details page**
<img src="/v2/images/apps/app_permissions.png" className="screenshot_full_screen" alt="The Permissions tab of the App Details page" />

## Publish the app

When you have completed developing your app, you can use the [app upload](/v2/developer-tools/cobo-cli/app-upload) Cobo CLI command to publish the app to the production environment or development environment:

```shell
cobo app upload
```

<Info>For more information about how to switch between development and production environments, refer to [Environment management](/v2/developer-tools/cobo-cli/environment-management).</Info>

Before publishing your Cobo Portal Apps, ensure the following:
- Verify the [app manifest file](/v2/apps/build-app#configure-the-manifest-file) is configured correctly.
- Log into Cobo CLI with your Cobo Portal account.
- Generate an app key if you haven't done so. For more details, refer to [Generate keys](/v2/developer-tools/cobo-cli/key-management#generate-keys).
- Run the command from the project directory where the manifest file is located.


If you publish your app to the production environment, the command will automatically trigger a workflow that requires approval from Cobo. The approval result will be sent as an email to the `contact_email` configured in the manifest file. Once approved, your app will be available to all users on Cobo Portal. 

If you publish your app to the development environment, no approval is required and your app will be available only to the users in your organization.

The `app upload` command will generate a client ID for your app in the manifest file. A client ID is a unique identifier for a Cobo Portal App and cannot be modified after being generated. It is required for getting the Org Access Token. For more details, refer to [Org Access Tokens](/v2/apps/org-access-tokens). 

An app can be published only once, otherwise an error will occur. For subsequent updates, you need to follow the process of [updating the app](#update-the-app).

## Update the app

If you want to update your app after it's published, you can implement the changes and then use the [app update](/v2/developer-tools/cobo-cli/app-update) Cobo CLI command:

```shell
cobo app update
```

Before updating your Cobo Portal Apps, ensure the following:
- Verify the [app manifest file](/v2/apps/build-app#configure-the-manifest-file) is configured correctly.
- Log into Cobo CLI with your Cobo Portal account.
- Run the command from the project directory where the manifest file is located.

Similar to publishing an app, if you update an app in the production environment, the update command will automatically trigger a workflow that requires approval from Cobo. Otherwise, if you update an app in the development environment, no approval is required.
<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>