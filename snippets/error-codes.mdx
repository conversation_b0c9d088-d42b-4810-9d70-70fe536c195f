This article explains the common error codes and HTTP status codes you may encounter when using the Cobo WaaS 2.0 API and how to resolve the errors. 

### Error codes

| Error code      | Description                                                                                         | Solution                                                                                                                      |
|---------------------|---------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|
| 1000                | Internal Server Error. This error can be caused by several issues including expired [Org Access Tokens](/v2/apps/org-access-tokens). | Check your server configuration settings, including whether your Org Access Token has expired, and try again later.   |
| 2002                | The HTTP method used in the request is not supported.                                                   | Use a supported HTTP method.                                                                                  |
| 1003, 2003                | One or more required parameters are missing in the request.                                                         | Provide all required parameters.                                                                                                  |
| 1006, 2006                | One or more parameters are in an invalid format or contain unsupported values.                          | Provide valid parameters in the expected format.                                                                                  |
| 12002          | The specified token is not supported by Cobo.                                                     | Choose a supported token. Call the [List supported tokens](/v2/api-references/wallets/list-supported-tokens) operation to get the full list of supported tokens.          |
| 12007, 30012        | Insufficient balance to perform the requested operation.                                                 | Ensure the source address has sufficient balance to cover the transferred amount.                                                 |
| 12009          | Duplicate request ID.                                       | Use a unique request ID.                                     |
| 2000                | Internal error occurred during processing.                                                              | Please try again later.                                                                                                           |
| 2010                | Rate limit exceeded. Too many requests in a short time.                                                  | Please try again later.                                                                                                           |
| 2021                | The request handler is missing or not implemented.                                                      | Provide a valid handler for the request.                                                                                          |
| 2022                | The request is missing required headers.                                                                | Include all required headers.                                                                                                     |
| 2023                | Missing or invalid API signature.                                                                                      | Verify your API signature and ensure it is correct. You can refer to [Calculate the API signature](/v2/guides/overview/cobo-auth#calculate-the-api-signature) for details.                                                                                   |
| 2024                | API key authentication failed.                                                                          | Use a valid API key. If your API key is permanent, ensure the request is sent from a whitelisted IP address. See [Register an API key](https://manuals.cobo.com/en/portal/developer-console/create-api-key) for details. |
| 2025, 4001                | Forbidden access to the requested resource.                                                             | Check the permissions associated with your API key. You can refer to [Permissions and wallet scopes](/v2/guides/overview/permissions-and-scopes) for details.                                                                               |
| 2026                | Too many requests.                                                                                      | Please try again later.                                                                                                           |
| 2028                | The requested resource was not found.                                                                   | Check the request URL.                                                                                                            |
| 2029                | The provided status property is invalid.                                                                | Provide a valid value for the status property.                                                                                    |
| 2040                | A resource with the same key already exists.                                                            | Use a unique key.                                                                                                                 |
| 2050                | No available pricing plan, or usage limit has been exceeded.                                            | Purchase a pricing plan or upgrade your existing one. For more information, see [Introduction to Bills & Payments](https://manuals.cobo.com/en/portal/bills-and-payments/introduction). |
| 2051           | The current pricing plan has expired.                                                             | Renew your pricing plan to continue using the service. For more information, see [Introduction to Bills & Payments](https://manuals.cobo.com/en/portal/bills-and-payments/introduction).                                              |
| 30001          | The current pricing plan does not support this feature.                                           | Upgrade to a pricing plan that includes this feature.  For more information, see [Introduction to Bills & Payments](https://manuals.cobo.com/en/portal/bills-and-payments/introduction).                   |
| 30007               | Invalid amount. The value is not a valid number or does not meet the required format or range.          | Provide a valid amount that meets the expected format and range.                                                                 |
| 30008               | Invalid absolute amount. The absolute value of the amount is either too small, too large, or zero when a non-zero value is required. | Ensure the absolute value of the amount meets the required conditions.                                                           |
| 30010               | The provided amount is below the dust threshold. It is too small to be processed or transferred.         | Increase the amount to exceed the dust threshold.                                     |
| 30011               | The provided amount is below the minimum deposit threshold.                      | Increase the deposit amount to meet the minimum threshold.                |
| 30013               | Insufficient balance to cover the required transaction fee.                             | Ensure the source address has enough balance to cover transaction fees.                                       |
| 30014               | The destination address is invalid.                                                    | Provide a valid destination address.                                                                                |
| 30023               | (This error only applies to Exchange Wallets) Invalid trading account type.                                                                           |  Provide a valid trading account type.                                                                      |
| 60010          | The specified token has not been enabled for this organization.                                   | Enable the token for your organization.                    |



### HTTP status codes

| Status code | Description        | Solution                                    |
| --------------- | ---------------------- | ----------------------------------------------- |
| 200             | OK.                    | N/A              |
| 400             | Bad request.           | Check the request parameters.                   |
| 401             | Unauthorized.          | Check the API key, API signature, or timestamp. |
| 403             | Forbidden.             | Ensure you have the required permissions.       |
| 404             | Not Found.             | Check the requestURL.                           |
| 405             | Method Not Allowed.    | Use a supported HTTP method.                    |
| 406             | Not Acceptable.        | Ensure the request content format is JSON.      |
| 429             | Too Many Requests.     | Reduce request frequency and try again later.   |
| 500             | Internal Server Error. This error can be caused by several issues including expired [Org Access Tokens](/v2/apps/org-access-tokens). | Check your server configuration settings, including whether your Org Access Token has expired, and try again later.                              |
| 502             | Bad Gateway.           | Check the connectivity and try again later.     |
| 503             | Service Unavailable.   | Try again later.                                |

<Tip>Feel free to [share your feedback](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI) to improve our documentation!</Tip>