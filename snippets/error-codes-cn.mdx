<Note>本文档由 AI 辅助翻译。如需确认内容准确性，请参考[英文官方文档](https://www.cobo.com/developers/v2/guides/overview/introduction)。</Note>

本文介绍了在使用 Cobo WaaS 2.0 API 时可能遇到的常见错误码和 HTTP 状态码，以及如何解决这些错误。

### 错误码

| 错误码      | 描述                                                                                         | 解决方案                                                                                                                      |
|---------------------|---------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|
| 1000                | 内部服务器错误。此错误可能由多个问题引起，包括[Org Access Tokens](/v2/apps/org-access-tokens)过期。 | 检查您的服务器配置设置，包括 Org Access Tokens 是否已过期，然后稍后重试。   |
| 2002                | 请求中使用的 HTTP 方法不受支持。                                                   | 使用支持的 HTTP 方法。                                                                                  |
| 1003, 2003                | 请求中缺少一个或多个必填参数。                                                         | 提供所有必填参数。                                                                                                  |
| 1006, 2006                | 一个或多个参数格式无效或包含不支持的值。                          | 以预期格式提供有效参数。                                                                                  |
| 12002          | Cobo 不支持指定的代币。                                                     | 选择支持的代币。调用 [List supported tokens](/v2/api-references/wallets/list-supported-tokens) 接口获取完整的支持代币列表。          |
| 12007, 30012        | 余额不足，无法执行请求的操作。                                                 | 确保源地址有足够的余额支付转账金额。                                                 |
| 12009          | 重复的请求 ID。                                       | 使用唯一的请求 ID。                                     |
| 2000                | 处理过程中发生内部错误。                                                              | 请稍后重试。                                                                                                           |
| 2010                | 超出速率限制。短时间内请求过多。                                                  | 请稍后重试。                                                                                                           |
| 2021                | 请求处理程序缺失或未实现。                                                      | 为请求提供有效的处理程序。                                                                                          |
| 2022                | 请求缺少必填的请求头。                                                                | 包含所有必填的请求头。                                                                                                     |
| 2023                | API 签名缺失或无效。                                                                                      | 验证您的 API 签名并确保其正确。您可以参考[计算 API 签名](/v2/guides/overview/cobo-auth#calculate-the-api-signature)获取详细信息。                                                                                   |
| 2024                | API Key 认证失败。                                                                          | 使用有效的 API Key。如果您的 API Key 是永久的，请确保请求来自白名单 IP 地址。详情请参见[注册 API 密钥](https://manuals.cobo.com/cn/portal/developer-console/create-api-key)。 |
| 2025, 4001                | 禁止访问请求的资源。                                                             | 检查与您的 API Key 关联的权限。您可以参考[权限和钱包范围](/v2/guides/overview/permissions-and-scopes)获取详细信息。                                                                               |
| 2026                | 请求过多。                                                                                      | 请稍后重试。                                                                                                           |
| 2028                | 未找到请求的资源。                                                                   | 检查请求 URL。                                                                                                            |
| 2029                | 提供的状态属性无效。                                                                | 为状态属性提供有效值。                                                                                    |
| 2040                | 具有相同密钥的资源已存在。                                                            | 使用唯一的密钥。                                                                                                                 |
| 2050                | 无可用的套餐，或已超出使用限制。                                            | 购买套餐或升级现有套餐。更多信息，请参见[账单和付款介绍](https://manuals.cobo.com/cn/portal/bills-and-payments/introduction)。 |
| 2051           | 当前套餐已过期。                                                             | 续订您的套餐以继续使用服务。更多信息，请参见[账单和付款介绍](https://manuals.cobo.com/cn/portal/bills-and-payments/introduction)。                                              |
| 30001          | 当前套餐不支持此功能。                                           | 升级到包含此功能的套餐。更多信息，请参见[账单和付款介绍](https://manuals.cobo.com/cn/portal/bills-and-payments/introduction)。                   |
| 30007               | 金额无效。该值不是有效数字或不符合所需格式或范围。          | 提供符合预期格式和范围的有效金额。                                                                 |
| 30008               | 绝对金额无效。金额的绝对值太小、太大，或在需要非零值时为零。 | 确保金额的绝对值满足所需条件。                                                           |
| 30010               | 提供的金额低于尘埃阈值。金额太小，无法处理或转账。         | 增加金额以超过尘埃阈值。                                     |
| 30011               | 提供的金额低于最低充币阈值。                      | 增加充币金额以满足最低阈值。                |
| 30013               | 余额不足以支付所需的交易费用。                             | 确保源地址有足够的余额支付交易费用。                                       |
| 30014               | 目标地址无效。                                                    | 提供有效的目标地址。                                                                                |
| 30023               | （此错误仅适用于交易所钱包）交易账户类型无效。                                                                           | 提供有效的交易账户类型。                                                                      |
| 60010          | 指定的代币尚未为此团队启用。                                   | 为您的团队启用该代币。                    |



### HTTP 状态码

| 状态码 | 描述        | 解决方案                                    |
| --------------- | ---------------------- | ----------------------------------------------- |
| 200             | 成功。                    | 不适用              |
| 400             | 错误请求。           | 检查请求参数。                   |
| 401             | 未经授权。          | 检查 API Key、API 签名或时间戳。 |
| 403             | 禁止访问。             | 确保您具有所需权限。       |
| 404             | 未找到。             | 检查请求 URL。                           |
| 405             | 方法不允许。    | 使用支持的 HTTP 方法。                    |
| 406             | 不可接受。        | 确保请求内容格式为 JSON。      |
| 429             | 请求过多。     | 降低请求频率并稍后重试。   |
| 500             | 内部服务器错误。此错误可能由多个问题引起，包括 [Org Access Tokens](/v2/apps/org-access-tokens) 过期。 | 检查您的服务器配置设置，包括 Org Access Tokens 是否已过期，然后稍后重试。                              |
| 502             | 错误网关。           | 检查连接并稍后重试。     |
| 503             | 服务不可用。   | 稍后重试。                                |

<Tip>欢迎您[提交反馈](https://forms.zohopublic.com/cobo/form/DocumentFeedbackForm/formperma/QvLOhxJv1_JMsJ-1dleZ8Itb_7rzN-LtgvsDdxosoVI)来帮助改进我们的文档！</Tip>