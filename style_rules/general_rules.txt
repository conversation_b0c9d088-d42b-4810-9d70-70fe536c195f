### General Rules


#### **Spelling**
Use American spelling consistently across the document.


#### **Grammar**
Use 2nd person perspective. Replace "my" with "your." For example:
- Correct: "View your balance."
- Incorrect: "View my balance."


#### **Plurals**
Use plural nouns when possible, except for specific cases (e.g., Safe{Wallet}). For example:
- Correct: "Manage Custodial Wallets."
- Incorrect: "Manage Custodial Wallet."


#### **Contractions**
Avoid contractions. If a word contains an apostrophe (`'`) and it is not part of a proper noun or acronym, treat it as a contraction and mark it incorrect. Replace with full forms. For example:
- Correct: "It is ready."
- Incorrect: "It's ready."


#### **Capitalization**
Use sentence case for topic titles, except for module names. Capitalize wallet names. For example:
- Correct: "Create an MPC Wallet."
- Incorrect: "Creating an MPC Wallet."


#### **Punctuation**
End each step in instructions with a full stop. For example:
- Correct: "Click Exchange Wallet."
- Incorrect: "Click Exchange Wallet"


#### **Formatting**
Use backticks (`) for code and parameters. Use **bold** for emphasis in warnings or notes. For example:
- **Warning:** This action is irreversible.
- Enter `John Smith` in the field.


#### **Date & Time**
Follow the formats:
- YYYY-MM-DD
- YYYY-MM-DD at HH:mm:ss


#### **Linking**
Use relative URLs. Cross-reference manuals and modules with specific terms. For example:
- Correct: "See **Cobo Guard user manual**."
- Correct: Use `/v2/guides/overview/introduction.`


#### **Computer Interface**
Bold UI elements. Avoid redundant phrases. For example:
- Correct: Click **Next**.
- Incorrect: Click on the "Next" button.


#### **Accessibility**
1. Avoid sensory-based descriptions. For example:
  - Correct: "The tables are displayed in the Table List section."
  - Incorrect: "The tables are in the lower-right corner."


2. Add the alt text attribute to the images tag. For example:
  - Correct: `<img src="/en/images/portal-apps/batch-payouts/make_payout.png" className="screenshot_full_screen" alt="Screenshot of the Make a Payout page" />`
  - Incorrect: `<img src="/en/images/portal-apps/batch-payouts/make_payout.png" className="screenshot_full_screen"  />`
